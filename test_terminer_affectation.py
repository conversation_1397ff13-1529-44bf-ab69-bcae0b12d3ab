#!/usr/bin/env python3
"""
Test de la fonctionnalité de fin d'affectation
"""

import sqlite3
from datetime import date

def test_terminer_affectation():
    """Test de la nouvelle fonctionnalité de fin d'affectation"""
    print("🧪 Test de la fonctionnalité de fin d'affectation")
    print("=" * 50)
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect('parc_automobile.db')
        conn.row_factory = sqlite3.Row
        
        # Vérifier les nouvelles colonnes
        print("📋 Vérification des nouvelles colonnes...")
        cursor = conn.execute("PRAGMA table_info(affectations)")
        columns = [row[1] for row in cursor.fetchall()]
        
        nouvelles_colonnes = [
            'motif_fin', 'commentaire_fin', 'kilometrage_debut', 
            'kilometrage_fin', 'budget_carburant', 'carburant_consomme', 
            'cout_carburant', 'etat_vehicule_fin'
        ]
        
        for colonne in nouvelles_colonnes:
            if colonne in columns:
                print(f"✅ Colonne '{colonne}' présente")
            else:
                print(f"❌ Colonne '{colonne}' manquante")
        
        # Vérifier les affectations actives
        print("\n📊 Affectations actives:")
        affectations = conn.execute('''
            SELECT a.id, a.statut, v.immatriculation, c.prenom, c.nom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.statut = 'active'
        ''').fetchall()
        
        if affectations:
            for aff in affectations:
                print(f"  - ID {aff['id']}: {aff['immatriculation']} → {aff['prenom']} {aff['nom']}")
        else:
            print("  Aucune affectation active trouvée")
        
        # Test de simulation d'une fin d'affectation
        if affectations:
            print(f"\n🔧 Simulation de fin d'affectation pour ID {affectations[0]['id']}...")
            
            # Simuler les données de fin
            test_data = {
                'date_fin': date.today().isoformat(),
                'motif_fin': 'test_automatique',
                'commentaire_fin': 'Test de la nouvelle fonctionnalité',
                'kilometrage_fin': 50000,
                'carburant_consomme': 45.5,
                'cout_carburant': 650.75,
                'etat_vehicule_fin': 'bon'
            }
            
            print("  Données de test:")
            for key, value in test_data.items():
                print(f"    {key}: {value}")
            
            print("✅ Structure de données validée")
        
        conn.close()
        print("\n✅ Test terminé avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_terminer_affectation()
