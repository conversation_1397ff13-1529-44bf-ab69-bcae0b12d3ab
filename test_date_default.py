#!/usr/bin/env python3
import sqlite3
from datetime import datetime

def get_datetime_fin_default(affectation):
    """Calculer la date/heure de fin par défaut pour une affectation"""
    # Utiliser la date_fin existante si elle existe, sinon la date/heure actuelle
    if affectation and affectation['date_fin']:
        try:
            # Si la date_fin est au format ISO (avec T), on la garde telle quelle
            if 'T' in affectation['date_fin']:
                return affectation['date_fin'][:16]  # Garder YYYY-MM-DDTHH:MM
            else:
                # Si c'est juste une date, ajouter l'heure actuelle
                return f"{affectation['date_fin']}T{datetime.now().strftime('%H:%M')}"
        except:
            # En cas d'erreur, utiliser la date/heure actuelle
            pass
    
    # Pas de date_fin existante ou erreur, utiliser la date/heure actuelle
    return datetime.now().strftime('%Y-%m-%dT%H:%M')

# Connexion à la base de données
conn = sqlite3.connect('parc_automobile.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

print("🔍 Test de génération de date par défaut")
print("=" * 50)

# Récupérer l'affectation active
cursor.execute('''
    SELECT a.*, v.immatriculation, v.marque, v.modele, v.kilometrage, c.nom, c.prenom
    FROM affectations a
    JOIN vehicules v ON a.vehicule_id = v.id
    JOIN conducteurs c ON a.conducteur_id = c.id
    WHERE a.id = 0
''')

affectation = cursor.fetchone()

if affectation:
    print(f"📋 Affectation trouvée (ID: {affectation['id']})")
    print(f"   Véhicule: {affectation['immatriculation']}")
    print(f"   Conducteur: {affectation['prenom']} {affectation['nom']}")
    print(f"   Date début: {affectation['date_debut']}")
    print(f"   Date fin actuelle: {affectation['date_fin']}")
    
    # Tester la génération de date par défaut
    datetime_fin_default = get_datetime_fin_default(affectation)
    print(f"   Date fin générée: '{datetime_fin_default}'")
    print(f"   Longueur: {len(datetime_fin_default) if datetime_fin_default else 0}")
    print(f"   Type: {type(datetime_fin_default)}")
    
    # Vérifier le format
    if datetime_fin_default:
        import re
        pattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$'
        is_valid = re.match(pattern, datetime_fin_default) is not None
        print(f"   Format valide: {is_valid}")
        
        if not is_valid:
            print(f"   ❌ Format invalide! Attendu: YYYY-MM-DDTHH:MM")
        else:
            print(f"   ✅ Format correct")
    else:
        print(f"   ❌ Valeur vide ou None!")
        
else:
    print("❌ Affectation non trouvée")

conn.close()
print("\n🎉 Test terminé !")