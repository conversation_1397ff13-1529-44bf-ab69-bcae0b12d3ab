{% extends "base.html" %}

{% block title %}Gestion des Véhicules - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-car"></i> Gestion des Véhicules</h1>
            <a href="{{ url_for('ajouter_vehicule') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Ajouter un véhicule
            </a>
        </div>
    </div>
</div>

<!-- Filtres et recherche -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" class="form-control" id="searchVehicules" placeholder="Rechercher par immatriculation, marque, modèle...">
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="filterStatut">
            <option value="">Tous les statuts</option>
            <option value="disponible">Disponible</option>
            <option value="affecte">Affecté</option>
            <option value="en_maintenance">En maintenance</option>
            <option value="hors_service">Hors service</option>
        </select>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="filterCarburant">
            <option value="">Tous les carburants</option>
            <option value="Essence">Essence</option>
            <option value="Diesel">Diesel</option>
            <option value="Hybride">Hybride</option>
            <option value="Électrique">Électrique</option>
        </select>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4>{{ stats.total or 0 }}</h4>
                <p class="mb-0">Total véhicules</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4>{{ stats.disponible or 0 }}</h4>
                <p class="mb-0">Disponibles</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4>{{ stats.affecte or 0 }}</h4>
                <p class="mb-0">Affectés</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <h4>{{ stats.en_maintenance or 0 }}</h4>
                <p class="mb-0">En maintenance</p>
            </div>
        </div>
    </div>
</div>

<!-- Tableau des véhicules -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> Liste des véhicules
                <span class="badge bg-secondary ms-2">{{ vehicules|length }} véhicule(s)</span>
            </h5>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download"></i> Exporter
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('export_vehicules', format='csv') }}">
                        <i class="fas fa-file-csv"></i> CSV
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('export_vehicules', format='xlsx') }}">
                        <i class="fas fa-file-excel"></i> Excel (XLSX)
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('export_vehicules', format='xls') }}">
                        <i class="fas fa-file-excel"></i> Excel (XLS)
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if vehicules %}
        <div class="table-responsive">
            <table class="table table-hover" id="tableVehicules">
                <thead>
                    <tr>
                        <th>Immatriculation</th>
                        <th>Marque</th>
                        <th>Modèle</th>
                        <th>Année</th>
                        <th>Carburant</th>
                        <th>Kilométrage</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for vehicule in vehicules %}
                    <tr>
                        <td>
                            <strong>{{ vehicule.immatriculation }}</strong>
                            {% if vehicule.couleur %}
                            <br><small class="text-muted">{{ vehicule.couleur }}</small>
                            {% endif %}
                        </td>
                        <td>{{ vehicule.marque }}</td>
                        <td>{{ vehicule.modele }}</td>
                        <td>
                            {{ vehicule.annee }}
                            <br><small class="text-muted">{{ 2025 - vehicule.annee }} ans</small>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ vehicule.carburant }}</span>
                        </td>
                        <td>
                            {{ "{:,}".format(vehicule.kilometrage).replace(',', ' ') }} km
                        </td>
                        <td>
                            {% if vehicule.statut == 'disponible' %}
                                <span class="badge bg-success">Disponible</span>
                            {% elif vehicule.statut == 'affecte' %}
                                <span class="badge bg-info">Affecté</span>
                            {% elif vehicule.statut == 'en_maintenance' %}
                                <span class="badge bg-warning text-dark">En maintenance</span>
                            {% elif vehicule.statut == 'hors_service' %}
                                <span class="badge bg-danger">Hors service</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ vehicule.statut }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('voir_vehicule', id=vehicule.id) }}" 
                                   class="btn btn-outline-primary" title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('modifier_vehicule', id=vehicule.id) }}" 
                                   class="btn btn-outline-warning" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('supprimer_vehicule', id=vehicule.id) }}" 
                                   class="btn btn-outline-danger btn-delete" 
                                   data-item-name="{{ vehicule.immatriculation }}"
                                   title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-car fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun véhicule enregistré</h5>
            <p class="text-muted">Commencez par ajouter votre premier véhicule au parc automobile.</p>
            <a href="{{ url_for('ajouter_vehicule') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Ajouter un véhicule
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filtrage en temps réel
    const searchInput = document.getElementById('searchVehicules');
    const filterStatut = document.getElementById('filterStatut');
    const filterCarburant = document.getElementById('filterCarburant');
    
    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statutFilter = filterStatut.value;
        const carburantFilter = filterCarburant.value;
        const rows = document.querySelectorAll('#tableVehicules tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const statut = row.querySelector('.badge').textContent.toLowerCase();
            const carburant = row.cells[4].textContent.toLowerCase();
            
            const matchSearch = text.includes(searchTerm);
            const matchStatut = !statutFilter || statut.includes(statutFilter.toLowerCase());
            const matchCarburant = !carburantFilter || carburant.includes(carburantFilter.toLowerCase());
            
            row.style.display = matchSearch && matchStatut && matchCarburant ? '' : 'none';
        });
    }
    
    searchInput.addEventListener('keyup', filterTable);
    filterStatut.addEventListener('change', filterTable);
    filterCarburant.addEventListener('change', filterTable);
});
</script>
{% endblock %}
