{% extends "base.html" %}

{% block title %}Guide Immatriculation Marocaine - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-id-card"></i> Guide des Immatriculations Marocaines</h1>
            <a href="{{ url_for('ajouter_vehicule') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Ajouter un véhicule
            </a>
        </div>
    </div>
</div>

<!-- Formats Standard -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-car"></i> Formats Standard
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><i class="fas fa-star"></i> Format Officiel Actuel</h6>
                    <div class="alert alert-success">
                        <strong>Structure :</strong> <code>N(N)(N)(N)(N) ل NN</code><br>
                        <strong>Exemples :</strong>
                        <span class="badge bg-success">12345 ل 1</span>
                        <span class="badge bg-success">123 ل 1</span>
                        <span class="badge bg-success">1 ل 1</span><br>
                        <small>1 à 5 chiffres + lettre arabe + code territorial</small>
                    </div>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-language"></i> Format avec Lettre Latine</h6>
                    <div class="alert alert-info">
                        <strong>Structure :</strong> <code>N(N)(N)(N)(N) L NN</code><br>
                        <strong>Exemples :</strong>
                        <span class="badge bg-info">12345 A 1</span>
                        <span class="badge bg-info">123 A 1</span>
                        <span class="badge bg-info">1 A 1</span><br>
                        <small>1 à 5 chiffres + lettre latine + code territorial</small>
                    </div>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-keyboard"></i> Format Saisie (avec tirets)</h6>
                    <div class="alert alert-warning">
                        <strong>Structure :</strong> <code>N(N)(N)(N)(N)-L-NN</code><br>
                        <strong>Exemples :</strong>
                        <span class="badge bg-warning text-dark">12345-A-1</span>
                        <span class="badge bg-warning text-dark">123-A-1</span>
                        <span class="badge bg-warning text-dark">1-A-1</span><br>
                        <small>1 à 5 chiffres + lettre + code territorial (saisie)</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-flag"></i> Formats Spéciaux
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><i class="fas fa-university"></i> Véhicules Diplomatiques</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-danger">CD 1234</span> Corps Diplomatique</li>
                        <li><span class="badge bg-danger">CC 1234</span> Consulaire</li>
                        <li><span class="badge bg-danger">CMD 1234</span> Chef de Mission</li>
                    </ul>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-clock"></i> Véhicules Temporaires</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-info">W 12345</span> Garage/Réparation</li>
                        <li><span class="badge bg-info">WW 12345</span> Véhicule neuf</li>
                    </ul>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-building"></i> Administration</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-secondary">ADM 1234</span> Administration</li>
                        <li><span class="badge bg-secondary">ADMIN 1234</span> Administration étendue</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Codes Territoriaux -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt"></i> Codes Territoriaux Officiels (89 codes)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>Principales Villes</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-primary">1</span> Rabat</li>
                            <li><span class="badge bg-primary">6</span> Casablanca Anfa</li>
                            <li><span class="badge bg-primary">14</span> Mohammedia</li>
                            <li><span class="badge bg-primary">16</span> Fès Medina</li>
                            <li><span class="badge bg-primary">26</span> Marrakech-Menara</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>Autres Grandes Villes</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-primary">34</span> Agadir – Inezgane</li>
                            <li><span class="badge bg-primary">40</span> Tangier – Asilah</li>
                            <li><span class="badge bg-primary">48</span> Oujda</li>
                            <li><span class="badge bg-primary">59</span> Kénitra</li>
                            <li><span class="badge bg-primary">68</span> Laâyoune</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>Exemples Sud</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-primary">63</span> Smara</li>
                            <li><span class="badge bg-primary">64</span> Guelmim</li>
                            <li><span class="badge bg-primary">70</span> Oued Ed-Dahab</li>
                            <li><span class="badge bg-primary">89</span> Lagouira</li>
                        </ul>
                        <small class="text-muted">Total : 89 codes territoriaux</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Testeur d'Immatriculation -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-vial"></i> Testeur d'Immatriculation
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="test-immat" class="form-label">
                            <i class="fas fa-keyboard"></i> Saisir une immatriculation à tester
                        </label>
                        <input type="text" class="form-control" id="test-immat" 
                               placeholder="Ex: 12345-A-67">
                        <div id="test-result" class="mt-3"></div>
                    </div>
                    <div class="col-md-6">
                        <h6>Exemples à tester :</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-success btn-sm test-example" data-immat="12345 A 1">
                                12345 A 1 (Standard 5 chiffres)
                            </button>
                            <button class="btn btn-outline-success btn-sm test-example" data-immat="123 A 1">
                                123 A 1 (Format court 3 chiffres)
                            </button>
                            <button class="btn btn-outline-success btn-sm test-example" data-immat="1 A 1">
                                1 A 1 (Format très court 1 chiffre)
                            </button>
                            <button class="btn btn-outline-info btn-sm test-example" data-immat="67890 ل 34">
                                67890 ل 34 (Avec lettre arabe)
                            </button>
                            <button class="btn btn-outline-primary btn-sm test-example" data-immat="123-A-1">
                                123-A-1 (Format court tirets)
                            </button>
                            <button class="btn btn-outline-danger btn-sm test-example" data-immat="CD 123">
                                CD 123 (Diplomatique court)
                            </button>
                            <button class="btn btn-outline-warning btn-sm test-example" data-immat="WW 123">
                                WW 123 (Temporaire court)
                            </button>
                            <button class="btn btn-outline-secondary btn-sm test-example" data-immat="ABCD-E-FG">
                                ABCD-E-FG (Format invalide)
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Règles de Validation -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Règles de Validation
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success"></i> Formats Acceptés</h6>
                        <ul>
                            <li>Standard long : <code>12345 A 1</code></li>
                            <li>Standard moyen : <code>1234 B 6</code></li>
                            <li>Format court : <code>123 A 1</code></li>
                            <li>Format très court : <code>12 C 16</code></li>
                            <li>Format minimal : <code>1 A 1</code></li>
                            <li>Avec tirets : <code>123-A-1</code></li>
                            <li>Lettre arabe : <code>123 ل 1</code></li>
                            <li>Diplomatique : <code>CD 123</code></li>
                            <li>Temporaire : <code>WW 123</code></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-times-circle text-danger"></i> Formats Rejetés</h6>
                        <ul>
                            <li>Lettres dans les chiffres : <code>ABCD-E-FG</code></li>
                            <li>Format incorrect : <code>A-123-1</code></li>
                            <li>Caractères spéciaux : <code>123@-A-1</code></li>
                            <li>Trop de chiffres : <code>123456-A-1</code></li>
                            <li>Code territorial invalide : <code>123-A-99</code></li>
                            <li>Vide ou espaces seuls : <code>   </code></li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-lightbulb"></i> Conseils</h6>
                    <ul class="mb-0">
                        <li><strong>Formats courts acceptés :</strong> De 1 à 5 chiffres sont valides</li>
                        <li><strong>Normalisation automatique :</strong> Espaces convertis en tirets si nécessaire</li>
                        <li><strong>Casse automatique :</strong> Lettres automatiquement en majuscules</li>
                        <li><strong>Validation complète :</strong> Vérification de l'unicité en base</li>
                        <li><strong>Codes territoriaux :</strong> 89 codes officiels supportés (1-89)</li>
                        <li><strong>Lettres arabes :</strong> Support complet des caractères arabes</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const testInput = document.getElementById('test-immat');
    const testResult = document.getElementById('test-result');
    const exampleButtons = document.querySelectorAll('.test-example');
    
    // Test en temps réel
    testInput.addEventListener('input', function() {
        const value = this.value.trim();
        if (value.length < 3) {
            testResult.innerHTML = '';
            return;
        }
        
        testerImmatriculation(value);
    });
    
    // Boutons d'exemple
    exampleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const immat = this.dataset.immat;
            testInput.value = immat;
            testerImmatriculation(immat);
        });
    });
    
    function testerImmatriculation(immatriculation) {
        fetch('/api/valider-immatriculation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                immatriculation: immatriculation
            })
        })
        .then(response => response.json())
        .then(data => {
            let html = '';
            
            if (data.valide) {
                html = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle"></i> Immatriculation Valide</h6>
                        <p><strong>Format :</strong> ${data.format.replace('_', ' ')}</p>
                        <p><strong>Formatée :</strong> <code>${data.immatriculation_formatee}</code></p>
                        ${data.region ? `<p><strong>Région :</strong> ${data.region}</p>` : ''}
                        <p><strong>Message :</strong> ${data.message}</p>
                    </div>
                `;
            } else {
                html = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times-circle"></i> Immatriculation Invalide</h6>
                        <p><strong>Erreur :</strong> ${data.message}</p>
                        <p><strong>Saisie :</strong> <code>${immatriculation}</code></p>
                    </div>
                `;
            }
            
            testResult.innerHTML = html;
        })
        .catch(error => {
            testResult.innerHTML = `
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Erreur de Test</h6>
                    <p>Impossible de tester l'immatriculation : ${error.message}</p>
                </div>
            `;
        });
    }
});
</script>
{% endblock %}
