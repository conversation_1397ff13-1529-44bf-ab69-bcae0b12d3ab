{% extends "base.html" %}

{% block title %}Gestion des Maintenances - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-tools"></i> Gestion des Maintenances</h1>
            <a href="{{ url_for('ajouter_maintenance') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Planifier une maintenance
            </a>
        </div>
    </div>
</div>

<!-- Tableau des maintenances -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> Liste des maintenances
                <span class="badge bg-secondary ms-2">{{ maintenances|length }} maintenance(s)</span>
            </h5>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download"></i> Exporter
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('export_maintenances', format='csv') }}">
                        <i class="fas fa-file-csv"></i> CSV
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('export_maintenances', format='xlsx') }}">
                        <i class="fas fa-file-excel"></i> Excel (XLSX)
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('export_maintenances', format='xls') }}">
                        <i class="fas fa-file-excel"></i> Excel (XLS)
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if maintenances %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Véhicule</th>
                        <th>Type</th>
                        <th>Date prévue</th>
                        <th>Priorité</th>
                        <th>Coût (MAD)</th>
                        <th>Garage</th>
                        <th>Justificatif</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for maintenance in maintenances %}
                    <tr>
                        <td>
                            <strong>{{ maintenance.immatriculation }}</strong><br>
                            <small class="text-muted">{{ maintenance.marque }} {{ maintenance.modele }}</small>
                        </td>
                        <td>{{ maintenance.type_maintenance }}</td>
                        <td>{{ maintenance.date_maintenance }}</td>
                        <td>
                            {% if maintenance.priorite == 'urgente' %}
                                <span class="badge bg-danger">🔴 Urgente</span>
                            {% elif maintenance.priorite == 'elevee' %}
                                <span class="badge bg-warning">🟠 Élevée</span>
                            {% elif maintenance.priorite == 'normale' %}
                                <span class="badge bg-info">🟡 Normale</span>
                            {% elif maintenance.priorite == 'faible' %}
                                <span class="badge bg-success">🟢 Faible</span>
                            {% else %}
                                <span class="badge bg-secondary">🟡 Normale</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if maintenance.cout %}
                                {{ "{:,.2f}".format(maintenance.cout).replace(',', ' ') }} MAD
                            {% else %}
                                <span class="text-muted">Non estimé</span>
                            {% endif %}
                        </td>
                        <td>{{ maintenance.garage or '-' }}</td>
                        <td>
                            {% if maintenance.justificatif %}
                                {% set file_extension = maintenance.justificatif.split('.')[-1].lower() %}
                                {% if file_extension in ['jpg', 'jpeg', 'png', 'gif'] %}
                                    <a href="{{ url_for('uploaded_file', filename=maintenance.justificatif) }}" 
                                       target="_blank" class="btn btn-sm btn-outline-success" title="Voir l'image">
                                        <i class="fas fa-image"></i>
                                    </a>
                                {% elif file_extension == 'pdf' %}
                                    <a href="{{ url_for('uploaded_file', filename=maintenance.justificatif) }}" 
                                       target="_blank" class="btn btn-sm btn-outline-danger" title="Voir le PDF">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                {% else %}
                                    <a href="{{ url_for('uploaded_file', filename=maintenance.justificatif) }}" 
                                       target="_blank" class="btn btn-sm btn-outline-info" title="Télécharger le fichier">
                                        <i class="fas fa-file"></i>
                                    </a>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if maintenance.statut == 'planifiee' %}
                                <span class="badge bg-warning text-dark">📅 Planifiée</span>
                            {% elif maintenance.statut == 'en_cours' %}
                                <span class="badge bg-info">⚙️ En cours</span>
                            {% elif maintenance.statut == 'terminee' %}
                                <span class="badge bg-success">✅ Terminée</span>
                            {% elif maintenance.statut == 'reportee' %}
                                <span class="badge bg-secondary">⏸️ Reportée</span>
                            {% elif maintenance.statut == 'annulee' %}
                                <span class="badge bg-danger">❌ Annulée</span>
                            {% else %}
                                <span class="badge bg-warning text-dark">📅 Planifiée</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                {% if maintenance.statut == 'planifiee' %}
                                <form method="POST" action="{{ url_for('demarrer_maintenance', id=maintenance.id) }}" style="display: inline;">
                                    <button type="submit" class="btn btn-outline-success btn-sm btn-action"
                                            title="Démarrer la maintenance"
                                            onclick="return confirm('Démarrer cette maintenance ?')">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </form>
                                <a href="{{ url_for('modifier_maintenance', id=maintenance.id) }}"
                                   class="btn btn-outline-primary btn-sm"
                                   title="Modifier la maintenance">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% elif maintenance.statut == 'en_cours' %}
                                <form method="POST" action="{{ url_for('terminer_maintenance', id=maintenance.id) }}" style="display: inline;">
                                    <button type="submit" class="btn btn-outline-success btn-sm btn-action"
                                            title="Terminer la maintenance"
                                            onclick="return confirm('Marquer cette maintenance comme terminée ?')">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                <a href="{{ url_for('modifier_maintenance', id=maintenance.id) }}"
                                   class="btn btn-outline-primary btn-sm"
                                   title="Modifier la maintenance">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% elif maintenance.statut == 'terminee' %}
                                <span class="btn btn-outline-secondary btn-sm disabled" title="Maintenance terminée">
                                    <i class="fas fa-check-circle"></i>
                                </span>
                                {% endif %}
                                <a href="{{ url_for('voir_maintenance', id=maintenance.id) }}"
                                   class="btn btn-outline-info btn-sm"
                                   title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tools fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune maintenance planifiée</h5>
            <p class="text-muted">Commencez par planifier la première maintenance.</p>
            <a href="{{ url_for('ajouter_maintenance') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Planifier une maintenance
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.btn-action {
    transition: all 0.2s ease;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-group-sm .btn {
    margin-right: 2px;
}

.btn-group-sm .btn:last-child {
    margin-right: 0;
}

.table td {
    vertical-align: middle;
}

.badge {
    font-size: 0.85em;
}
</style>
{% endblock %}
