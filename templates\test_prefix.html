{% extends "base.html" %}

{% block title %}Test Préfixe /gesparc - GesParc Auto{% endblock %}

{% block extra_css %}
<style>
    .test-card {
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.2s ease;
    }
    .test-card:hover {
        transform: translateY(-2px);
    }
    .url-test {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        border-left: 4px solid #007bff;
    }
    .url-success {
        border-left-color: #28a745;
        background: #d4edda;
    }
    .url-error {
        border-left-color: #dc3545;
        background: #f8d7da;
    }
    .config-info {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="config-info">
                <h1 class="mb-3">
                    <i class="fas fa-cogs"></i>
                    Test de Configuration Préfixe /gesparc
                </h1>
                <div class="row">
                    <div class="col-md-4">
                        <strong>Mode Préfixe:</strong> 
                        {% if USE_PREFIX %}
                            <span class="badge bg-success">✅ Activé</span>
                        {% else %}
                            <span class="badge bg-warning">❌ Désactivé</span>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <strong>Préfixe:</strong> 
                        <code>{{ GESPARC_PREFIX or 'Aucun' }}</code>
                    </div>
                    <div class="col-md-4">
                        <strong>URL Actuelle:</strong> 
                        <code>{{ request.url }}</code>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Test des URLs -->
        <div class="col-md-6">
            <div class="card test-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-link"></i>
                        Test des URLs
                    </h5>
                </div>
                <div class="card-body">
                    <h6>URLs Flask Standard (url_for):</h6>
                    
                    <div class="url-test">
                        <strong>Page d'accueil:</strong><br>
                        <code>{{ url_for('index') }}</code><br>
                        <a href="{{ url_for('index') }}" class="btn btn-sm btn-outline-primary mt-2">
                            <i class="fas fa-external-link-alt"></i> Tester
                        </a>
                    </div>
                    
                    <div class="url-test">
                        <strong>Véhicules:</strong><br>
                        <code>{{ url_for('vehicules') }}</code><br>
                        <a href="{{ url_for('vehicules') }}" class="btn btn-sm btn-outline-primary mt-2">
                            <i class="fas fa-external-link-alt"></i> Tester
                        </a>
                    </div>
                    
                    <div class="url-test">
                        <strong>Analytics Matplotlib:</strong><br>
                        <code>{{ url_for('analytics_matplotlib') }}</code><br>
                        <a href="{{ url_for('analytics_matplotlib') }}" class="btn btn-sm btn-outline-primary mt-2">
                            <i class="fas fa-external-link-alt"></i> Tester
                        </a>
                    </div>
                    
                    <div class="url-test">
                        <strong>Rapports:</strong><br>
                        <code>{{ url_for('rapports') }}</code><br>
                        <a href="{{ url_for('rapports') }}" class="btn btn-sm btn-outline-primary mt-2">
                            <i class="fas fa-external-link-alt"></i> Tester
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test des URLs avec préfixe -->
        <div class="col-md-6">
            <div class="card test-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-link"></i>
                        URLs avec Préfixe (url_for_prefix)
                    </h5>
                </div>
                <div class="card-body">
                    <h6>URLs avec préfixe automatique:</h6>
                    
                    <div class="url-test">
                        <strong>Page d'accueil:</strong><br>
                        <code>{{ url_for_prefix('index') }}</code><br>
                        <a href="{{ url_for_prefix('index') }}" class="btn btn-sm btn-outline-success mt-2">
                            <i class="fas fa-external-link-alt"></i> Tester
                        </a>
                    </div>
                    
                    <div class="url-test">
                        <strong>Véhicules:</strong><br>
                        <code>{{ url_for_prefix('vehicules') }}</code><br>
                        <a href="{{ url_for_prefix('vehicules') }}" class="btn btn-sm btn-outline-success mt-2">
                            <i class="fas fa-external-link-alt"></i> Tester
                        </a>
                    </div>
                    
                    <div class="url-test">
                        <strong>Analytics Matplotlib:</strong><br>
                        <code>{{ url_for_prefix('analytics_matplotlib') }}</code><br>
                        <a href="{{ url_for_prefix('analytics_matplotlib') }}" class="btn btn-sm btn-outline-success mt-2">
                            <i class="fas fa-external-link-alt"></i> Tester
                        </a>
                    </div>
                    
                    <div class="url-test">
                        <strong>Rapports:</strong><br>
                        <code>{{ url_for_prefix('rapports') }}</code><br>
                        <a href="{{ url_for_prefix('rapports') }}" class="btn btn-sm btn-outline-success mt-2">
                            <i class="fas fa-external-link-alt"></i> Tester
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Informations de configuration -->
        <div class="col-md-6">
            <div class="card test-card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        Configuration Actuelle
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>APPLICATION_ROOT:</strong></td>
                            <td><code>{{ config.APPLICATION_ROOT or 'Non défini' }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>SCRIPT_NAME (env):</strong></td>
                            <td><code>{{ request.environ.get('SCRIPT_NAME', 'Non défini') }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>PATH_INFO:</strong></td>
                            <td><code>{{ request.environ.get('PATH_INFO', 'Non défini') }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>REQUEST_URI:</strong></td>
                            <td><code>{{ request.environ.get('REQUEST_URI', 'Non défini') }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>USE_PREFIX:</strong></td>
                            <td>
                                {% if USE_PREFIX %}
                                    <span class="badge bg-success">True</span>
                                {% else %}
                                    <span class="badge bg-secondary">False</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Actions de test -->
        <div class="col-md-6">
            <div class="card test-card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-tools"></i>
                        Actions de Test
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for_prefix('index') }}" class="btn btn-primary">
                            <i class="fas fa-home"></i> Retour à l'Accueil
                        </a>
                        
                        <a href="{{ url_for_prefix('vehicules') }}" class="btn btn-outline-primary">
                            <i class="fas fa-car"></i> Test Page Véhicules
                        </a>
                        
                        <a href="{{ url_for_prefix('analytics_matplotlib') }}" class="btn btn-outline-success">
                            <i class="fas fa-chart-area"></i> Test Analytics Matplotlib
                        </a>
                        
                        <button onclick="testAllUrls()" class="btn btn-outline-info">
                            <i class="fas fa-play"></i> Tester Toutes les URLs
                        </button>
                        
                        <button onclick="location.reload()" class="btn btn-outline-secondary">
                            <i class="fas fa-sync-alt"></i> Actualiser
                        </button>
                    </div>
                    
                    <hr>
                    
                    <h6>URLs Externes:</h6>
                    <div class="d-grid gap-2">
                        <a href="http://localhost:5001/" target="_blank" class="btn btn-sm btn-outline-dark">
                            <i class="fas fa-external-link-alt"></i> Mode Normal (port 5001)
                        </a>
                        
                        <a href="http://localhost:5001/gesparc/" target="_blank" class="btn btn-sm btn-outline-dark">
                            <i class="fas fa-external-link-alt"></i> Mode Préfixe (port 5001/gesparc)
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function testAllUrls() {
    const urls = [
        '{{ url_for_prefix("index") }}',
        '{{ url_for_prefix("vehicules") }}',
        '{{ url_for_prefix("analytics_matplotlib") }}',
        '{{ url_for_prefix("rapports") }}'
    ];
    
    let results = [];
    let completed = 0;
    
    console.log('🧪 Test de toutes les URLs...');
    
    urls.forEach((url, index) => {
        fetch(url)
            .then(response => {
                results[index] = {
                    url: url,
                    status: response.status,
                    ok: response.ok
                };
                completed++;
                
                if (completed === urls.length) {
                    showResults(results);
                }
            })
            .catch(error => {
                results[index] = {
                    url: url,
                    status: 'Error',
                    ok: false,
                    error: error.message
                };
                completed++;
                
                if (completed === urls.length) {
                    showResults(results);
                }
            });
    });
}

function showResults(results) {
    let message = '📊 Résultats des Tests d\'URLs:\n\n';
    
    results.forEach(result => {
        const status = result.ok ? '✅' : '❌';
        message += `${status} ${result.url} → ${result.status}\n`;
    });
    
    alert(message);
    console.log('📊 Résultats:', results);
}

// Auto-test au chargement si en mode debug
{% if config.DEBUG %}
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Mode Debug - Configuration préfixe:');
    console.log('   USE_PREFIX:', {{ USE_PREFIX|tojson }});
    console.log('   GESPARC_PREFIX:', {{ GESPARC_PREFIX|tojson }});
    console.log('   APPLICATION_ROOT:', {{ config.APPLICATION_ROOT|tojson }});
});
{% endif %}
</script>
{% endblock %}
