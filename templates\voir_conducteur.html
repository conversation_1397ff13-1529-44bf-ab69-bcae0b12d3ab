{% extends "base.html" %}

{% block title %}{{ conducteur.prenom }} {{ conducteur.nom }} - Détails du Conducteur{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user"></i> {{ conducteur.prenom }} {{ conducteur.nom }}
                <span class="badge {{ 'bg-success' if conducteur.statut == 'actif' else 'bg-secondary' }}">
                    {{ conducteur.statut|title }}
                </span>
            </h1>
            <div class="btn-group">
                <a href="{{ url_for('conducteurs') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
                <a href="{{ url_for('modifier_conducteur', id=conducteur.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> Modifier
                </a>
                <a href="{{ url_for('supprimer_conducteur', id=conducteur.id) }}" 
                   class="btn btn-danger btn-delete" 
                   data-item-name="{{ conducteur.prenom }} {{ conducteur.nom }}">
                    <i class="fas fa-trash"></i> Supprimer
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informations personnelles -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-circle"></i> Informations personnelles
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Nom complet:</strong></td>
                        <td>{{ conducteur.prenom }} {{ conducteur.nom }}</td>
                    </tr>
                    <tr>
                        <td><strong>Numéro de permis:</strong></td>
                        <td><code>{{ conducteur.numero_permis }}</code></td>
                    </tr>
                    {% if conducteur.date_permis %}
                    <tr>
                        <td><strong>Date du permis:</strong></td>
                        <td>
                            {{ conducteur.date_permis }}
                            <br><small class="text-muted">
                                {% set years = ((2025 - conducteur.date_permis.split('-')[0]|int) if conducteur.date_permis else 0) %}
                                {{ years }} ans d'expérience
                            </small>
                        </td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>Statut:</strong></td>
                        <td>
                            {% if conducteur.statut == 'actif' %}
                                <span class="badge bg-success">Actif</span>
                            {% else %}
                                <span class="badge bg-secondary">Inactif</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Date d'enregistrement:</strong></td>
                        <td>{{ conducteur.date_creation }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Informations de contact -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-address-book"></i> Informations de contact
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Téléphone:</strong></td>
                        <td>
                            {% if conducteur.telephone %}
                                <i class="fas fa-phone text-success"></i> 
                                <a href="tel:{{ conducteur.telephone }}">{{ conducteur.telephone }}</a>
                            {% else %}
                                <span class="text-muted">Non renseigné</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td>
                            {% if conducteur.email %}
                                <i class="fas fa-envelope text-primary"></i> 
                                <a href="mailto:{{ conducteur.email }}">{{ conducteur.email }}</a>
                            {% else %}
                                <span class="text-muted">Non renseigné</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
                
                {% if conducteur.telephone or conducteur.email %}
                <div class="mt-3">
                    <div class="btn-group btn-group-sm">
                        {% if conducteur.telephone %}
                        <a href="tel:{{ conducteur.telephone }}" class="btn btn-outline-success">
                            <i class="fas fa-phone"></i> Appeler
                        </a>
                        {% endif %}
                        {% if conducteur.email %}
                        <a href="mailto:{{ conducteur.email }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope"></i> Envoyer un email
                        </a>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Historique des affectations -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-car"></i> Historique des affectations de véhicules
                    <span class="badge bg-secondary ms-2">{{ affectations|length }}</span>
                </h5>
                <a href="{{ url_for('affectations') }}?conducteur_id={{ conducteur.id }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> Nouvelle affectation
                </a>
            </div>
            <div class="card-body">
                {% if affectations %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Véhicule</th>
                                <th>Date début</th>
                                <th>Date fin</th>
                                <th>Durée</th>
                                <th>Statut</th>
                                <th>Commentaire</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for affectation in affectations %}
                            <tr>
                                <td>
                                    <strong>{{ affectation.immatriculation }}</strong><br>
                                    <small class="text-muted">{{ affectation.marque }} {{ affectation.modele }}</small>
                                </td>
                                <td>{{ affectation.date_debut }}</td>
                                <td>{{ affectation.date_fin or '-' }}</td>
                                <td>
                                    {% if affectation.date_fin %}
                                        {% set debut = affectation.date_debut %}
                                        {% set fin = affectation.date_fin %}
                                        <!-- Calcul approximatif de la durée -->
                                        <span class="text-muted">Terminée</span>
                                    {% elif affectation.statut == 'active' %}
                                        <span class="badge bg-info">En cours</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if affectation.statut == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                    {% elif affectation.statut == 'terminee' %}
                                        <span class="badge bg-secondary">Terminée</span>
                                    {% elif affectation.statut == 'annulee' %}
                                        <span class="badge bg-danger">Annulée</span>
                                    {% endif %}
                                </td>
                                <td>{{ affectation.commentaire or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Statistiques des affectations -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>Total affectations</h6>
                                <h4 class="text-primary">{{ affectations|length }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>Affectations actives</h6>
                                <h4 class="text-success">
                                    {{ affectations|selectattr('statut', 'equalto', 'active')|list|length }}
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>Véhicules différents</h6>
                                <h4 class="text-info">
                                    {{ affectations|map(attribute='immatriculation')|unique|list|length }}
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-car-slash fa-2x text-muted mb-3"></i>
                    <p class="text-muted">Aucune affectation de véhicule enregistrée pour ce conducteur</p>
                    <a href="{{ url_for('affectations') }}?conducteur_id={{ conducteur.id }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Créer la première affectation
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
