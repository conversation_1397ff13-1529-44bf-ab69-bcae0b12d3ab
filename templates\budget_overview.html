{% extends "base.html" %}

{% block title %}Gestion des Budgets - GesParc Auto{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-wallet text-success"></i> Gestion des Budgets
                </h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('config_budget_globale') }}" class="btn btn-secondary">
                        <i class="fas fa-cog"></i> Configuration Globale
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Résumé global -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Recettes</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(total_recettes).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-up fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Dépenses</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(total_depenses).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Solde Global</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(total_solde).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-balance-scale fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Types de Budget</h6>
                            <h4 class="mb-0">{{ budgets_data|length }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Budgets par type -->
    <div class="row">
        {% for budget in budgets_data %}
        <div class="col-md-4 mb-4">
            <div class="card h-100 {% if budget.alerte %}border-warning{% endif %}">
                <div class="card-header {% if budget.config.type_budget == 'carburant' %}bg-primary text-white{% elif budget.config.type_budget == 'maintenance' %}bg-warning text-dark{% else %}bg-info text-white{% endif %}">
                    <h5 class="card-title mb-0">
                        {% if budget.config.type_budget == 'carburant' %}
                            <i class="fas fa-gas-pump"></i> {{ budget.config.nom_budget }}
                        {% elif budget.config.type_budget == 'maintenance' %}
                            <i class="fas fa-tools"></i> {{ budget.config.nom_budget }}
                        {% else %}
                            <i class="fas fa-truck"></i> {{ budget.config.nom_budget }}
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Indicateurs financiers -->
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">Recettes</small>
                            <div class="h6 text-success">{{ '{:,.2f}'.format(budget.recettes).replace(',', ' ') }} MAD</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Dépenses</small>
                            <div class="h6 text-danger">{{ '{:,.2f}'.format(budget.depenses).replace(',', ' ') }} MAD</div>
                        </div>
                    </div>
                    
                    <!-- Solde actuel -->
                    <div class="mb-3">
                        <small class="text-muted">Solde Actuel</small>
                        <div class="h5 {% if budget.solde_actuel >= budget.config.seuil_alerte %}text-success{% else %}text-warning{% endif %}">
                            {{ '{:,.2f}'.format(budget.solde_actuel).replace(',', ' ') }} MAD
                        </div>
                        
                        <!-- Barre de progression -->
                        {% set pourcentage = (budget.solde_actuel / budget.config.budget_initial * 100) if budget.config.budget_initial > 0 else 0 %}
                        <div class="progress mb-2" style="height: 8px;">
                            <div class="progress-bar {% if pourcentage >= 50 %}bg-success{% elif pourcentage >= 25 %}bg-warning{% else %}bg-danger{% endif %}" 
                                 style="width: {{ pourcentage|round(1) }}%"></div>
                        </div>
                        <small class="text-muted">{{ pourcentage|round(1) }}% du budget initial</small>
                    </div>

                    <!-- Alerte si nécessaire -->
                    {% if budget.alerte %}
                    <div class="alert alert-warning alert-sm py-2">
                        <i class="fas fa-exclamation-triangle"></i>
                        <small>Solde inférieur au seuil d'alerte ({{ '{:,.2f}'.format(budget.config.seuil_alerte).replace(',', ' ') }} MAD)</small>
                    </div>
                    {% endif %}

                    <!-- Dernières opérations -->
                    {% if budget.operations %}
                    <div class="mb-3">
                        <h6 class="text-muted">Dernières Opérations</h6>
                        {% for operation in budget.operations[:3] %}
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <div>
                                <small>
                                    {% if operation.type == 'recette' %}
                                        <span class="badge badge-sm bg-success">+</span>
                                    {% elif operation.type == 'depense' %}
                                        <span class="badge badge-sm bg-danger">-</span>
                                    {% else %}
                                        <span class="badge badge-sm bg-primary">↑</span>
                                    {% endif %}
                                    {{ operation.commentaire[:25] }}{% if operation.commentaire|length > 25 %}...{% endif %}
                                </small>
                            </div>
                            <small class="text-muted">{{ '{:,.0f}'.format(operation.montant).replace(',', ' ') }}</small>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <a href="{{ url_for('budget_detail', type_budget=budget.config.type_budget) }}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> Détails
                        </a>
                        <a href="{{ url_for('ajouter_operation_budget', type_budget=budget.config.type_budget) }}" 
                           class="btn btn-outline-success btn-sm">
                            <i class="fas fa-plus"></i> Opération
                        </a>
                        <a href="{{ url_for('reapprovisionner_budget', type_budget=budget.config.type_budget) }}" 
                           class="btn btn-outline-info btn-sm">
                            <i class="fas fa-money-bill-wave"></i> Réappro.
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Actions rapides -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt"></i> Actions Rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-gas-pump text-primary"></i> Budget Carburant</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                <a href="{{ url_for('ajouter_operation_budget', type_budget='carburant') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-plus"></i> Nouvelle Dépense Carburant
                                </a>
                                <a href="{{ url_for('reapprovisionner_budget', type_budget='carburant') }}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-money-bill-wave"></i> Réapprovisionner
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-tools text-warning"></i> Budget Maintenance</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                <a href="{{ url_for('ajouter_operation_budget', type_budget='maintenance') }}" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-plus"></i> Nouvelle Dépense Maintenance
                                </a>
                                <a href="{{ url_for('reapprovisionner_budget', type_budget='maintenance') }}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-money-bill-wave"></i> Réapprovisionner
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-truck text-info"></i> Budget Transport</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                <a href="{{ url_for('ajouter_operation_budget', type_budget='transport_terrestre') }}" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-plus"></i> Nouvelle Dépense Transport
                                </a>
                                <a href="{{ url_for('reapprovisionner_budget', type_budget='transport_terrestre') }}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-money-bill-wave"></i> Réapprovisionner
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}