{% extends "base.html" %}

{% block title %}Nouvelle Opération Budget - GesParc Auto{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-plus text-success"></i> Nouvelle Opération - {{ config.nom_budget }}
                </h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('budget') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Vue d'ensemble
                    </a>
                    <a href="{{ url_for('budget_detail', type_budget=type_budget) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-eye"></i> Détails {{ config.nom_budget }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-wallet"></i> Informations de l'Opération
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <!-- Type d'opération -->
                            <div class="col-md-6 mb-3">
                                <label for="type" class="form-label">
                                    <i class="fas fa-tag"></i> Type d'opération *
                                </label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">Sélectionnez le type</option>
                                    <option value="recette">
                                        <i class="fas fa-arrow-up"></i> Recette
                                    </option>
                                    <option value="depense">
                                        <i class="fas fa-arrow-down"></i> Dépense
                                    </option>
                                    <option value="reapprovisionnement">
                                        <i class="fas fa-money-bill-wave"></i> Réapprovisionnement
                                    </option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner le type d'opération
                                </div>
                            </div>

                            <!-- Montant -->
                            <div class="col-md-6 mb-3">
                                <label for="montant" class="form-label">
                                    <i class="fas fa-coins"></i> Montant (MAD) *
                                </label>
                                <input type="number" class="form-control" id="montant" 
                                       name="montant" min="0.01" step="0.01" required
                                       placeholder="0.00">
                                <div class="invalid-feedback">
                                    Veuillez saisir un montant valide
                                </div>
                                <small class="form-text text-muted">
                                    Montant en dirhams marocains
                                </small>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Date et heure -->
                            <div class="col-md-6 mb-3">
                                <label for="date_operation" class="form-label">
                                    <i class="fas fa-calendar"></i> Date et heure *
                                </label>
                                <input type="datetime-local" class="form-control" id="date_operation"
                                       name="date_operation" required
                                       value="{{ datetime_default }}">
                                <div class="invalid-feedback">
                                    Veuillez saisir la date et heure
                                </div>
                            </div>

                            <!-- Catégorie -->
                            <div class="col-md-6 mb-3">
                                <label for="categorie" class="form-label">
                                    <i class="fas fa-folder"></i> Catégorie
                                </label>
                                <select class="form-select" id="categorie" name="categorie">
                                    <option value="">Sélectionnez une catégorie</option>
                                    {% if type_budget == 'carburant' %}
                                        <option value="Carburant" selected>Carburant</option>
                                        <option value="Essence">Essence</option>
                                        <option value="Diesel">Diesel</option>
                                        <option value="GPL">GPL</option>
                                    {% elif type_budget == 'maintenance' %}
                                        <option value="Maintenance" selected>Maintenance</option>
                                        <option value="Révision">Révision</option>
                                        <option value="Réparation">Réparation</option>
                                        <option value="Pièces détachées">Pièces détachées</option>
                                        <option value="Pneus">Pneus</option>
                                        <option value="Vidange">Vidange</option>
                                    {% else %}
                                        <option value="Transport" selected>Transport</option>
                                        <option value="Péage">Péage</option>
                                        <option value="Parking">Parking</option>
                                        <option value="Frais de route">Frais de route</option>
                                        <option value="Location">Location</option>
                                    {% endif %}
                                    <option value="Autre">Autre</option>
                                </select>
                                <small class="form-text text-muted">
                                    Catégorie spécifique au {{ config.nom_budget }}
                                </small>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Référence -->
                            <div class="col-md-6 mb-3">
                                <label for="reference" class="form-label">
                                    <i class="fas fa-hashtag"></i> Référence
                                </label>
                                <input type="text" class="form-control" id="reference" 
                                       name="reference" maxlength="50"
                                       placeholder="REF-001">
                                <small class="form-text text-muted">
                                    Numéro de référence ou de facture
                                </small>
                            </div>
                        </div>

                        <!-- Commentaire -->
                        <div class="mb-3">
                            <label for="commentaire" class="form-label">
                                <i class="fas fa-comment"></i> Commentaire
                            </label>
                            <textarea class="form-control" id="commentaire" name="commentaire" 
                                      rows="3" maxlength="500"
                                      placeholder="Description de l'opération..."></textarea>
                            <small class="form-text text-muted">
                                Description détaillée de l'opération (optionnel)
                            </small>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('budget_detail', type_budget=type_budget) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Enregistrer l'Opération
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mise à jour de l'icône selon le type sélectionné
    const typeSelect = document.getElementById('type');
    const montantInput = document.getElementById('montant');
    
    typeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        const montantLabel = document.querySelector('label[for="montant"]');
        
        if (selectedType === 'recette' || selectedType === 'reapprovisionnement') {
            montantInput.style.borderColor = '#28a745';
            montantLabel.innerHTML = '<i class="fas fa-coins text-success"></i> Montant (MAD) *';
        } else if (selectedType === 'depense') {
            montantInput.style.borderColor = '#dc3545';
            montantLabel.innerHTML = '<i class="fas fa-coins text-danger"></i> Montant (MAD) *';
        } else {
            montantInput.style.borderColor = '';
            montantLabel.innerHTML = '<i class="fas fa-coins"></i> Montant (MAD) *';
        }
    });
});
</script>
{% endblock %}