<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ordre de Mission - {{ affectation.immatriculation }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12pt; }
            .page-break { page-break-before: always; }
        }
        
        .ordre-mission {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Arial', sans-serif;
        }
        
        .header-logo {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .ordre-title {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .info-section {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px dotted #dee2e6;
        }
        
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
            min-width: 150px;
        }
        
        .info-value {
            color: #212529;
            flex: 1;
            text-align: right;
        }
        
        .mission-box {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .destination-box {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            text-align: center;
            width: 200px;
            border-top: 2px solid #dee2e6;
            padding-top: 10px;
        }
        
        .footer-info {
            margin-top: 40px;
            text-align: center;
            font-size: 0.9em;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        
        .badge-custom {
            font-size: 0.9em;
            padding: 8px 12px;
        }
        
        .vehicle-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .conductor-info {
            background: #f3e5f5;
            border: 1px solid #9c27b0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="ordre-mission">
        <!-- Boutons d'action (masqués à l'impression) -->
        <div class="no-print mb-4 text-center">
            <button onclick="window.print()" class="btn btn-primary me-2">
                <i class="fas fa-print"></i> Imprimer
            </button>
            <button onclick="window.close()" class="btn btn-secondary">
                <i class="fas fa-times"></i> Fermer
            </button>
        </div>

        <!-- En-tête -->
        <div class="header-logo">
            <h1 class="text-primary mb-2">
                <i class="fas fa-car"></i> GesParc Auto
            </h1>
            <p class="text-muted mb-0">Système de Gestion de Parc Automobile</p>
        </div>

        <!-- Titre de l'ordre de mission -->
        <div class="ordre-title">
            <h2 class="mb-2">
                <i class="fas fa-clipboard-list"></i> ORDRE DE MISSION
            </h2>
            <p class="mb-0">N° {{ affectation.id }} - {{ affectation.immatriculation }}</p>
        </div>

        <!-- Informations du conducteur -->
        <div class="conductor-info">
            <h4 class="text-purple mb-3">
                <i class="fas fa-user"></i> Informations du Conducteur
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">Nom complet:</span>
                        <span class="info-value"><strong>{{ affectation.prenom }} {{ affectation.nom }}</strong></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Téléphone:</span>
                        <span class="info-value">{{ affectation.telephone or 'Non renseigné' }}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">Email:</span>
                        <span class="info-value">{{ affectation.email or 'Non renseigné' }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">N° Permis:</span>
                        <span class="info-value">{{ affectation.numero_permis or 'Non renseigné' }}</span>
                    </div>
                </div>
            </div>

        </div>

        <!-- Informations du véhicule -->
        <div class="vehicle-info">
            <h4 class="text-primary mb-3">
                <i class="fas fa-car"></i> Véhicule Affecté
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">Immatriculation:</span>
                        <span class="info-value"><strong>{{ affectation.immatriculation }}</strong></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Marque/Modèle:</span>
                        <span class="info-value">{{ affectation.marque }} {{ affectation.modele }}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">Année:</span>
                        <span class="info-value">{{ affectation.annee }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Couleur:</span>
                        <span class="info-value">{{ affectation.couleur or 'Non spécifiée' }}</span>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">Carburant:</span>
                        <span class="info-value">{{ affectation.carburant or 'Non spécifié' }}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">Kilométrage:</span>
                        <span class="info-value">{{ '{:,}'.format(affectation.kilometrage or 0).replace(',', ' ') }} km</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Période et horaires -->
        <div class="info-section">
            <h4 class="text-info mb-3">
                <i class="fas fa-calendar-alt"></i> Période d'Affectation
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-play-circle text-success"></i> Départ:
                        </span>
                        <span class="info-value">
                            <strong>{{ affectation.date_debut | format_datetime }}</strong>
                        </span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-stop-circle text-danger"></i> Arrivée:
                        </span>
                        <span class="info-value">
                            {% if affectation.date_fin %}
                                <strong>{{ affectation.date_fin | format_datetime }}</strong>
                            {% else %}
                                <span class="text-muted">Non définie</span>
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
            {% if duree_affectation %}
            <div class="info-row mt-2">
                <span class="info-label">Durée prévue:</span>
                <span class="info-value"><strong>{{ duree_affectation }}</strong></span>
            </div>
            {% endif %}
        </div>

        <!-- Mission -->
        {% if affectation.mission %}
        <div class="mission-box">
            <h4 class="mb-3">
                <i class="fas fa-bullseye"></i> MISSION
            </h4>
            <p class="fs-5 mb-0">{{ affectation.mission }}</p>
        </div>
        {% endif %}

        <!-- Destination -->
        {% if affectation.destination %}
        <div class="destination-box">
            <h4 class="mb-3">
                <i class="fas fa-map-marker-alt"></i> DESTINATION
            </h4>
            <p class="fs-5 mb-0">{{ affectation.destination }}</p>
        </div>
        {% endif %}

        <!-- Informations complémentaires -->
        {% if affectation.commentaire %}
        <div class="info-section">
            <h5 class="text-secondary mb-3">
                <i class="fas fa-comment"></i> Informations Complémentaires
            </h5>
            <p class="mb-0">{{ affectation.commentaire }}</p>
        </div>
        {% endif %}

        <!-- Budget carburant -->
        {% if affectation.budget_carburant %}
        <div class="info-section">
            <h5 class="text-warning mb-3">
                <i class="fas fa-gas-pump"></i> Budget Carburant
            </h5>
            <div class="info-row">
                <span class="info-label">Budget alloué:</span>
                <span class="info-value">
                    <strong>{{ '{:,.2f}'.format(affectation.budget_carburant).replace(',', ' ') }} MAD</strong>
                </span>
            </div>
        </div>
        {% endif %}

        <!-- Signatures -->
        <div class="signature-section">
            <div class="signature-box">
                <p class="mb-4"><strong>Conducteur</strong></p>
                <div style="height: 60px; border-bottom: 1px solid #dee2e6; margin-bottom: 10px;"></div>
                <small>Signature et date</small>
            </div>
            
            <div class="signature-box">
                <p class="mb-4"><strong>Responsable Parc</strong></p>
                <div style="height: 60px; border-bottom: 1px solid #dee2e6; margin-bottom: 10px;"></div>
                <small>Signature et cachet</small>
            </div>
        </div>

        <!-- Pied de page -->
        <div class="footer-info">
            <p class="mb-1">
                <strong>Document généré le:</strong> {{ date_generation.strftime('%d/%m/%Y à %H:%M') }}
            </p>
            <p class="mb-1">
                <strong>Statut de l'affectation:</strong> 
                {% if affectation.statut == 'active' %}
                    <span class="badge bg-success badge-custom">Active</span>
                {% elif affectation.statut == 'terminee' %}
                    <span class="badge bg-secondary badge-custom">Terminée</span>
                {% else %}
                    <span class="badge bg-warning badge-custom">{{ affectation.statut }}</span>
                {% endif %}
            </p>
            <hr>
            <p class="mb-0">
                <small>
                    <i class="fas fa-shield-alt"></i> 
                    Ce document est confidentiel et ne doit être utilisé que dans le cadre de la mission spécifiée.
                </small>
            </p>
        </div>
    </div>

    <!-- Scripts Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Script d'impression automatique (optionnel) -->
    <script>
        // Impression automatique au chargement (décommentez si souhaité)
        // window.onload = function() { window.print(); }
        
        // Fermer la fenêtre après impression
        window.onafterprint = function() {
            // window.close(); // Décommentez si vous voulez fermer automatiquement
        }
    </script>
</body>
</html>
