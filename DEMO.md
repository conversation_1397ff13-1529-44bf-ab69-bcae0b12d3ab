# 🚗 Démonstration GesParc Auto

## Guide de Démonstration Rapide

### 🎯 Objectif
Cette démonstration vous guide à travers les principales fonctionnalités de GesParc Auto.

### 🚀 Démarrage Rapide

1. **Lancer l'application**
   ```bash
   python gesparc_app.py
   ```

2. **Ouvrir dans le navigateur**
   ```
   http://localhost:5001
   ```

### 📊 Tableau de Bord

**Ce que vous verrez :**
- 4 véhicules dans le parc
- 3 conducteurs enregistrés
- 3 maintenances planifiées
- Statistiques en temps réel

**Actions à tester :**
- Cliquer sur les cartes statistiques
- Utiliser les boutons d'actions rapides
- Observer les maintenances à venir

### 🚙 Gestion des Véhicules

**Véhicules de démonstration :**
- AB-123-CD : Peugeot 308 (2020) - Disponible
- EF-456-GH : Renault Clio (2019) - Affecté
- IJ-789-KL : Citroën C3 (2021) - Disponible
- MN-012-OP : Volkswagen Golf (2018) - En maintenance

**Tests à effectuer :**

1. **Consulter la liste**
   - Menu → Véhicules → Liste des véhicules
   - Tester la recherche : tapez "Peugeot"
   - Filtrer par statut : sélectionner "Disponible"

2. **Voir les détails**
   - Cliquer sur l'icône 👁️ d'un véhicule
   - Observer les informations complètes
   - Consulter l'historique des maintenances

3. **Ajouter un véhicule**
   - Cliquer sur "Ajouter un véhicule"
   - Remplir le formulaire :
     - Immatriculation : QR-567-ST
     - Marque : Toyota
     - Modèle : Yaris
     - Année : 2022
     - Carburant : Hybride
   - Enregistrer et vérifier dans la liste

4. **Modifier un véhicule**
   - Cliquer sur l'icône ✏️ d'un véhicule
   - Modifier le kilométrage
   - Enregistrer les modifications

### 👥 Gestion des Conducteurs

**Conducteurs de démonstration :**
- Jean Dupont (Permis: 123456789)
- Marie Martin (Permis: 987654321)
- Pierre Bernard (Permis: 456789123)

**Tests à effectuer :**

1. **Consulter la liste**
   - Menu → Conducteurs → Liste des conducteurs
   - Tester la recherche : tapez "Martin"
   - Observer les véhicules affectés

2. **Voir les détails**
   - Cliquer sur l'icône 👁️ d'un conducteur
   - Consulter les informations de contact
   - Voir l'historique des affectations

3. **Ajouter un conducteur**
   - Cliquer sur "Ajouter un conducteur"
   - Remplir le formulaire :
     - Nom : Durand
     - Prénom : Sophie
     - Numéro de permis : 789123456
     - Téléphone : 01 23 45 67 89
     - Email : <EMAIL>
   - Enregistrer

### 🔧 Maintenances

**Maintenances de démonstration :**
- Révision Golf (40 000 km) - 15/08/2025
- Vidange Clio - 20/07/2025
- Contrôle technique Peugeot - 01/09/2025

**Tests à effectuer :**
- Menu → Maintenance → Planification
- Observer les maintenances planifiées
- Noter les coûts estimés

### 🔄 Affectations

**Affectations de démonstration :**
- Jean Dupont → Renault Clio (Active)
- Marie Martin → Peugeot 308 (Terminée)

**Tests à effectuer :**
- Menu → Affectations
- Observer les affectations actives et terminées
- Noter les dates et commentaires

### 📊 Rapports et Statistiques

**Tests à effectuer :**

1. **Consulter les statistiques**
   - Menu → Rapports
   - Observer les graphiques circulaires
   - Analyser la répartition par statut et carburant

2. **Tableaux détaillés**
   - Consulter les pourcentages
   - Comparer les données

3. **Actions d'export**
   - Tester le bouton "Imprimer"
   - (L'export CSV est préparé mais nécessite des données dans les tableaux)

### 🎨 Interface et Ergonomie

**Points à observer :**

1. **Design responsive**
   - Redimensionner la fenêtre du navigateur
   - Tester sur mobile (F12 → mode mobile)

2. **Navigation intuitive**
   - Menu déroulant pour les véhicules et conducteurs
   - Breadcrumbs et boutons de retour

3. **Feedback utilisateur**
   - Messages de succès/erreur
   - Confirmations de suppression
   - Validation des formulaires

4. **Recherche et filtres**
   - Recherche en temps réel
   - Filtres par statut, carburant, etc.

### 🔍 Fonctionnalités Avancées

**Tests avancés :**

1. **Validation des formulaires**
   - Essayer de soumettre un formulaire vide
   - Tester les formats (immatriculation, email, téléphone)

2. **Gestion des erreurs**
   - Essayer d'ajouter un véhicule avec une immatriculation existante
   - Tenter de supprimer un véhicule affecté

3. **Calculs automatiques**
   - Observer le calcul de l'âge des véhicules
   - Voir la dépréciation estimée dans les détails

### 📱 Scénarios d'Usage

**Scénario 1 : Nouveau véhicule**
1. Réceptionner un nouveau véhicule
2. L'ajouter dans le système
3. Planifier sa première maintenance
4. L'affecter à un conducteur

**Scénario 2 : Maintenance préventive**
1. Consulter les maintenances à venir
2. Identifier les véhicules à entretenir
3. Planifier les interventions
4. Suivre les coûts

**Scénario 3 : Gestion quotidienne**
1. Consulter le tableau de bord
2. Vérifier les alertes
3. Gérer les affectations
4. Mettre à jour les kilométrages

### 🎯 Points Forts à Démontrer

1. **Simplicité d'utilisation** : Interface intuitive
2. **Complétude** : Toutes les fonctions essentielles
3. **Fiabilité** : Validation et gestion d'erreurs
4. **Performance** : Réactivité de l'interface
5. **Évolutivité** : Architecture modulaire

### 🔧 Personnalisation

**Modifications possibles :**
- Ajouter de nouveaux types de carburant
- Personnaliser les statuts
- Adapter les champs selon les besoins
- Modifier les couleurs et le style

---

**Durée de démonstration recommandée :** 15-20 minutes  
**Public cible :** Gestionnaires de flotte, responsables maintenance, directeurs techniques
