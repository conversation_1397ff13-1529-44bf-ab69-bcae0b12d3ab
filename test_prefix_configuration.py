#!/usr/bin/env python3
"""
Test complet de la configuration préfixe /gesparc pour Flask
"""

import requests
import os
import sys
import subprocess
import time
import json

def test_normal_mode():
    """Teste Flask en mode normal (sans préfixe)"""
    print("🔧 Test Mode Normal (sans préfixe)")
    print("=" * 45)
    
    base_url = "http://localhost:5001"
    
    test_urls = [
        "/",
        "/vehicules", 
        "/test-prefix",
        "/api/prefix-info"
    ]
    
    results = {}
    
    for url in test_urls:
        try:
            full_url = base_url + url
            print(f"📍 Test: {full_url}")
            
            response = requests.get(full_url, timeout=5)
            results[url] = {
                'status': response.status_code,
                'ok': response.ok,
                'url': full_url
            }
            
            if response.ok:
                print(f"   ✅ {response.status_code}")
            else:
                print(f"   ❌ {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connexion refusée")
            results[url] = {'status': 'Connection Error', 'ok': False, 'url': full_url}
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            results[url] = {'status': f'Error: {e}', 'ok': False, 'url': full_url}
    
    return results

def test_prefix_mode():
    """Teste Flask en mode préfixe"""
    print("\n🌐 Test Mode Préfixe (/gesparc)")
    print("=" * 40)
    
    # Démarrer Flask avec préfixe
    print("🚀 Démarrage de Flask avec préfixe...")
    
    # Définir les variables d'environnement
    env = os.environ.copy()
    env['GESPARC_USE_PREFIX'] = 'true'
    env['SCRIPT_NAME'] = '/gesparc'
    
    try:
        # Démarrer le processus Flask en arrière-plan
        process = subprocess.Popen(
            [sys.executable, 'gesparc_app.py', '--prefix'],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Attendre que Flask démarre
        print("⏳ Attente du démarrage...")
        time.sleep(5)
        
        # Tester les URLs avec préfixe
        base_url = "http://localhost:5001"
        
        test_urls = [
            "/gesparc/",
            "/gesparc/vehicules",
            "/gesparc/test-prefix",
            "/gesparc/api/prefix-info",
            "/",  # Doit échouer
            "/vehicules"  # Doit échouer
        ]
        
        results = {}
        
        for url in test_urls:
            try:
                full_url = base_url + url
                print(f"📍 Test: {full_url}")
                
                response = requests.get(full_url, timeout=5)
                results[url] = {
                    'status': response.status_code,
                    'ok': response.ok,
                    'url': full_url
                }
                
                if url.startswith('/gesparc/'):
                    # URLs avec préfixe doivent fonctionner
                    if response.ok:
                        print(f"   ✅ {response.status_code} (attendu)")
                    else:
                        print(f"   ❌ {response.status_code} (problème)")
                else:
                    # URLs sans préfixe doivent échouer
                    if not response.ok:
                        print(f"   ✅ {response.status_code} (échec attendu)")
                    else:
                        print(f"   ⚠️ {response.status_code} (inattendu)")
                        
            except requests.exceptions.ConnectionError:
                print(f"   ❌ Connexion refusée")
                results[url] = {'status': 'Connection Error', 'ok': False, 'url': full_url}
            except Exception as e:
                print(f"   ❌ Erreur: {e}")
                results[url] = {'status': f'Error: {e}', 'ok': False, 'url': full_url}
        
        # Arrêter le processus Flask
        process.terminate()
        process.wait(timeout=5)
        
        return results
        
    except Exception as e:
        print(f"❌ Erreur lors du test préfixe: {e}")
        return {}

def test_api_prefix_info():
    """Teste l'API d'information du préfixe"""
    print("\n📊 Test API Prefix Info")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:5001/api/prefix-info", timeout=5)
        
        if response.ok:
            data = response.json()
            print("✅ API accessible")
            print(f"   Use Prefix: {data.get('use_prefix')}")
            print(f"   Prefix: {data.get('prefix')}")
            print(f"   Application Root: {data.get('application_root')}")
            print(f"   Script Name: {data.get('script_name')}")
            
            # Afficher quelques URLs
            urls = data.get('urls', {})
            print("\n📍 URLs générées:")
            for endpoint, url in urls.items():
                print(f"   {endpoint}: {url}")
                
            return data
        else:
            print(f"❌ API non accessible: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur API: {e}")
        return None

def test_wsgi_configuration():
    """Teste la configuration WSGI"""
    print("\n🔧 Test Configuration WSGI")
    print("=" * 35)
    
    wsgi_file = "gesparc.wsgi"
    
    if os.path.exists(wsgi_file):
        print(f"✅ Fichier WSGI trouvé: {wsgi_file}")
        
        with open(wsgi_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Vérifier les configurations importantes
        checks = [
            ("SCRIPT_NAME", "os.environ['SCRIPT_NAME'] = '/gesparc'"),
            ("GESPARC_USE_PREFIX", "os.environ['GESPARC_USE_PREFIX'] = 'true'"),
            ("APPLICATION_ROOT", "application.config['APPLICATION_ROOT'] = '/gesparc'")
        ]
        
        for check_name, check_pattern in checks:
            if check_pattern in content:
                print(f"   ✅ {check_name} configuré")
            else:
                print(f"   ❌ {check_name} manquant")
                
    else:
        print(f"❌ Fichier WSGI non trouvé: {wsgi_file}")

def create_apache_test_config():
    """Crée une configuration Apache de test"""
    print("\n📝 Génération Configuration Apache")
    print("=" * 40)
    
    config_content = """# Configuration Apache pour GesParc Auto avec préfixe /gesparc

# Option 1: WSGI (Recommandée)
LoadModule wsgi_module modules/mod_wsgi.so

WSGIScriptAlias /gesparc "c:/Apache24/htdocs/gesparc/gesparc.wsgi"
WSGIPythonPath "c:/Apache24/htdocs/gesparc"

<Directory "c:/Apache24/htdocs/gesparc">
    WSGIApplicationGroup %{GLOBAL}
    WSGIScriptReloading On
    Require all granted
    
    # Variables d'environnement
    SetEnv SCRIPT_NAME /gesparc
    SetEnv GESPARC_USE_PREFIX true
</Directory>

# Option 2: Proxy (Alternative)
# LoadModule proxy_module modules/mod_proxy.so
# LoadModule proxy_http_module modules/mod_proxy_http.so
# 
# ProxyPass /gesparc/ http://127.0.0.1:5001/gesparc/
# ProxyPassReverse /gesparc/ http://127.0.0.1:5001/gesparc/
# ProxyPreserveHost On

# Fichiers statiques
Alias /gesparc/static "c:/Apache24/htdocs/gesparc/static"
<Directory "c:/Apache24/htdocs/gesparc/static">
    Require all granted
    Header set Cache-Control "max-age=86400"
</Directory>

# Sécurité
<FilesMatch "\\.(py|pyc|pyo|db|wsgi|log)$">
    Require all denied
</FilesMatch>

# Logs
LogFormat "%h %l %u %t \\"%r\\" %>s %O \\"%{Referer}i\\" \\"%{User-Agent}i\\"" gesparc_combined
CustomLog "logs/gesparc_access.log" gesparc_combined
ErrorLog "logs/gesparc_error.log"
"""
    
    try:
        with open("apache_gesparc_prefix.conf", "w", encoding="utf-8") as f:
            f.write(config_content)
        print("✅ Configuration Apache générée: apache_gesparc_prefix.conf")
        print("💡 À inclure dans httpd.conf ou dans un fichier .conf séparé")
    except Exception as e:
        print(f"❌ Erreur génération config: {e}")

def main():
    """Fonction principale"""
    print("🧪 Test Complet Configuration Préfixe /gesparc")
    print("=" * 60)
    
    # Vérifier que Flask est démarré
    try:
        response = requests.get("http://localhost:5001/", timeout=3)
        if not response.ok:
            print("⚠️ Flask ne semble pas démarré en mode normal")
            print("💡 Démarrez d'abord: python gesparc_app.py")
            return
    except:
        print("❌ Flask n'est pas accessible sur le port 5001")
        print("💡 Démarrez d'abord: python gesparc_app.py")
        return
    
    # Tests
    normal_results = test_normal_mode()
    api_data = test_api_prefix_info()
    test_wsgi_configuration()
    create_apache_test_config()
    
    # Résumé
    print("\n" + "=" * 60)
    print("📋 Résumé des Tests")
    print("=" * 60)
    
    # Compter les succès en mode normal
    normal_success = sum(1 for r in normal_results.values() if r.get('ok', False))
    normal_total = len(normal_results)
    
    print(f"Mode Normal: {normal_success}/{normal_total} tests réussis")
    
    if api_data:
        print(f"Configuration actuelle:")
        print(f"  - Use Prefix: {api_data.get('use_prefix', 'Unknown')}")
        print(f"  - Prefix: {api_data.get('prefix', 'None')}")
        print(f"  - Application Root: {api_data.get('application_root', 'None')}")
    
    print("\n🎯 Prochaines étapes:")
    print("1. Tester en mode préfixe: python start_gesparc_prefix.py --prefix")
    print("2. Configurer Apache avec apache_gesparc_prefix.conf")
    print("3. Tester via Apache: http://localhost/gesparc/")
    
    print("\n📍 URLs de test:")
    print("- Mode normal: http://localhost:5001/test-prefix")
    print("- Mode préfixe: http://localhost:5001/gesparc/test-prefix")
    print("- Apache WSGI: http://localhost/gesparc/test-prefix")

if __name__ == "__main__":
    main()
