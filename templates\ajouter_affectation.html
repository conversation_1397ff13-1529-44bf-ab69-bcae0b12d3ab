{% extends "base.html" %}

{% block title %}Nouvelle Affectation - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-plus"></i> Nouvelle Affectation</h1>
            <a href="{{ url_for('affectations') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exchange-alt"></i> Affecter un véhicule à un conducteur
                </h5>
            </div>
            <div class="card-body">
                {% if not vehicules %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Aucun véhicule disponible</strong><br>
                    Tous les véhicules sont déjà affectés ou indisponibles.
                    <a href="{{ url_for('vehicules') }}" class="btn btn-sm btn-outline-primary ms-2">
                        Voir les véhicules
                    </a>
                </div>
                {% elif not conducteurs %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Aucun conducteur actif</strong><br>
                    Aucun conducteur actif n'est disponible pour une affectation.
                    <a href="{{ url_for('conducteurs') }}" class="btn btn-sm btn-outline-primary ms-2">
                        Voir les conducteurs
                    </a>
                </div>
                {% else %}
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Véhicule -->
                        <div class="col-md-6 mb-3">
                            <label for="vehicule_id" class="form-label">
                                <i class="fas fa-car"></i> Véhicule *
                            </label>
                            <select class="form-select" id="vehicule_id" name="vehicule_id" required>
                                <option value="">Sélectionner un véhicule</option>
                                {% for vehicule in vehicules %}
                                <option value="{{ vehicule.id }}"
                                        data-marque="{{ vehicule.marque }}"
                                        data-modele="{{ vehicule.modele }}"
                                        data-annee="{{ vehicule.annee }}"
                                        data-carburant="{{ vehicule.carburant }}"
                                        data-kilometrage="{{ vehicule.kilometrage }}">
                                    {{ vehicule.immatriculation }} - {{ vehicule.marque }} {{ vehicule.modele }} ({{ vehicule.annee }})
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner un véhicule
                            </div>
                            <div id="vehicule-info" class="mt-2" style="display: none;">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    <span id="vehicule-details"></span>
                                </small>
                            </div>
                        </div>

                        <!-- Conducteur -->
                        <div class="col-md-6 mb-3">
                            <label for="conducteur_id" class="form-label">
                                <i class="fas fa-user"></i> Conducteur *
                            </label>
                            <select class="form-select" id="conducteur_id" name="conducteur_id" required>
                                <option value="">Sélectionner un conducteur</option>
                                {% for conducteur in conducteurs %}
                                <option value="{{ conducteur.id }}"
                                        data-permis="{{ conducteur.numero_permis }}"
                                        data-telephone="{{ conducteur.telephone or '' }}"
                                        data-email="{{ conducteur.email or '' }}">
                                    {{ conducteur.prenom }} {{ conducteur.nom }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner un conducteur
                            </div>
                            <div id="conducteur-info" class="mt-2" style="display: none;">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    <span id="conducteur-details"></span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Date de début -->
                        <div class="col-md-6 mb-3">
                            <label for="date_debut" class="form-label">
                                <i class="fas fa-calendar-plus"></i> Date et heure de début *
                            </label>
                            <input type="datetime-local" class="form-control" id="date_debut"
                                   name="date_debut" required
                                   value="{{ datetime_debut_default }}"
                                   min="{{ datetime_debut_default }}">
                            <div class="invalid-feedback">
                                Veuillez saisir la date et heure de début
                            </div>
                            <small class="form-text text-muted">
                                Date et heure précises du début de l'affectation
                            </small>
                        </div>

                        <!-- Date de fin (optionnelle) -->
                        <div class="col-md-6 mb-3">
                            <label for="date_fin" class="form-label">
                                <i class="fas fa-calendar-minus"></i> Date et heure de fin (optionnelle)
                            </label>
                            <input type="datetime-local" class="form-control" id="date_fin"
                                   name="date_fin"
                                   value="{{ datetime_fin_default }}"
                                   min="{{ datetime_debut_default }}">
                            <small class="form-text text-muted">
                                Date et heure précises de fin prévue (laisser vide pour durée indéterminée)
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Kilométrage actuel -->
                        <div class="col-md-6 mb-3">
                            <label for="kilometrage_actuel" class="form-label">
                                <i class="fas fa-tachometer-alt"></i> Kilométrage actuel (km)
                            </label>
                            <input type="number" class="form-control" id="kilometrage_actuel"
                                   name="kilometrage_actuel" min="0" readonly
                                   placeholder="Sélectionnez un véhicule">
                            <small class="form-text text-muted">
                                Kilométrage automatiquement chargé depuis le véhicule sélectionné
                            </small>
                        </div>

                        <!-- Budget carburant -->
                        <div class="col-md-6 mb-3">
                            <label for="budget_carburant" class="form-label">
                                <i class="fas fa-gas-pump"></i> Budget carburant (MAD)
                                {% if solde_budget_carburant < config_budget_carburant.seuil_alerte %}
                                    <span class="badge bg-warning ms-2">⚠️ Solde faible</span>
                                {% endif %}
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="budget_carburant"
                                       name="budget_carburant" min="0" step="0.01"
                                       max="{{ solde_budget_carburant }}"
                                       placeholder="0.00">
                                <span class="input-group-text">MAD</span>
                            </div>
                            <div class="mt-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        Solde disponible: <strong class="{% if solde_budget_carburant >= config_budget_carburant.seuil_alerte %}text-success{% else %}text-warning{% endif %}">{{ '{:,.2f}'.format(solde_budget_carburant).replace(',', ' ') }} MAD</strong>
                                    </small>
                                    <a href="{{ url_for('budget_detail', type_budget='carburant') }}" 
                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-external-link-alt"></i> Voir budget
                                    </a>
                                </div>
                                {% if solde_budget_carburant < config_budget_carburant.seuil_alerte %}
                                <div class="alert alert-warning alert-sm mt-2 py-2">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <small>
                                        <strong>Attention :</strong> Le solde du budget carburant est inférieur au seuil d'alerte 
                                        ({{ '{:,.2f}'.format(config_budget_carburant.seuil_alerte).replace(',', ' ') }} MAD).
                                        <a href="{{ url_for('reapprovisionner_budget', type_budget='carburant') }}" 
                                           class="btn btn-sm btn-warning ms-2" target="_blank">
                                            <i class="fas fa-money-bill-wave"></i> Réapprovisionner
                                        </a>
                                    </small>
                                </div>
                                {% endif %}
                                <div class="btn-group btn-group-sm mt-2" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="setBudgetAmount(500)">500</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setBudgetAmount(1000)">1K</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setBudgetAmount(2000)">2K</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setBudgetAmount(5000)">5K</button>
                                    <button type="button" class="btn btn-outline-info" onclick="setBudgetAmount({{ solde_budget_carburant }})">Max</button>
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-lightbulb"></i> Ce montant sera automatiquement déduit du budget carburant global
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Mission -->
                        <div class="col-md-6 mb-3">
                            <label for="mission" class="form-label">
                                <i class="fas fa-bullseye"></i> Mission *
                            </label>
                            <input type="text" class="form-control" id="mission" name="mission"
                                   required maxlength="255"
                                   placeholder="Décrivez la mission ou l'objectif de cette affectation...">
                            <div class="invalid-feedback">
                                Veuillez saisir la mission de cette affectation
                            </div>
                            <small class="form-text text-muted">
                                Type de mission (livraison, déplacement, service, etc.)
                            </small>
                        </div>

                        <!-- Destination -->
                        <div class="col-md-6 mb-3">
                            <label for="destination" class="form-label">
                                <i class="fas fa-map-marker-alt"></i> Destination
                            </label>
                            <input type="text" class="form-control" id="destination" name="destination"
                                   maxlength="255"
                                   placeholder="Lieu de destination, adresse, ville...">
                            <small class="form-text text-muted">
                                Lieu de destination de la mission (optionnel)
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Commentaire -->
                        <div class="col-12 mb-3">
                            <label for="commentaire" class="form-label">
                                <i class="fas fa-comment"></i> Commentaire
                            </label>
                            <textarea class="form-control" id="commentaire" name="commentaire"
                                      rows="3" placeholder="Informations complémentaires, conditions particulières..."></textarea>
                            <small class="form-text text-muted">
                                Informations complémentaires sur l'affectation (optionnel)
                            </small>
                        </div>
                    </div>

                    <!-- Résumé de l'affectation -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info" id="resume-affectation" style="display: none;">
                                <h6><i class="fas fa-info-circle"></i> Résumé de l'affectation</h6>
                                <div id="resume-content"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Boutons -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('affectations') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Créer l'affectation
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistiques -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-light">
            <div class="card-body">
                <h6><i class="fas fa-car"></i> Véhicules disponibles</h6>
                <h4 class="text-primary">{{ vehicules|length }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-light">
            <div class="card-body">
                <h6><i class="fas fa-users"></i> Conducteurs actifs</h6>
                <h4 class="text-success">{{ conducteurs|length }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card {% if solde_budget_carburant >= config_budget_carburant.seuil_alerte %}bg-light{% else %}bg-warning{% endif %}">
            <div class="card-body">
                <h6><i class="fas fa-gas-pump"></i> Budget Carburant</h6>
                <h4 class="{% if solde_budget_carburant >= config_budget_carburant.seuil_alerte %}text-success{% else %}text-dark{% endif %}">
                    {{ '{:,.0f}'.format(solde_budget_carburant).replace(',', ' ') }} MAD
                </h4>
                <small class="text-muted">
                    {% if solde_budget_carburant >= config_budget_carburant.seuil_alerte %}
                        <i class="fas fa-check-circle text-success"></i> Solde suffisant
                    {% else %}
                        <i class="fas fa-exclamation-triangle text-warning"></i> Solde faible
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Affichage des détails du véhicule
    const vehiculeSelect = document.getElementById('vehicule_id');
    const vehiculeInfo = document.getElementById('vehicule-info');
    const vehiculeDetails = document.getElementById('vehicule-details');
    
    vehiculeSelect.addEventListener('change', function() {
        const option = this.options[this.selectedIndex];
        const kilometrageInput = document.getElementById('kilometrage_actuel');

        if (option.value) {
            const marque = option.dataset.marque;
            const modele = option.dataset.modele;
            const annee = option.dataset.annee;
            const carburant = option.dataset.carburant;
            const kilometrage = option.dataset.kilometrage;

            vehiculeDetails.innerHTML = `${marque} ${modele} (${annee}) - ${carburant}`;
            vehiculeInfo.style.display = 'block';

            // Charger automatiquement le kilométrage
            kilometrageInput.value = kilometrage || 0;
        } else {
            vehiculeInfo.style.display = 'none';
            kilometrageInput.value = '';
        }
        updateResume();
    });
    
    // Affichage des détails du conducteur
    const conducteurSelect = document.getElementById('conducteur_id');
    const conducteurInfo = document.getElementById('conducteur-info');
    const conducteurDetails = document.getElementById('conducteur-details');
    
    conducteurSelect.addEventListener('change', function() {
        const option = this.options[this.selectedIndex];
        if (option.value) {
            const permis = option.dataset.permis;
            const telephone = option.dataset.telephone;
            const email = option.dataset.email;
            
            let details = `Permis: ${permis}`;
            if (telephone) details += ` - Tél: ${telephone}`;
            if (email) details += ` - Email: ${email}`;
            
            conducteurDetails.innerHTML = details;
            conducteurInfo.style.display = 'block';
        } else {
            conducteurInfo.style.display = 'none';
        }
        updateResume();
    });
    
    // Validation des dates avec heures par défaut
    const dateDebut = document.getElementById('date_debut');
    const dateFin = document.getElementById('date_fin');

    // Fonction pour valider l'ordre des dates
    function validateDateOrder() {
        if (dateDebut.value && dateFin.value) {
            const debut = new Date(dateDebut.value);
            const fin = new Date(dateFin.value);

            if (debut >= fin) {
                dateFin.setCustomValidity('La date de fin doit être postérieure à la date de début');
                dateFin.classList.add('is-invalid');
                return false;
            } else {
                dateFin.setCustomValidity('');
                dateFin.classList.remove('is-invalid');
                dateFin.classList.add('is-valid');
                return true;
            }
        }
        return true;
    }

    dateDebut.addEventListener('change', function() {
        // Mettre à jour le minimum de la date de fin
        dateFin.min = this.value;

        // Si la date de fin n'est pas définie, proposer la même date avec 23:59
        if (!dateFin.value && this.value) {
            const dateDebutValue = this.value.split('T')[0]; // Extraire la partie date
            dateFin.value = dateDebutValue + 'T23:59';
        }

        validateDateOrder();
        updateResume();
    });

    dateFin.addEventListener('change', function() {
        validateDateOrder();
        updateResume();
    });

    // Mise à jour du résumé lors du changement des nouveaux champs
    document.getElementById('budget_carburant').addEventListener('input', updateResume);
    
    // Fonction pour définir le montant du budget carburant
    window.setBudgetAmount = function(amount) {
        const budgetInput = document.getElementById('budget_carburant');
        const maxAmount = {{ solde_budget_carburant }};
        
        if (amount > maxAmount) {
            budgetInput.value = maxAmount.toFixed(2);
            // Afficher une alerte temporaire
            const alert = document.createElement('div');
            alert.className = 'alert alert-warning alert-sm mt-2';
            alert.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Montant ajusté au solde disponible';
            budgetInput.parentNode.appendChild(alert);
            setTimeout(() => alert.remove(), 3000);
        } else {
            budgetInput.value = amount.toFixed(2);
        }
        
        // Effet visuel
        budgetInput.style.backgroundColor = '#e3f2fd';
        setTimeout(() => {
            budgetInput.style.backgroundColor = '';
        }, 500);
        
        updateResume();
    };

    // Mise à jour du résumé
    function updateResume() {
        const vehiculeOption = vehiculeSelect.options[vehiculeSelect.selectedIndex];
        const conducteurOption = conducteurSelect.options[conducteurSelect.selectedIndex];
        const debut = dateDebut.value;
        const fin = dateFin.value;
        const kilometrage = document.getElementById('kilometrage_actuel').value;
        const budget = document.getElementById('budget_carburant').value;

        if (vehiculeOption.value && conducteurOption.value && debut) {
            const resumeDiv = document.getElementById('resume-affectation');
            const resumeContent = document.getElementById('resume-content');

            let html = `
                <strong>Véhicule:</strong> ${vehiculeOption.text}<br>
                <strong>Conducteur:</strong> ${conducteurOption.text}<br>
                <strong>Période:</strong> Du ${debut}${fin ? ' au ' + fin : ' (durée indéterminée)'}<br>
                <strong>Kilométrage actuel:</strong> ${kilometrage ? kilometrage + ' km' : 'Non défini'}<br>
                <strong>Budget carburant:</strong> ${budget ? budget + ' MAD' : 'Non défini'}
            `;

            resumeContent.innerHTML = html;
            resumeDiv.style.display = 'block';
        } else {
            document.getElementById('resume-affectation').style.display = 'none';
        }
    }
    
    // Définir la date d'aujourd'hui par défaut
    const today = new Date().toISOString().split('T')[0];
    dateDebut.value = today;
    dateDebut.min = today;
    dateFin.min = today;
});
</script>
{% endblock %}
