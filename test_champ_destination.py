#!/usr/bin/env python3
"""
Test du nouveau champ Destination dans les affectations
"""

import sqlite3
from datetime import date

def test_champ_destination():
    """Test du champ Destination"""
    print("🧪 Test du Champ Destination dans les Affectations")
    print("=" * 55)
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect('parc_automobile.db')
        conn.row_factory = sqlite3.Row
        
        # Vérifier que la colonne destination existe
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(affectations)")
        columns = cursor.fetchall()
        
        destination_column = None
        for col in columns:
            if col[1] == 'destination':
                destination_column = col
                break
        
        if destination_column:
            print("✅ Colonne 'destination' trouvée dans la table affectations")
            print(f"   Type: {destination_column[2]}, Null: {'YES' if not destination_column[3] else 'NO'}")
        else:
            print("❌ Colonne 'destination' non trouvée")
            conn.close()
            return False
        
        # Tester l'insertion d'une affectation avec destination
        print(f"\n🔧 Test d'insertion d'affectation avec destination...")
        
        # Récupérer un véhicule disponible
        vehicule = conn.execute('''
            SELECT * FROM vehicules 
            WHERE statut = 'disponible' 
            LIMIT 1
        ''').fetchone()
        
        # Récupérer un conducteur actif
        conducteur = conn.execute('''
            SELECT * FROM conducteurs 
            WHERE statut = 'actif' 
            LIMIT 1
        ''').fetchone()
        
        if not vehicule:
            print("⚠️  Aucun véhicule disponible pour le test")
            # Créer un véhicule de test
            conn.execute('''
                INSERT INTO vehicules (immatriculation, marque, modele, annee, statut)
                VALUES ('TEST-DEST-001', 'Test', 'Destination', 2024, 'disponible')
            ''')
            vehicule = conn.execute('''
                SELECT * FROM vehicules WHERE immatriculation = 'TEST-DEST-001'
            ''').fetchone()
            print("✅ Véhicule de test créé")
        
        if not conducteur:
            print("⚠️  Aucun conducteur actif pour le test")
            # Créer un conducteur de test
            conn.execute('''
                INSERT INTO conducteurs (nom, prenom, statut)
                VALUES ('Test', 'Destination', 'actif')
            ''')
            conducteur = conn.execute('''
                SELECT * FROM conducteurs WHERE nom = 'Test' AND prenom = 'Destination'
            ''').fetchone()
            print("✅ Conducteur de test créé")
        
        # Données de test
        mission_test = "Livraison de matériel"
        destination_test = "Hôpital Mohammed V, Rabat"
        date_debut = date.today().strftime('%Y-%m-%dT00:00')
        date_fin = date.today().strftime('%Y-%m-%dT23:59')
        
        print(f"   Véhicule: {vehicule['immatriculation']}")
        print(f"   Conducteur: {conducteur['prenom']} {conducteur['nom']}")
        print(f"   Mission: {mission_test}")
        print(f"   Destination: {destination_test}")
        print(f"   Date début: {date_debut}")
        print(f"   Date fin: {date_fin}")
        
        # Insérer l'affectation avec mission et destination
        cursor.execute('''
            INSERT INTO affectations (vehicule_id, conducteur_id, date_debut, date_fin, statut, mission, destination)
            VALUES (?, ?, ?, ?, 'active', ?, ?)
        ''', (vehicule['id'], conducteur['id'], date_debut, date_fin, mission_test, destination_test))
        
        affectation_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ Affectation créée avec ID: {affectation_id}")
        
        # Vérifier que l'affectation a été créée avec la destination
        affectation = conn.execute('''
            SELECT a.*, v.immatriculation, c.prenom, c.nom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.id = ?
        ''', (affectation_id,)).fetchone()
        
        if affectation and affectation['destination'] == destination_test:
            print("✅ Destination correctement enregistrée")
            print(f"   Destination récupérée: {affectation['destination']}")
        else:
            print("❌ Erreur: Destination non enregistrée correctement")
            conn.close()
            return False
        
        # Test de récupération pour la liste des affectations
        print(f"\n📋 Test de récupération pour la liste...")
        
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele,
                   c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC, a.id DESC
            LIMIT 5
        ''').fetchall()
        
        print(f"✅ {len(affectations)} affectation(s) récupérée(s)")
        
        for aff in affectations:
            mission_display = aff['mission'] if aff['mission'] else 'Non définie'
            destination_display = aff['destination'] if aff['destination'] else 'Non spécifiée'
            print(f"   ID {aff['id']}: {aff['immatriculation']} - {mission_display} → {destination_display}")
        
        # Test de mise à jour d'une destination
        print(f"\n🔄 Test de mise à jour de destination...")
        
        nouvelle_destination = "Centre Hospitalier Universitaire, Casablanca"
        conn.execute('''
            UPDATE affectations SET destination = ? WHERE id = ?
        ''', (nouvelle_destination, affectation_id))
        conn.commit()
        
        # Vérifier la mise à jour
        affectation_maj = conn.execute('''
            SELECT destination FROM affectations WHERE id = ?
        ''', (affectation_id,)).fetchone()
        
        if affectation_maj['destination'] == nouvelle_destination:
            print("✅ Destination mise à jour avec succès")
            print(f"   Nouvelle destination: {affectation_maj['destination']}")
        else:
            print("❌ Erreur lors de la mise à jour")
        
        # Test avec destination vide/null
        print(f"\n🔍 Test avec destination vide...")
        
        conn.execute('''
            INSERT INTO affectations (vehicule_id, conducteur_id, date_debut, statut, mission, destination)
            VALUES (?, ?, ?, 'active', ?, ?)
        ''', (vehicule['id'], conducteur['id'], date_debut, "Mission sans destination", None))
        
        affectation_vide_id = cursor.lastrowid
        conn.commit()
        
        affectation_vide = conn.execute('''
            SELECT destination FROM affectations WHERE id = ?
        ''', (affectation_vide_id,)).fetchone()
        
        if affectation_vide['destination'] is None:
            print("✅ Destination NULL gérée correctement")
        else:
            print("❌ Problème avec destination NULL")
        
        # Test de différents types de destinations
        print(f"\n🗺️  Test de différents types de destinations...")
        
        destinations_test = [
            "Aéroport Mohammed V, Casablanca",
            "Gare ONCF, Rabat",
            "Université Hassan II, Mohammedia",
            "Zone Industrielle Ain Sebaa",
            "Port de Casablanca",
            "Technopolis Rabat",
            "Quartier Maarif, Casablanca"
        ]
        
        for i, dest in enumerate(destinations_test[:3]):  # Tester seulement 3 pour ne pas surcharger
            mission_dest = f"Mission test {i+1}"
            cursor.execute('''
                INSERT INTO affectations (vehicule_id, conducteur_id, date_debut, statut, mission, destination)
                VALUES (?, ?, ?, 'active', ?, ?)
            ''', (vehicule['id'], conducteur['id'], date_debut, mission_dest, dest))
            
            test_id = cursor.lastrowid
            print(f"   ✅ Test {i+1}: {mission_dest} → {dest}")
            
            # Nettoyer immédiatement
            conn.execute('DELETE FROM affectations WHERE id = ?', (test_id,))
        
        conn.commit()
        
        # Nettoyage des données de test
        print(f"\n🧹 Nettoyage des données de test...")
        
        conn.execute('DELETE FROM affectations WHERE id IN (?, ?)', (affectation_id, affectation_vide_id))
        
        # Supprimer les données de test si créées
        if vehicule['immatriculation'] == 'TEST-DEST-001':
            conn.execute('DELETE FROM vehicules WHERE id = ?', (vehicule['id'],))
        
        if conducteur['nom'] == 'Test' and conducteur['prenom'] == 'Destination':
            conn.execute('DELETE FROM conducteurs WHERE id = ?', (conducteur['id'],))
        
        conn.commit()
        print("✅ Données de test nettoyées")
        
        conn.close()
        
        # Résumé des tests
        print(f"\n📊 Résumé des Tests:")
        print(f"  ✅ Colonne 'destination' présente dans la base")
        print(f"  ✅ Insertion avec destination fonctionnelle")
        print(f"  ✅ Récupération de destination correcte")
        print(f"  ✅ Mise à jour de destination opérationnelle")
        print(f"  ✅ Gestion des destinations NULL/vides")
        print(f"  ✅ Différents types de destinations testés")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_interface_destination():
    """Test de l'interface avec le champ destination"""
    print(f"\n🎨 Test de l'Interface avec Destination:")
    print("-" * 45)
    
    try:
        # Vérifier que le champ destination est ajouté au formulaire
        with open('templates/ajouter_affectation.html', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Vérifications du formulaire
        checks = [
            ('name="destination"', 'Champ destination présent'),
            ('fas fa-map-marker-alt', 'Icône destination présente'),
            ('Destination', 'Label destination'),
            ('maxlength="255"', 'Limitation de longueur'),
            ('placeholder=', 'Placeholder informatif'),
            ('col-md-6', 'Disposition en colonnes avec mission')
        ]
        
        for check, description in checks:
            if check in contenu:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description} - manquant")
        
        # Vérifier la page de liste
        with open('templates/affectations.html', 'r', encoding='utf-8') as f:
            contenu_liste = f.read()
        
        if '<th>Destination</th>' in contenu_liste:
            print(f"  ✅ Colonne Destination ajoutée à la liste")
        else:
            print(f"  ❌ Colonne Destination manquante dans la liste")
        
        if 'affectation.destination' in contenu_liste:
            print(f"  ✅ Affichage de la destination dans la liste")
        else:
            print(f"  ❌ Affichage de la destination manquant")
        
        if 'fa-map-marker-alt' in contenu_liste:
            print(f"  ✅ Icône destination dans la liste")
        else:
            print(f"  ❌ Icône destination manquante")
        
        # Vérifier la page de détail
        with open('templates/detail_affectation.html', 'r', encoding='utf-8') as f:
            contenu_detail = f.read()
        
        if 'affectation.destination' in contenu_detail:
            print(f"  ✅ Destination affichée dans la page de détail")
        else:
            print(f"  ❌ Destination manquante dans la page de détail")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur test interface: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Test du champ Destination dans les affectations...")
    
    # Tests
    db_success = test_champ_destination()
    interface_success = test_interface_destination()
    
    # Résultat final
    print(f"\n" + "="*55)
    if db_success and interface_success:
        print("🎉 CHAMP DESTINATION IMPLÉMENTÉ AVEC SUCCÈS!")
        print("✅ Base de données mise à jour")
        print("✅ Formulaire d'ajout modifié")
        print("✅ Liste des affectations mise à jour")
        print("✅ Page de détail enrichie")
        print("🗺️  Le champ Destination est maintenant opérationnel")
    else:
        print("⚠️  PROBLÈME DÉTECTÉ")
        print("🔧 Vérifiez les erreurs ci-dessus")
