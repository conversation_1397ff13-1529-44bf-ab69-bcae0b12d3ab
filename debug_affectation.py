#!/usr/bin/env python3
import sqlite3

# Connexion à la base de données
conn = sqlite3.connect('parc_automobile.db')
cursor = conn.cursor()

print("🔍 Debug des affectations")
print("=" * 30)

# Lister toutes les affectations
cursor.execute('SELECT * FROM affectations ORDER BY id DESC LIMIT 5')
affectations = cursor.fetchall()

print(f"📋 Dernières affectations :")
for a in affectations:
    print(f"   ID: {a[0]} | Véhicule: {a[1]} | Conducteur: {a[2]} | Statut: {a[6]}")

# Chercher l'affectation active
cursor.execute('SELECT * FROM affectations WHERE statut = "active" ORDER BY id DESC LIMIT 1')
active = cursor.fetchone()

if active:
    print(f"\n✅ Affectation active trouvée:")
    print(f"   ID: {active[0]}")
    print(f"   Véhicule ID: {active[1]}")
    print(f"   Conducteur ID: {active[2]}")
    print(f"   Date début: {active[3]}")
    print(f"   Date fin: {active[4]}")
    print(f"   Statut: {active[6]}")
    print(f"   URL: http://localhost:8080/affectations/{active[0]}/terminer")
else:
    print("\n❌ Aucune affectation active")

conn.close()
print("\n🎉 Debug terminé !")