{% extends "base.html" %}

{% block title %}Configuration Budget - GesParc Auto{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-cog text-secondary"></i> Configuration du Budget
                </h1>
                <a href="{{ url_for('budget') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour au Budget
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h"></i> Paramètres du Budget
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Information :</strong> Ces paramètres définissent le comportement global 
                        du système de gestion budgétaire.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <!-- Budget initial -->
                            <div class="col-md-6 mb-4">
                                <label for="budget_initial" class="form-label">
                                    <i class="fas fa-piggy-bank text-success"></i> Budget Initial (MAD) *
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control form-control-lg" id="budget_initial" 
                                           name="budget_initial" min="0" step="0.01" required
                                           value="{{ config.budget_initial }}"
                                           placeholder="0.00">
                                    <span class="input-group-text">MAD</span>
                                </div>
                                <div class="invalid-feedback">
                                    Veuillez saisir le budget initial
                                </div>
                                <small class="form-text text-muted">
                                    <i class="fas fa-lightbulb"></i> Montant de référence pour les calculs budgétaires
                                </small>
                            </div>

                            <!-- Seuil d'alerte -->
                            <div class="col-md-6 mb-4">
                                <label for="seuil_alerte" class="form-label">
                                    <i class="fas fa-exclamation-triangle text-warning"></i> Seuil d'Alerte (MAD) *
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control form-control-lg" id="seuil_alerte" 
                                           name="seuil_alerte" min="0" step="0.01" required
                                           value="{{ config.seuil_alerte }}"
                                           placeholder="0.00">
                                    <span class="input-group-text">MAD</span>
                                </div>
                                <div class="invalid-feedback">
                                    Veuillez saisir le seuil d'alerte
                                </div>
                                <small class="form-text text-muted">
                                    <i class="fas fa-bell"></i> Montant en dessous duquel une alerte sera affichée
                                </small>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Période de début -->
                            <div class="col-md-6 mb-3">
                                <label for="periode_debut" class="form-label">
                                    <i class="fas fa-calendar-plus text-primary"></i> Début de Période
                                </label>
                                <input type="date" class="form-control" id="periode_debut"
                                       name="periode_debut"
                                       value="{{ config.periode_debut or '' }}">
                                <small class="form-text text-muted">
                                    Date de début de la période budgétaire (optionnel)
                                </small>
                            </div>

                            <!-- Période de fin -->
                            <div class="col-md-6 mb-3">
                                <label for="periode_fin" class="form-label">
                                    <i class="fas fa-calendar-minus text-danger"></i> Fin de Période
                                </label>
                                <input type="date" class="form-control" id="periode_fin"
                                       name="periode_fin"
                                       value="{{ config.periode_fin or '' }}">
                                <small class="form-text text-muted">
                                    Date de fin de la période budgétaire (optionnel)
                                </small>
                            </div>
                        </div>

                        <!-- Aperçu des paramètres -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-eye"></i> Aperçu des Paramètres
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <small class="text-muted">Budget Initial Actuel :</small><br>
                                            <strong class="text-success">{{ '{:,.2f}'.format(config.budget_initial).replace(',', ' ') }} MAD</strong>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">Seuil d'Alerte Actuel :</small><br>
                                            <strong class="text-warning">{{ '{:,.2f}'.format(config.seuil_alerte).replace(',', ' ') }} MAD</strong>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        {% if config.periode_debut and config.periode_fin %}
                                        <div class="mb-2">
                                            <small class="text-muted">Période Actuelle :</small><br>
                                            <strong>{{ config.periode_debut }} - {{ config.periode_fin }}</strong>
                                        </div>
                                        {% else %}
                                        <div class="mb-2">
                                            <small class="text-muted">Période :</small><br>
                                            <span class="text-muted">Non définie</span>
                                        </div>
                                        {% endif %}
                                        <div class="mb-2">
                                            <small class="text-muted">Dernière Modification :</small><br>
                                            <strong>{{ config.updated_at[:16].replace('T', ' ') }}</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Suggestions de configuration -->
                        <div class="card border-info mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-lightbulb"></i> Suggestions de Configuration
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Seuils d'Alerte Recommandés :</h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success"></i> 10% du budget initial : <strong>{{ '{:,.0f}'.format(config.budget_initial * 0.1).replace(',', ' ') }} MAD</strong></li>
                                            <li><i class="fas fa-check text-success"></i> 15% du budget initial : <strong>{{ '{:,.0f}'.format(config.budget_initial * 0.15).replace(',', ' ') }} MAD</strong></li>
                                            <li><i class="fas fa-check text-success"></i> 20% du budget initial : <strong>{{ '{:,.0f}'.format(config.budget_initial * 0.2).replace(',', ' ') }} MAD</strong></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Bonnes Pratiques :</h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-info-circle text-info"></i> Définir une période budgétaire claire</li>
                                            <li><i class="fas fa-info-circle text-info"></i> Réviser les seuils régulièrement</li>
                                            <li><i class="fas fa-info-circle text-info"></i> Surveiller les alertes</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('budget') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> Enregistrer la Configuration
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const budgetInitialInput = document.getElementById('budget_initial');
    const seuilAlerteInput = document.getElementById('seuil_alerte');
    
    // Validation en temps réel
    function validateSeuil() {
        const budgetInitial = parseFloat(budgetInitialInput.value) || 0;
        const seuilAlerte = parseFloat(seuilAlerteInput.value) || 0;
        
        if (seuilAlerte > budgetInitial) {
            seuilAlerteInput.setCustomValidity('Le seuil d\'alerte ne peut pas être supérieur au budget initial');
            seuilAlerteInput.classList.add('is-invalid');
        } else {
            seuilAlerteInput.setCustomValidity('');
            seuilAlerteInput.classList.remove('is-invalid');
        }
    }
    
    budgetInitialInput.addEventListener('input', validateSeuil);
    seuilAlerteInput.addEventListener('input', validateSeuil);
    
    // Validation des dates
    const periodeDebutInput = document.getElementById('periode_debut');
    const periodeFinInput = document.getElementById('periode_fin');
    
    function validateDates() {
        const dateDebut = new Date(periodeDebutInput.value);
        const dateFin = new Date(periodeFinInput.value);
        
        if (periodeDebutInput.value && periodeFinInput.value && dateDebut >= dateFin) {
            periodeFinInput.setCustomValidity('La date de fin doit être postérieure à la date de début');
            periodeFinInput.classList.add('is-invalid');
        } else {
            periodeFinInput.setCustomValidity('');
            periodeFinInput.classList.remove('is-invalid');
        }
    }
    
    periodeDebutInput.addEventListener('change', validateDates);
    periodeFinInput.addEventListener('change', validateDates);
});
</script>
{% endblock %}