#!/usr/bin/env python3
import sqlite3

# Connexion à la base de données
conn = sqlite3.connect('parc_automobile.db')
cursor = conn.cursor()

print("🔍 Vérification des ressources disponibles")
print("=" * 50)

# Vérifier les véhicules
cursor.execute('SELECT id, immatriculation, marque, modele, statut FROM vehicules')
vehicules = cursor.fetchall()

print(f"🚗 Véhicules ({len(vehicules)}) :")
for v in vehicules:
    print(f"   ID: {v[0]} | {v[1]} - {v[2]} {v[3]} | Statut: {v[4]}")

print()

# Vérifier les conducteurs
cursor.execute('SELECT id, nom, prenom, statut FROM conducteurs')
conducteurs = cursor.fetchall()

print(f"👤 Conducteurs ({len(conducteurs)}) :")
for c in conducteurs:
    print(f"   ID: {c[0]} | {c[2]} {c[1]} | Statut: {c[3]}")

print()

# Vérifier les affectations actives
cursor.execute('SELECT id, vehicule_id, conducteur_id, statut FROM affectations WHERE statut = "active"')
affectations = cursor.fetchall()

print(f"📋 Affectations actives ({len(affectations)}) :")
for a in affectations:
    print(f"   ID: {a[0]} | Véhicule: {a[1]} | Conducteur: {a[2]} | Statut: {a[3]}")

conn.close()
print("\n🎉 Vérification terminée !")