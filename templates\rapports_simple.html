{% extends "base.html" %}

{% block title %}Rapports et Statistiques - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-chart-bar"></i> Rapports et Statistiques</h1>
    </div>
</div>

<!-- Statistiques générales -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ stats.total_vehicules or 0 }}</h3>
                <p class="mb-0">Véhicules</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ stats.total_conducteurs or 0 }}</h3>
                <p class="mb-0">Conducteurs</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <h3>{{ stats.total_maintenances or 0 }}</h3>
                <p class="mb-0">Maintenances</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ stats.total_affectations or 0 }}</h3>
                <p class="mb-0">Affectations</p>
            </div>
        </div>
    </div>
</div>

<!-- Test simple -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>Test des données</h5>
            </div>
            <div class="card-body">
                <p>Véhicules par statut :</p>
                <ul>
                {% for item in vehicules_par_statut %}
                    <li>{{ item.statut }}: {{ item.count }}</li>
                {% endfor %}
                </ul>
                
                <p>Véhicules par carburant :</p>
                <ul>
                {% for item in vehicules_par_carburant %}
                    <li>{{ item.carburant }}: {{ item.count }}</li>
                {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
