#!/usr/bin/env python3
"""
Script de test pour vérifier la configuration /gesparc
"""

import requests
import sys

def test_gesparc_context():
    """Teste l'accès à GesParc Auto dans le contexte /gesparc"""
    print("🔍 Test de la configuration /gesparc")
    print("=" * 50)
    
    base_urls = [
        "http://localhost/gesparc",
        "http://localhost:5001"
    ]
    
    test_paths = [
        "/",
        "/vehicules",
        "/conducteurs", 
        "/maintenances",
        "/affectations",
        "/rapports"
    ]
    
    for base_url in base_urls:
        print(f"\n📍 Test de {base_url}")
        print("-" * 30)
        
        for path in test_paths:
            full_url = base_url + path
            try:
                response = requests.get(full_url, timeout=5, allow_redirects=True)
                status = "✅" if response.status_code == 200 else "❌"
                print(f"{status} {path:<15} → {response.status_code}")
                
                # Vérifier si c'est une redirection
                if response.history:
                    print(f"   ↳ Redirigé vers: {response.url}")
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ {path:<15} → Erreur: {e}")
    
    # Test spécifique des exports
    print(f"\n📊 Test des exports")
    print("-" * 20)
    
    export_tests = [
        "/export/vehicules/csv",
        "/export/conducteurs/csv", 
        "/export/maintenances/csv",
        "/export/affectations/csv"
    ]
    
    for export_path in export_tests:
        for base_url in base_urls:
            full_url = base_url + export_path
            try:
                response = requests.get(full_url, timeout=5)
                status = "✅" if response.status_code == 200 else "❌"
                print(f"{status} {export_path:<25} → {response.status_code}")
                break  # Si ça marche avec une URL, pas besoin de tester l'autre
            except:
                continue
    
    print("\n" + "=" * 50)
    print("🏁 Tests terminés")

def test_url_generation():
    """Teste la génération d'URLs Flask"""
    print("\n🔗 Test de génération d'URLs")
    print("-" * 30)
    
    try:
        # Importer l'app Flask
        import os
        os.chdir("c:/Apache24/htdocs/gesparc")
        
        from gesparc_app import gesparc_app
        
        with gesparc_app.test_request_context('/gesparc/'):
            from flask import url_for
            
            test_routes = [
                ('index', {}),
                ('vehicules', {}),
                ('conducteurs', {}),
                ('ajouter_vehicule', {}),
                ('export_vehicules', {'format': 'csv'})
            ]
            
            for route_name, kwargs in test_routes:
                try:
                    url = url_for(route_name, **kwargs)
                    print(f"✅ {route_name:<20} → {url}")
                except Exception as e:
                    print(f"❌ {route_name:<20} → Erreur: {e}")
                    
    except Exception as e:
        print(f"❌ Erreur lors du test d'URLs: {e}")

if __name__ == "__main__":
    test_gesparc_context()
    test_url_generation()
