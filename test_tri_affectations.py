#!/usr/bin/env python3
"""
Test du tri des affectations par date de début décroissante
"""

import sqlite3
from datetime import datetime

def test_tri_affectations():
    """Test du tri des affectations"""
    print("🧪 Test du Tri des Affectations par Date de Début")
    print("=" * 55)
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect('parc_automobile.db')
        conn.row_factory = sqlite3.Row
        
        # Exécuter la même requête que dans l'application
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele,
                   c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC
        ''').fetchall()
        
        if not affectations:
            print("⚠️  Aucune affectation trouvée")
            conn.close()
            return False
        
        print(f"✅ {len(affectations)} affectation(s) trouvée(s)")
        print(f"\n📋 Ordre des affectations (par date de début décroissante):")
        print("-" * 80)
        print(f"{'ID':<4} {'Date Début':<12} {'Véhicule':<12} {'Conducteur':<20} {'Statut':<10}")
        print("-" * 80)
        
        dates_precedentes = []
        tri_correct = True
        
        for i, affectation in enumerate(affectations):
            # Afficher les informations
            date_debut = affectation['date_debut'] or 'Non définie'
            vehicule = affectation['immatriculation']
            conducteur = f"{affectation['prenom']} {affectation['nom']}"
            statut = affectation['statut']
            
            print(f"{affectation['id']:<4} {date_debut:<12} {vehicule:<12} {conducteur:<20} {statut:<10}")
            
            # Vérifier l'ordre chronologique
            if affectation['date_debut']:
                dates_precedentes.append(affectation['date_debut'])
                
                # Vérifier que les dates sont en ordre décroissant
                if len(dates_precedentes) > 1:
                    date_actuelle = datetime.strptime(affectation['date_debut'], '%Y-%m-%d')
                    date_precedente = datetime.strptime(dates_precedentes[-2], '%Y-%m-%d')
                    
                    if date_actuelle > date_precedente:
                        tri_correct = False
                        print(f"    ❌ ERREUR: Date {affectation['date_debut']} > {dates_precedentes[-2]}")
        
        print("-" * 80)
        
        # Résultat du test de tri
        if tri_correct:
            print("✅ TRI CORRECT: Les affectations sont bien triées par date décroissante")
        else:
            print("❌ TRI INCORRECT: L'ordre des dates n'est pas respecté")
        
        # Statistiques sur les dates
        print(f"\n📊 Analyse des dates:")
        dates_valides = [aff['date_debut'] for aff in affectations if aff['date_debut']]
        
        if dates_valides:
            date_plus_recente = max(dates_valides)
            date_plus_ancienne = min(dates_valides)
            
            print(f"  📅 Date la plus récente: {date_plus_recente}")
            print(f"  📅 Date la plus ancienne: {date_plus_ancienne}")
            print(f"  📈 Nombre d'affectations avec date: {len(dates_valides)}")
            print(f"  ⚠️  Affectations sans date: {len(affectations) - len(dates_valides)}")
        
        # Test avec différents critères de tri
        print(f"\n🔄 Test d'autres critères de tri:")
        
        # Tri par ID décroissant
        affectations_id = conn.execute('''
            SELECT a.id, a.date_debut, v.immatriculation
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            ORDER BY a.id DESC
            LIMIT 5
        ''').fetchall()
        
        print(f"  📋 Tri par ID décroissant (5 premiers):")
        for aff in affectations_id:
            print(f"    ID: {aff['id']}, Date: {aff['date_debut'] or 'N/A'}, Véhicule: {aff['immatriculation']}")
        
        # Tri par statut puis date
        affectations_statut = conn.execute('''
            SELECT a.id, a.date_debut, a.statut, v.immatriculation
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            ORDER BY a.statut DESC, a.date_debut DESC
            LIMIT 5
        ''').fetchall()
        
        print(f"  📋 Tri par statut puis date (5 premiers):")
        for aff in affectations_statut:
            print(f"    ID: {aff['id']}, Statut: {aff['statut']}, Date: {aff['date_debut'] or 'N/A'}")
        
        conn.close()
        
        # Recommandations
        print(f"\n💡 Recommandations:")
        if tri_correct:
            print("  ✅ Le tri actuel fonctionne correctement")
            print("  📋 Les affectations les plus récentes apparaissent en premier")
            print("  🎯 L'interface utilisateur reflète l'ordre chronologique")
        else:
            print("  🔧 Vérifier la requête SQL dans gesparc_app.py")
            print("  📋 S'assurer que ORDER BY a.date_debut DESC est présent")
            print("  ⚠️  Vérifier les données de test")
        
        print(f"\n📝 Informations techniques:")
        print(f"  🔍 Requête utilisée: ORDER BY a.date_debut DESC")
        print(f"  📊 Tri: Date de début décroissante (plus récent → plus ancien)")
        print(f"  🎯 Objectif: Affectations récentes en haut de liste")
        
        return tri_correct
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_tri():
    """Test de performance du tri"""
    print(f"\n⚡ Test de Performance du Tri:")
    print("-" * 35)
    
    try:
        import time
        conn = sqlite3.connect('parc_automobile.db')
        
        # Mesurer le temps d'exécution
        start_time = time.time()
        
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele,
                   c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC
        ''').fetchall()
        
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000  # en millisecondes
        
        print(f"  📊 Nombre d'affectations: {len(affectations)}")
        print(f"  ⏱️  Temps d'exécution: {execution_time:.2f} ms")
        
        if execution_time < 100:
            print(f"  ✅ Performance excellente (< 100ms)")
        elif execution_time < 500:
            print(f"  ✅ Performance bonne (< 500ms)")
        else:
            print(f"  ⚠️  Performance à surveiller (> 500ms)")
        
        conn.close()
        
        # Suggestions d'optimisation
        print(f"\n🚀 Suggestions d'optimisation:")
        print(f"  📈 Ajouter un index sur date_debut si nécessaire")
        print(f"  🔄 Pagination pour grandes quantités de données")
        print(f"  💾 Cache pour requêtes fréquentes")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur test performance: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Démarrage du test de tri des affectations...")
    
    # Test principal
    tri_success = test_tri_affectations()
    
    # Test de performance
    perf_success = test_performance_tri()
    
    # Résultat final
    print(f"\n" + "="*55)
    if tri_success and perf_success:
        print("🎉 TRI DES AFFECTATIONS VALIDÉ!")
        print("✅ Les affectations sont correctement triées par date décroissante.")
        print("⚡ Performance satisfaisante.")
        print("📋 L'interface affiche les affectations récentes en premier.")
    else:
        print("⚠️  PROBLÈME DÉTECTÉ DANS LE TRI")
        print("🔧 Vérifiez la requête SQL et les données.")
