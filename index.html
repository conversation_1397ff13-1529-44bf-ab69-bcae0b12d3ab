<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GesParc Auto - Redirection</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        // Vérifier si Flask est accessible et rediriger
        function checkFlaskAndRedirect() {
            fetch('http://localhost:5001/')
                .then(response => {
                    if (response.ok) {
                        // Flask fonctionne, rediriger
                        window.location.href = 'http://localhost:5001/';
                    } else {
                        showFlaskNotRunning();
                    }
                })
                .catch(error => {
                    showFlaskNotRunning();
                });
        }
        
        function showFlaskNotRunning() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
        }
        
        // Vérifier au chargement de la page
        window.onload = function() {
            setTimeout(checkFlaskAndRedirect, 1000);
        };
    </script>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                
                <!-- Loading -->
                <div id="loading" class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-car"></i> GesParc Auto
                        </h3>
                    </div>
                    <div class="card-body text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <h5>Connexion à l'application...</h5>
                        <p class="text-muted">Redirection automatique vers GesParc Auto</p>
                    </div>
                </div>
                
                <!-- Error -->
                <div id="error" class="card" style="display: none;">
                    <div class="card-header bg-warning text-dark">
                        <h3 class="mb-0">
                            <i class="fas fa-exclamation-triangle"></i> Application Non Démarrée
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <strong>L'application Flask GesParc Auto n'est pas accessible.</strong>
                        </div>
                        
                        <h5>Pour démarrer l'application :</h5>
                        <ol>
                            <li>Ouvrez un terminal/invite de commandes</li>
                            <li>Naviguez vers : <code>cd c:\Apache24\htdocs\gesparc</code></li>
                            <li>Lancez : <code>python gesparc_app.py</code></li>
                            <li>Rafraîchissez cette page</li>
                        </ol>
                        
                        <div class="mt-4">
                            <button onclick="location.reload()" class="btn btn-primary">
                                <i class="fas fa-refresh"></i> Vérifier à nouveau
                            </button>
                            <a href="http://localhost:5001" class="btn btn-outline-primary" target="_blank">
                                <i class="fas fa-external-link-alt"></i> Accès direct (port 5001)
                            </a>
                        </div>
                        
                        <hr>
                        <h6>Scripts de démarrage disponibles :</h6>
                        <ul>
                            <li><code>start_gesparc.bat</code> - Script Windows</li>
                            <li><code>start_gesparc.ps1</code> - Script PowerShell</li>
                        </ul>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</body>
</html>
