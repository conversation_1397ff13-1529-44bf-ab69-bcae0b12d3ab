# 🌐 Configuration Apache pour GesParc Auto

## 🎯 Configuration Actuelle

L'application GesParc Auto est configurée pour fonctionner dans le contexte `/gesparc` du serveur Apache.

### 📁 Structure des Fichiers

```
c:/Apache24/htdocs/gesparc/
├── gesparc_app.py              # Application Flask principale
├── config.py                   # Configuration pour différents environnements
├── proxy.php                   # Proxy PHP vers Flask
├── index.php                   # Page d'accueil avec redirection
├── .htaccess                   # Configuration Apache
├── gesparc.wsgi               # Configuration WSGI (alternative)
├── start_gesparc_apache.py    # Script de démarrage pour Apache
├── run_app.py                 # Script de démarrage simple
├── test_gesparc_context.py    # Script de test
└── templates/                 # Templates HTML
```

### 🔧 Méthodes de Déploiement

#### 1. **Méthode Actuelle : Proxy PHP** ✅
- **Avantages** : Simple, pas besoin de mod_wsgi
- **Fonctionnement** : Apache → PHP → Flask
- **Configuration** : `.htaccess` + `proxy.php`

#### 2. **Méthode Alternative : WSGI** (Optionnelle)
- **Avantages** : Plus performant, intégration native
- **Prérequis** : mod_wsgi installé
- **Configuration** : `gesparc.wsgi` + configuration Apache

### 🚀 Démarrage de l'Application

#### Option 1 : Script Apache
```bash
cd c:\Apache24\htdocs\gesparc
python start_gesparc_apache.py
```

#### Option 2 : Script Simple
```bash
cd c:\Apache24\htdocs\gesparc
python run_app.py
```

#### Option 3 : Direct
```bash
cd c:\Apache24\htdocs\gesparc
python gesparc_app.py
```

### 🌐 URLs d'Accès

#### Via Apache (Recommandé)
- **Base** : `http://localhost/gesparc`
- **Véhicules** : `http://localhost/gesparc/vehicules`
- **Conducteurs** : `http://localhost/gesparc/conducteurs`
- **Maintenances** : `http://localhost/gesparc/maintenances`
- **Affectations** : `http://localhost/gesparc/affectations`
- **Rapports** : `http://localhost/gesparc/rapports`

#### Direct Flask (Développement)
- **Base** : `http://localhost:5001`
- **Toutes les pages** : `http://localhost:5001/[page]`

### ⚙️ Configuration Technique

#### Variables d'Environnement
```bash
SCRIPT_NAME=/gesparc
APPLICATION_ROOT=/gesparc
FLASK_ENV=production
```

#### Configuration Flask
```python
# Détection automatique du contexte
if os.environ.get('SCRIPT_NAME'):
    app.config['APPLICATION_ROOT'] = '/gesparc'
```

#### Configuration Apache (.htaccess)
```apache
DirectoryIndex proxy.php index.php
RewriteEngine On
RewriteBase /gesparc/

# Redirection vers proxy.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ proxy.php [L,QSA]
```

### 🔍 Tests et Validation

#### Script de Test
```bash
python test_gesparc_context.py
```

#### Tests Manuels
1. **Page d'accueil** : `http://localhost/gesparc`
2. **Sous-pages** : `http://localhost/gesparc/vehicules`
3. **Exports** : `http://localhost/gesparc/export/vehicules/csv`

### 🛠️ Dépannage

#### Problème : 404 sur les sous-pages
**Cause** : Configuration .htaccess incorrecte
**Solution** :
1. Vérifier que `proxy.php` existe
2. Contrôler les permissions du dossier
3. Tester la réécriture d'URL

#### Problème : Flask non accessible
**Cause** : Application Flask non démarrée
**Solution** :
1. Lancer `python start_gesparc_apache.py`
2. Vérifier le port 5001
3. Consulter les logs

#### Problème : Erreur 500
**Cause** : Erreur PHP ou Flask
**Solution** :
1. Vérifier les logs Apache
2. Tester l'accès direct Flask
3. Contrôler la configuration PHP

### 📊 Performance

#### Proxy PHP
- **Latence** : +10-50ms par requête
- **Mémoire** : Faible impact
- **CPU** : Léger overhead

#### Optimisations Possibles
1. **Cache PHP** : Activer OPcache
2. **Keep-Alive** : Réutiliser les connexions
3. **WSGI** : Migration vers mod_wsgi

### 🔒 Sécurité

#### Protections Mises en Place
```apache
# Bloquer l'accès aux fichiers sensibles
RewriteCond %{REQUEST_URI} \.(py|db|wsgi|log)$ [NC]
RewriteRule ^.*$ - [F,L]
```

#### Recommandations
1. **HTTPS** : Utiliser SSL en production
2. **Firewall** : Limiter l'accès au port 5001
3. **Logs** : Surveiller les accès
4. **Backup** : Sauvegarder la base de données

### 📈 Monitoring

#### Logs à Surveiller
- **Apache Access** : `c:\Apache24\logs\access.log`
- **Apache Error** : `c:\Apache24\logs\error.log`
- **Flask** : Console de l'application

#### Métriques Importantes
- Temps de réponse
- Erreurs 404/500
- Utilisation mémoire Flask
- Connexions simultanées

### 🔄 Maintenance

#### Mise à Jour de l'Application
1. Arrêter Flask (`Ctrl+C`)
2. Mettre à jour les fichiers
3. Redémarrer Flask
4. Tester les fonctionnalités

#### Sauvegarde
```bash
# Base de données
copy parc_automobile.db parc_automobile_backup.db

# Configuration
copy .htaccess .htaccess.backup
copy proxy.php proxy.php.backup
```

### 🎯 Statut Actuel

#### ✅ Fonctionnel
- Page d'accueil via Apache
- Application Flask complète
- Exports CSV/Excel
- Interface responsive

#### ⚠️ En Cours
- Optimisation du proxy PHP
- Tests des sous-pages via Apache
- Configuration WSGI alternative

#### 📋 À Faire
- Tests de charge
- Optimisation des performances
- Documentation utilisateur
- Formation équipe

---

**Configuration testée avec :**
- Apache 2.4
- PHP 7.4+
- Python 3.8+
- Flask 2.3+
- Windows Server/Desktop
