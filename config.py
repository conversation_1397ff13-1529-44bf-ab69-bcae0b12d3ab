#!/usr/bin/env python3
"""
Configuration pour GesParc Auto
"""

import os

class Config:
    """Configuration de base"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'gesparc_secret_key_2025'
    DATABASE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'parc_automobile.db')
    
    # Configuration pour le déploiement
    APPLICATION_ROOT = os.environ.get('APPLICATION_ROOT', '/')
    PREFERRED_URL_SCHEME = 'http'
    
class DevelopmentConfig(Config):
    """Configuration pour le développement"""
    DEBUG = True
    HOST = '127.0.0.1'
    PORT = 5001
    APPLICATION_ROOT = '/'

class ProductionConfig(Config):
    """Configuration pour la production (Apache)"""
    DEBUG = False
    APPLICATION_ROOT = '/gesparc'
    PREFERRED_URL_SCHEME = 'http'

class ApacheConfig(ProductionConfig):
    """Configuration spécifique pour Apache"""
    # Variables d'environnement Apache
    SCRIPT_NAME = '/gesparc'
    
    @staticmethod
    def init_app(app):
        """Initialisation spécifique pour Apache"""
        # Configurer le préfixe URL
        app.config['APPLICATION_ROOT'] = '/gesparc'
        
        # Middleware pour gérer le préfixe
        from werkzeug.middleware.proxy_fix import ProxyFix
        app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)

# Configuration par défaut selon l'environnement
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'apache': ApacheConfig,
    'default': DevelopmentConfig
}

def get_config():
    """Retourne la configuration appropriée selon l'environnement"""
    env = os.environ.get('FLASK_ENV', 'development')
    
    # Détecter si on est sous Apache
    if os.environ.get('SCRIPT_NAME') or os.environ.get('APPLICATION_ROOT'):
        return config['apache']
    
    return config.get(env, config['default'])
