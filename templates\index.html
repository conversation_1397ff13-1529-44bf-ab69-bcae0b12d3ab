{% extends "base.html" %}

{% block title %}Tableau de bord - Gestion Parc Automobile{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Tableau de bord
        </h1>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_vehicules or 0 }}</h4>
                        <p class="card-text">Véhicules</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-car fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('vehicules') }}" class="text-white text-decoration-none">
                    Voir détails <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.vehicules_disponibles or 0 }}</h4>
                        <p class="card-text">Disponibles</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_conducteurs or 0 }}</h4>
                        <p class="card-text">Conducteurs</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('conducteurs') }}" class="text-white text-decoration-none">
                    Voir détails <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.maintenances_prevues or 0 }}</h4>
                        <p class="card-text">Maintenances prévues</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tools fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('maintenances') }}" class="text-white text-decoration-none">
                    Voir détails <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt"></i> Actions rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('ajouter_vehicule') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus"></i> Nouveau véhicule
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('ajouter_conducteur') }}" class="btn btn-success w-100">
                            <i class="fas fa-user-plus"></i> Nouveau conducteur
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('ajouter_maintenance') }}" class="btn btn-warning w-100">
                            <i class="fas fa-wrench"></i> Planifier maintenance
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('rapports') }}" class="btn btn-info w-100">
                            <i class="fas fa-chart-line"></i> Voir rapports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alertes et notifications -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Alertes
                </h5>
            </div>
            <div class="card-body">
                {% if alertes %}
                    {% for alerte in alertes %}
                        <div class="alert alert-{{ alerte.type }} alert-sm">
                            {{ alerte.message }}
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">Aucune alerte pour le moment.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt"></i> Maintenances à venir
                </h5>
            </div>
            <div class="card-body">
                {% if maintenances_prochaines %}
                    {% for maintenance in maintenances_prochaines %}
                        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                            <div>
                                <strong>{{ maintenance.vehicule_immat }}</strong><br>
                                <small class="text-muted">{{ maintenance.type_maintenance }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-warning">{{ maintenance.date_maintenance }}</span>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">Aucune maintenance prévue.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
