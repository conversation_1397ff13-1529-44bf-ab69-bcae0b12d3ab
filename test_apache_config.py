#!/usr/bin/env python3
"""
Script de test pour vérifier la configuration Apache de GesParc Auto
"""

import requests
import time
import subprocess
import sys
import os

def check_apache_running():
    """Vérifier si Apache est en cours d'exécution"""
    try:
        response = requests.get("http://localhost", timeout=3)
        return True
    except:
        return False

def check_flask_running():
    """Vérifier si Flask est en cours d'exécution"""
    try:
        response = requests.get("http://localhost:5001", timeout=3)
        return response.status_code == 200
    except:
        return False

def test_url(url, description):
    """Tester une URL spécifique"""
    try:
        response = requests.get(url, timeout=10, allow_redirects=True)
        status = "✅" if response.status_code == 200 else "❌"
        print(f"{status} {description:<30} → {response.status_code}")
        
        if response.history:
            print(f"   ↳ Redirigé depuis: {response.history[0].url}")
            print(f"   ↳ Vers: {response.url}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"❌ {description:<30} → Erreur: {e}")
        return False

def main():
    print("🔍 Test de Configuration Apache pour GesParc Auto")
    print("=" * 60)
    
    # 1. Vérifier Apache
    print("\n1. Vérification d'Apache...")
    if check_apache_running():
        print("   ✅ Apache est en cours d'exécution")
    else:
        print("   ❌ Apache n'est pas accessible")
        print("   💡 Démarrez Apache et réessayez")
        return False
    
    # 2. Vérifier Flask
    print("\n2. Vérification de Flask...")
    flask_running = check_flask_running()
    if flask_running:
        print("   ✅ Flask est en cours d'exécution sur le port 5001")
    else:
        print("   ❌ Flask n'est pas accessible")
        print("   💡 Démarrez Flask avec: python gesparc_app.py")
    
    # 3. Tests des URLs
    print("\n3. Test des URLs...")
    
    # URLs à tester
    test_urls = [
        ("http://localhost/gesparc", "Page d'accueil via Apache"),
        ("http://localhost/gesparc/", "Page d'accueil avec slash"),
        ("http://localhost/gesparc/vehicules", "Page véhicules via Apache"),
        ("http://localhost/gesparc/conducteurs", "Page conducteurs via Apache"),
        ("http://localhost/gesparc/maintenances", "Page maintenances via Apache"),
        ("http://localhost/gesparc/affectations", "Page affectations via Apache"),
        ("http://localhost/gesparc/rapports", "Page rapports via Apache"),
    ]
    
    success_count = 0
    for url, description in test_urls:
        if test_url(url, description):
            success_count += 1
    
    # 4. Tests des exports (si Flask fonctionne)
    if flask_running:
        print("\n4. Test des exports...")
        export_urls = [
            ("http://localhost/gesparc/export/vehicules/csv", "Export véhicules CSV"),
            ("http://localhost/gesparc/export/conducteurs/csv", "Export conducteurs CSV"),
        ]
        
        for url, description in export_urls:
            if test_url(url, description):
                success_count += 1
    
    # 5. Tests directs Flask (pour comparaison)
    if flask_running:
        print("\n5. Test direct Flask (comparaison)...")
        flask_urls = [
            ("http://localhost:5001/", "Page d'accueil Flask direct"),
            ("http://localhost:5001/vehicules", "Page véhicules Flask direct"),
        ]
        
        for url, description in flask_urls:
            test_url(url, description)
    
    # 6. Résumé
    print("\n" + "=" * 60)
    total_tests = len(test_urls)
    if flask_running:
        total_tests += 2  # exports
    
    print(f"📊 Résultats: {success_count}/{total_tests} tests réussis")
    
    if success_count == total_tests:
        print("🎉 Configuration Apache parfaitement fonctionnelle !")
        print("📍 Accédez à votre application: http://localhost/gesparc")
    elif success_count > 0:
        print("⚠️ Configuration partiellement fonctionnelle")
        print("💡 Vérifiez les erreurs ci-dessus")
    else:
        print("❌ Configuration non fonctionnelle")
        print("💡 Vérifiez la configuration Apache et Flask")
    
    return success_count == total_tests

def check_apache_config():
    """Vérifier la configuration Apache"""
    print("\n🔧 Vérification de la configuration Apache...")
    
    apache_dir = "c:\\Apache24"
    httpd_conf = f"{apache_dir}\\conf\\httpd.conf"
    
    if not os.path.exists(httpd_conf):
        print(f"❌ Fichier httpd.conf non trouvé: {httpd_conf}")
        return False
    
    # Vérifier les modules
    with open(httpd_conf, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    modules_to_check = [
        "mod_rewrite",
        "mod_proxy", 
        "mod_proxy_http"
    ]
    
    for module in modules_to_check:
        if f"LoadModule {module}" in content:
            print(f"   ✅ {module} activé")
        else:
            print(f"   ❌ {module} non activé")
    
    # Vérifier l'inclusion de gesparc-apache.conf
    if "gesparc-apache.conf" in content:
        print("   ✅ Configuration GesParc incluse")
    else:
        print("   ❌ Configuration GesParc non incluse")
    
    return True

if __name__ == "__main__":
    try:
        check_apache_config()
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Test interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erreur lors du test: {e}")
        sys.exit(1)
