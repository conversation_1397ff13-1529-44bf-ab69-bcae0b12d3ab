#!/usr/bin/env python3
"""
Fichier WSGI pour GesParc Auto
Configuration pour Apache mod_wsgi
"""

import sys
import os

# Ajouter le répertoire de l'application au path Python
sys.path.insert(0, os.path.dirname(__file__))

# Définir les variables d'environnement pour le contexte Apache
os.environ['SCRIPT_NAME'] = '/gesparc'
os.environ['GESPARC_USE_PREFIX'] = 'true'

# Importer l'application Flask
from gesparc_app import gesparc_app as application

# Configuration pour Apache avec préfixe
application.config['APPLICATION_ROOT'] = '/gesparc'

# Log de démarrage
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
logger.info("🌐 GesParc Auto démarré via WSGI avec préfixe /gesparc")

# Configuration pour l'environnement de production
if __name__ != "__main__":
    # Configuration pour Apache
    application.config['DEBUG'] = False
    application.config['TESTING'] = False

    # Logs pour Apache
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(name)s %(message)s',
        handlers=[
            logging.FileHandler('gesparc.log'),
            logging.StreamHandler()
        ]
    )

    application.logger.info('GesParc Auto démarré via Apache WSGI')
