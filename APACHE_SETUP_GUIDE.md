# 🌐 Guide de Configuration Apache pour GesParc Auto

## 🎯 Objectif
Configurer Apache pour que GesParc Auto soit accessible via `http://localhost/gesparc` au lieu de `http://localhost:5001`.

## 📋 Prérequis
- Apache installé et fonctionnel
- PHP activé dans Apache
- GesParc Auto dans `c:\Apache24\htdocs\gesparc\`

## 🔧 Configuration Étape par Étape

### 1. Vérifier la Structure des Répertoires

```
c:\Apache24\htdocs\
└── gesparc\
    ├── gesparc_app.py
    ├── index.php (proxy vers Flask)
    ├── simple_proxy.php (alternative simple)
    ├── .htaccess
    └── ... (autres fichiers)
```

### 2. Configuration Apache (httpd.conf)

Ajoutez ces lignes dans `c:\Apache24\conf\httpd.conf` :

```apache
# Activer les modules nécessaires
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule php_module modules/mod_php.so

# Configuration pour GesParc
<Directory "c:/Apache24/htdocs/gesparc">
    Options +FollowSymLinks -Indexes
    AllowOverride All
    Require all granted
    
    # Variables d'environnement
    SetEnv SCRIPT_NAME /gesparc
    SetEnv APPLICATION_ROOT /gesparc
    
    # Index files
    DirectoryIndex index.php simple_proxy.php index.html
</Directory>

# Alias pour gesparc (optionnel)
Alias /gesparc "c:/Apache24/htdocs/gesparc"
```

### 3. Redémarrer Apache

```cmd
# Windows Service
net stop Apache2.4
net start Apache2.4

# Ou via Services.msc
```

## 🚀 Solutions Disponibles

### Solution 1: Proxy PHP Complet (index.php)
- **Avantages**: Proxy transparent, garde les URLs /gesparc
- **Inconvénients**: Plus complexe, nécessite cURL PHP
- **Usage**: Production avec URLs propres

### Solution 2: Redirection Simple (simple_proxy.php)
- **Avantages**: Simple, fiable, page d'erreur élégante
- **Inconvénients**: Redirige vers port 5001
- **Usage**: Développement et tests

### Solution 3: WSGI (gesparc.wsgi)
- **Avantages**: Performance optimale, intégration native
- **Inconvénients**: Nécessite mod_wsgi
- **Usage**: Production haute performance

## 📝 Tests de Configuration

### Test 1: Vérification Apache
```bash
# Tester l'accès au répertoire
curl -I http://localhost/gesparc/

# Doit retourner 200 ou 302
```

### Test 2: Vérification PHP
```bash
# Tester le proxy simple
curl http://localhost/gesparc/simple_proxy.php

# Doit rediriger vers Flask ou afficher la page d'erreur
```

### Test 3: Vérification Flask
```bash
# Tester Flask direct
curl http://localhost:5001/

# Doit retourner la page d'accueil GesParc
```

## 🔧 Dépannage

### Problème: 404 Not Found
**Cause**: Apache ne trouve pas le répertoire gesparc
**Solution**:
1. Vérifier que le répertoire existe: `c:\Apache24\htdocs\gesparc\`
2. Vérifier les permissions du répertoire
3. Redémarrer Apache

### Problème: 403 Forbidden
**Cause**: Permissions insuffisantes
**Solution**:
1. Ajouter `Require all granted` dans la configuration
2. Vérifier les permissions Windows du dossier

### Problème: PHP ne fonctionne pas
**Cause**: mod_php non activé
**Solution**:
1. Vérifier `LoadModule php_module modules/mod_php.so`
2. Ajouter `AddType application/x-httpd-php .php`

### Problème: Redirection en boucle
**Cause**: Configuration .htaccess incorrecte
**Solution**:
1. Vérifier le fichier .htaccess
2. Désactiver temporairement: `AllowOverride None`

## 🎯 Configuration Recommandée

### Pour le Développement
```apache
<Directory "c:/Apache24/htdocs/gesparc">
    DirectoryIndex simple_proxy.php
    Options +FollowSymLinks -Indexes
    AllowOverride All
    Require all granted
</Directory>
```

### Pour la Production
```apache
# Utiliser WSGI
WSGIScriptAlias /gesparc "c:/Apache24/htdocs/gesparc/gesparc.wsgi"

<Directory "c:/Apache24/htdocs/gesparc">
    WSGIApplicationGroup %{GLOBAL}
    Require all granted
</Directory>
```

## 📊 Comparaison des Solutions

| Solution | Complexité | Performance | URLs Propres | Maintenance |
|----------|------------|-------------|--------------|-------------|
| Proxy PHP | Moyenne | Bonne | ✅ | Moyenne |
| Redirection | Faible | Excellente | ❌ | Faible |
| WSGI | Élevée | Excellente | ✅ | Faible |

## 🚀 Démarrage Rapide

### Option A: Redirection Simple (Recommandée pour débuter)
1. Copiez `simple_proxy.php` dans `c:\Apache24\htdocs\gesparc\`
2. Configurez Apache pour utiliser `simple_proxy.php` comme index
3. Redémarrez Apache
4. Accédez à `http://localhost/gesparc/`

### Option B: Proxy Complet
1. Utilisez `index.php` existant
2. Configurez .htaccess pour la réécriture
3. Activez mod_rewrite dans Apache
4. Testez `http://localhost/gesparc/`

## 📍 URLs Finales

Après configuration réussie :
- **Page principale**: `http://localhost/gesparc/`
- **Véhicules**: `http://localhost/gesparc/vehicules`
- **Analytics**: `http://localhost/gesparc/analytics/matplotlib`
- **Rapports**: `http://localhost/gesparc/rapports`

## ✅ Validation

Pour valider que tout fonctionne :
1. ✅ `http://localhost/gesparc/` accessible
2. ✅ Navigation dans l'application
3. ✅ Pas d'erreurs dans les logs Apache
4. ✅ Performance acceptable

## 🎉 Résultat Final

Une fois configuré, les utilisateurs pourront accéder à GesParc Auto via l'URL propre `localhost/gesparc` sans avoir besoin de connaître le port Flask 5001.
