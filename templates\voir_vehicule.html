{% extends "base.html" %}

{% block title %}{{ vehicule.immatriculation }} - Détails du Véhicule{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-car"></i> {{ vehicule.immatriculation }}
                <span class="badge {{ 'bg-success' if vehicule.statut == 'disponible' else 'bg-info' if vehicule.statut == 'affecte' else 'bg-warning' if vehicule.statut == 'en_maintenance' else 'bg-danger' }}">
                    {{ vehicule.statut|title }}
                </span>
            </h1>
            <div class="btn-group">
                <a href="{{ url_for('vehicules') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
                <a href="{{ url_for('modifier_vehicule', id=vehicule.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> Modifier
                </a>
                <a href="{{ url_for('supprimer_vehicule', id=vehicule.id) }}" 
                   class="btn btn-danger btn-delete" 
                   data-item-name="{{ vehicule.immatriculation }}">
                    <i class="fas fa-trash"></i> Supprimer
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informations générales -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Informations générales
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Immatriculation:</strong></td>
                        <td>{{ vehicule.immatriculation }}</td>
                    </tr>
                    <tr>
                        <td><strong>Marque:</strong></td>
                        <td>{{ vehicule.marque }}</td>
                    </tr>
                    <tr>
                        <td><strong>Modèle:</strong></td>
                        <td>{{ vehicule.modele }}</td>
                    </tr>
                    <tr>
                        <td><strong>Année:</strong></td>
                        <td>{{ vehicule.annee }} ({{ 2025 - vehicule.annee }} ans)</td>
                    </tr>
                    {% if vehicule.couleur %}
                    <tr>
                        <td><strong>Couleur:</strong></td>
                        <td>{{ vehicule.couleur }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>Carburant:</strong></td>
                        <td><span class="badge bg-secondary">{{ vehicule.carburant }}</span></td>
                    </tr>
                    <tr>
                        <td><strong>Kilométrage:</strong></td>
                        <td>{{ "{:,}".format(vehicule.kilometrage).replace(',', ' ') }} km</td>
                    </tr>
                    <tr>
                        <td><strong>Statut:</strong></td>
                        <td>
                            {% if vehicule.statut == 'disponible' %}
                                <span class="badge bg-success">Disponible</span>
                            {% elif vehicule.statut == 'affecte' %}
                                <span class="badge bg-info">Affecté</span>
                            {% elif vehicule.statut == 'en_maintenance' %}
                                <span class="badge bg-warning text-dark">En maintenance</span>
                            {% elif vehicule.statut == 'hors_service' %}
                                <span class="badge bg-danger">Hors service</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Informations financières -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-coins"></i> Informations financières
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    {% if vehicule.date_acquisition %}
                    <tr>
                        <td><strong>Date d'acquisition:</strong></td>
                        <td>{{ vehicule.date_acquisition }}</td>
                    </tr>
                    {% endif %}

                    <tr>
                        <td><strong>Âge du véhicule:</strong></td>
                        <td>{{ 2025 - vehicule.annee }} ans</td>
                    </tr>

                </table>
            </div>
        </div>
    </div>
</div>

<!-- Historique des maintenances -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools"></i> Historique des maintenances
                    <span class="badge bg-secondary ms-2">{{ maintenances|length }}</span>
                </h5>
                <a href="{{ url_for('ajouter_maintenance') }}?vehicule_id={{ vehicule.id }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> Planifier maintenance
                </a>
            </div>
            <div class="card-body">
                {% if maintenances %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Description</th>
                                <th>Coût</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for maintenance in maintenances %}
                            <tr>
                                <td>{{ maintenance.date_maintenance }}</td>
                                <td>{{ maintenance.type_maintenance }}</td>
                                <td>{{ maintenance.description or '-' }}</td>
                                <td>
                                    {% if maintenance.cout %}
                                        {{ "{:,.2f}".format(maintenance.cout).replace(',', ' ') }} MAD
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if maintenance.statut == 'planifiee' %}
                                        <span class="badge bg-warning text-dark">Planifiée</span>
                                    {% elif maintenance.statut == 'en_cours' %}
                                        <span class="badge bg-info">En cours</span>
                                    {% elif maintenance.statut == 'terminee' %}
                                        <span class="badge bg-success">Terminée</span>
                                    {% elif maintenance.statut == 'annulee' %}
                                        <span class="badge bg-danger">Annulée</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-tools fa-2x text-muted mb-3"></i>
                    <p class="text-muted">Aucune maintenance enregistrée pour ce véhicule</p>
                    <a href="{{ url_for('ajouter_maintenance') }}?vehicule_id={{ vehicule.id }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Planifier la première maintenance
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Historique des affectations -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users"></i> Historique des affectations
                    <span class="badge bg-secondary ms-2">{{ affectations|length }}</span>
                </h5>
                <a href="{{ url_for('affectations') }}?vehicule_id={{ vehicule.id }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> Nouvelle affectation
                </a>
            </div>
            <div class="card-body">
                {% if affectations %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Conducteur</th>
                                <th>Date début</th>
                                <th>Date fin</th>
                                <th>Statut</th>
                                <th>Commentaire</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for affectation in affectations %}
                            <tr>
                                <td>{{ affectation.prenom }} {{ affectation.nom }}</td>
                                <td>{{ affectation.date_debut }}</td>
                                <td>{{ affectation.date_fin or '-' }}</td>
                                <td>
                                    {% if affectation.statut == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                    {% elif affectation.statut == 'terminee' %}
                                        <span class="badge bg-secondary">Terminée</span>
                                    {% elif affectation.statut == 'annulee' %}
                                        <span class="badge bg-danger">Annulée</span>
                                    {% endif %}
                                </td>
                                <td>{{ affectation.commentaire or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-user-slash fa-2x text-muted mb-3"></i>
                    <p class="text-muted">Aucune affectation enregistrée pour ce véhicule</p>
                    <a href="{{ url_for('affectations') }}?vehicule_id={{ vehicule.id }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Créer la première affectation
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
