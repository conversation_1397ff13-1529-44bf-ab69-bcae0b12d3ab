# GesParc Auto - Système de Gestion de Parc Automobile

## 🚗 Description

GesParc Auto est une application web complète de gestion de parc automobile développée avec Flask et SQLite3. Elle permet de gérer efficacement les véhicules, conducteurs, maintenances et affectations d'une flotte automobile.

## ✨ Fonctionnalités

### 🚙 Gestion des Véhicules
- ✅ Ajout, modification et suppression de véhicules
- ✅ Suivi détaillé (immatriculation, marque, modèle, année, carburant, kilométrage)
- ✅ Gestion des statuts (disponible, affecté, en maintenance, hors service)
- ✅ Historique des maintenances et affectations
- ✅ Calcul de la valeur estimée et dépréciation

### 👥 Gestion des Conducteurs
- ✅ Ajout, modification et suppression de conducteurs
- ✅ Informations complètes (nom, prénom, permis, contact)
- ✅ Suivi des affectations de véhicules
- ✅ Historique des véhicules utilisés

### 🔧 Gestion des Maintenances
- ✅ Planification des maintenances
- ✅ Suivi des coûts et dates
- ✅ Historique par véhicule
- ✅ Statuts de maintenance (planifiée, en cours, terminée)

### 🔄 Gestion des Affectations
- ✅ Attribution de véhicules aux conducteurs
- ✅ Suivi des périodes d'affectation
- ✅ Historique complet

### 📊 Rapports et Statistiques
- ✅ Tableau de bord avec statistiques en temps réel
- ✅ Graphiques de répartition (statuts, carburants)
- ✅ Rapports détaillés
- ✅ Export des données

## 🛠️ Technologies Utilisées

- **Backend**: Flask (Python)
- **Base de données**: SQLite3
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework CSS**: Bootstrap 5
- **Icônes**: Font Awesome
- **Graphiques**: Chart.js

## 📋 Prérequis

- Python 3.8 ou supérieur
- Flask 2.3.3 ou supérieur

## 🚀 Installation et Démarrage

### 1. Cloner ou télécharger le projet
```bash
# Si vous avez git
git clone <url-du-repo>
cd gesparc

# Ou télécharger et extraire l'archive
```

### 2. Installer les dépendances
```bash
pip install Flask
# ou
pip install -r requirements.txt
```

### 3. Initialiser la base de données
```bash
python init_db.py
```

### 4. Démarrer l'application
```bash
python gesparc_app.py
```

### 5. Accéder à l'application
Ouvrez votre navigateur et allez à : `http://localhost:5001`

## 📁 Structure du Projet

```
gesparc/
├── gesparc_app.py          # Application Flask principale
├── models.py               # Modèles de données
├── init_db.py             # Script d'initialisation de la DB
├── requirements.txt        # Dépendances Python
├── parc_automobile.db     # Base de données SQLite
├── templates/             # Templates HTML
│   ├── base.html
│   ├── index.html
│   ├── vehicules.html
│   ├── ajouter_vehicule.html
│   ├── voir_vehicule.html
│   ├── modifier_vehicule.html
│   ├── conducteurs.html
│   ├── ajouter_conducteur.html
│   ├── voir_conducteur.html
│   ├── modifier_conducteur.html
│   ├── maintenances.html
│   ├── affectations.html
│   └── rapports.html
└── static/               # Fichiers statiques
    ├── css/
    │   └── style.css
    └── js/
        └── app.js
```

## 🗃️ Base de Données

L'application utilise SQLite3 avec les tables suivantes :

- **vehicules** : Informations des véhicules
- **conducteurs** : Informations des conducteurs
- **maintenances** : Planification et suivi des maintenances
- **affectations** : Attribution véhicule-conducteur

## 🎯 Utilisation

### Tableau de Bord
- Vue d'ensemble avec statistiques clés
- Alertes et maintenances à venir
- Actions rapides

### Gestion des Véhicules
1. Accéder à "Véhicules" dans le menu
2. Cliquer sur "Ajouter un véhicule"
3. Remplir le formulaire avec les informations
4. Enregistrer

### Gestion des Conducteurs
1. Accéder à "Conducteurs" dans le menu
2. Cliquer sur "Ajouter un conducteur"
3. Saisir les informations personnelles et de contact
4. Enregistrer

### Consultation des Rapports
1. Accéder à "Rapports" dans le menu
2. Consulter les statistiques et graphiques
3. Exporter les données si nécessaire

## 🔧 Fonctionnalités Avancées

- **Recherche et filtres** : Recherche en temps réel dans les listes
- **Validation des formulaires** : Validation côté client et serveur
- **Interface responsive** : Compatible mobile et desktop
- **Messages flash** : Notifications de succès/erreur
- **Confirmations** : Demandes de confirmation pour les suppressions

## 📊 Données de Test

L'application est livrée avec des données de test incluant :
- 4 véhicules d'exemple
- 3 conducteurs d'exemple
- Maintenances planifiées
- Affectations d'exemple

## 🔒 Sécurité

- Validation des données d'entrée
- Protection contre les injections SQL (utilisation de paramètres)
- Gestion des erreurs
- Clé secrète pour les sessions Flask

## 🚀 Développements Futurs

- Authentification et gestion des utilisateurs
- Notifications par email
- API REST
- Import/Export Excel
- Géolocalisation des véhicules
- Gestion des assurances
- Calcul automatique des coûts

## 🐛 Dépannage

### L'application ne démarre pas
- Vérifiez que Python et Flask sont installés
- Vérifiez que le port 5001 n'est pas utilisé

### Erreur de base de données
- Relancez `python init_db.py` pour réinitialiser la base

### Pages qui ne s'affichent pas
- Vérifiez que tous les templates sont présents
- Consultez les logs dans le terminal

## 📝 Licence

Ce projet est développé à des fins éducatives et de démonstration.

## 👨‍💻 Auteur

Développé avec Flask et SQLite3 pour la gestion de parc automobile.

---

**Version**: 1.0.0  
**Date**: Juillet 2025
