#!/usr/bin/env python3
"""
Test des corrections finales : heure par défaut et format dates
"""

import sqlite3
from datetime import date, datetime

def test_heure_defaut_debut():
    """Test que l'heure par défaut de début est 00:00"""
    print("🧪 Test Heure par Défaut Date Début")
    print("=" * 40)
    
    try:
        # Simuler la logique du backend
        date_today = date.today()
        datetime_debut_default = date_today.strftime('%Y-%m-%dT00:00')
        datetime_fin_default = date_today.strftime('%Y-%m-%dT23:59')
        
        print(f"📅 Date d'aujourd'hui: {date_today}")
        print(f"🕐 Début par défaut: {datetime_debut_default}")
        print(f"🕚 Fin par défaut: {datetime_fin_default}")
        
        # Vérifications
        if datetime_debut_default.endswith('T00:00'):
            print("✅ Heure de début correcte (00:00)")
        else:
            print("❌ Erreur heure de début")
            return False
        
        if datetime_fin_default.endswith('T23:59'):
            print("✅ Heure de fin correcte (23:59)")
        else:
            print("❌ Erreur heure de fin")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_format_datetime_ordre_mission():
    """Test du format datetime dans l'ordre de mission"""
    print(f"\n🎨 Test Format DateTime Ordre Mission")
    print("=" * 40)
    
    try:
        # Simuler le filtre format_datetime
        def format_datetime_filter(date_str):
            if not date_str:
                return '-'
            
            try:
                if 'T' in date_str:
                    dt = datetime.strptime(date_str, '%Y-%m-%dT%H:%M')
                    return dt.strftime('%d/%m/%Y à %H:%M')
                elif ' ' in date_str:
                    if len(date_str) > 16:
                        dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                    else:
                        dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M')
                    return dt.strftime('%d/%m/%Y à %H:%M')
                else:
                    dt = datetime.strptime(date_str, '%Y-%m-%d')
                    return dt.strftime('%d/%m/%Y')
            except (ValueError, TypeError):
                return date_str
        
        # Test de différents formats
        test_dates = [
            ('2025-07-25T00:00', '25/07/2025 à 00:00'),
            ('2025-07-25T23:59', '25/07/2025 à 23:59'),
            ('2025-07-25T08:30', '25/07/2025 à 08:30'),
            ('2025-07-25T17:45', '25/07/2025 à 17:45'),
            ('2025-07-25 14:30:00', '25/07/2025 à 14:30'),
            ('2025-07-25 09:15', '25/07/2025 à 09:15'),
            ('2025-07-25', '25/07/2025'),
            ('', '-'),
            (None, '-')
        ]
        
        print("📊 Test des formats de date:")
        all_passed = True
        
        for date_input, expected in test_dates:
            result = format_datetime_filter(date_input)
            if result == expected:
                print(f"  ✅ '{date_input}' → '{result}'")
            else:
                print(f"  ❌ '{date_input}' → '{result}' (attendu: '{expected}')")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_ordre_mission_avec_donnees():
    """Test de l'ordre de mission avec des données réelles"""
    print(f"\n📄 Test Ordre Mission avec Données")
    print("=" * 40)
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect('parc_automobile.db')
        conn.row_factory = sqlite3.Row
        
        # Créer une affectation de test avec dates et heures
        vehicule = conn.execute('SELECT * FROM vehicules LIMIT 1').fetchone()
        conducteur = conn.execute('SELECT * FROM conducteurs LIMIT 1').fetchone()
        
        if not vehicule or not conducteur:
            print("⚠️  Pas de données pour tester")
            conn.close()
            return True
        
        # Données de test avec heures spécifiques
        date_debut = '2025-07-25T00:00'  # Heure par défaut début
        date_fin = '2025-07-25T23:59'    # Heure par défaut fin
        mission = 'Test mission avec heures par défaut'
        destination = 'Centre-ville, Rabat'
        
        # Insérer l'affectation de test
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO affectations (vehicule_id, conducteur_id, date_debut, date_fin, statut, mission, destination)
            VALUES (?, ?, ?, ?, 'active', ?, ?)
        ''', (vehicule['id'], conducteur['id'], date_debut, date_fin, mission, destination))
        
        affectation_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ Affectation de test créée avec ID: {affectation_id}")
        
        # Tester la requête de l'ordre de mission
        ordre_data = conn.execute('''
            SELECT a.*, 
                   v.immatriculation, v.marque, v.modele, v.annee, v.couleur, 
                   v.carburant, v.kilometrage, v.statut as statut_vehicule,
                   c.nom, c.prenom, c.telephone, c.email, c.numero_permis,
                   c.statut as statut_conducteur
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.id = ?
        ''', (affectation_id,)).fetchone()
        
        if ordre_data:
            print(f"✅ Données ordre de mission récupérées")
            print(f"  📅 Date début: {ordre_data['date_debut']}")
            print(f"  📅 Date fin: {ordre_data['date_fin']}")
            print(f"  🎯 Mission: {ordre_data['mission']}")
            print(f"  📍 Destination: {ordre_data['destination']}")
            
            # Test du formatage
            def format_datetime_filter(date_str):
                if not date_str:
                    return '-'
                try:
                    if 'T' in date_str:
                        dt = datetime.strptime(date_str, '%Y-%m-%dT%H:%M')
                        return dt.strftime('%d/%m/%Y à %H:%M')
                    return date_str
                except:
                    return date_str
            
            debut_formate = format_datetime_filter(ordre_data['date_debut'])
            fin_formatee = format_datetime_filter(ordre_data['date_fin'])
            
            print(f"  📊 Début formaté: {debut_formate}")
            print(f"  📊 Fin formatée: {fin_formatee}")
            
            # Vérifier les heures par défaut
            if '00:00' in debut_formate:
                print("  ✅ Heure de début par défaut (00:00) présente")
            else:
                print("  ⚠️  Heure de début par défaut manquante")
            
            if '23:59' in fin_formatee:
                print("  ✅ Heure de fin par défaut (23:59) présente")
            else:
                print("  ⚠️  Heure de fin par défaut manquante")
        
        # Nettoyage
        conn.execute('DELETE FROM affectations WHERE id = ?', (affectation_id,))
        conn.commit()
        conn.close()
        
        print("✅ Données de test nettoyées")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fichiers_configuration():
    """Test de la configuration dans les fichiers"""
    print(f"\n🔧 Test Configuration Fichiers")
    print("=" * 35)
    
    try:
        # Vérifier gesparc_app.py
        with open('gesparc_app.py', 'r', encoding='utf-8') as f:
            contenu_backend = f.read()
        
        checks_backend = [
            ('T00:00', 'Heure début 00:00'),
            ('T23:59', 'Heure fin 23:59'),
            ('datetime_debut_default', 'Variable début par défaut'),
            ('datetime_fin_default', 'Variable fin par défaut'),
            ('@gesparc_app.template_filter(\'format_datetime\')', 'Filtre format_datetime'),
            ('strftime(\'%d/%m/%Y à %H:%M\')', 'Format français avec heure')
        ]
        
        for check, description in checks_backend:
            if check in contenu_backend:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description} - manquant")
        
        # Vérifier ajouter_affectation.html
        with open('templates/ajouter_affectation.html', 'r', encoding='utf-8') as f:
            contenu_form = f.read()
        
        if 'datetime_debut_default' in contenu_form:
            print(f"  ✅ Formulaire utilise datetime_debut_default")
        else:
            print(f"  ❌ Formulaire n'utilise pas datetime_debut_default")
        
        # Vérifier ordre_mission.html
        with open('templates/ordre_mission.html', 'r', encoding='utf-8') as f:
            contenu_ordre = f.read()
        
        if '| format_datetime' in contenu_ordre:
            print(f"  ✅ Ordre mission utilise format_datetime")
        else:
            print(f"  ❌ Ordre mission n'utilise pas format_datetime")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Test des corrections finales...")
    
    # Tests
    test1 = test_heure_defaut_debut()
    test2 = test_format_datetime_ordre_mission()
    test3 = test_ordre_mission_avec_donnees()
    test4 = test_fichiers_configuration()
    
    # Résultat final
    print(f"\n" + "="*40)
    if test1 and test2 and test3 and test4:
        print("🎉 CORRECTIONS VALIDÉES!")
        print("✅ Heure par défaut début: 00:00")
        print("✅ Format datetime dans ordre mission")
        print("✅ Configuration correcte")
        print("📅 Les dates s'affichent avec date ET heure")
    else:
        print("⚠️  PROBLÈME DÉTECTÉ")
        print("🔧 Vérifiez les corrections")
