{% extends "base.html" %}

{% block title %}Gestion des Conducteurs - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-users"></i> Gestion des Conducteurs</h1>
            <a href="{{ url_for('ajouter_conducteur') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Ajouter un conducteur
            </a>
        </div>
    </div>
</div>

<!-- Filtres et recherche -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" class="form-control" id="searchConducteurs" placeholder="Rechercher par nom, prénom, numéro de permis...">
        </div>
    </div>
    <div class="col-md-4">
        <select class="form-select" id="filterStatut">
            <option value="">Tous les statuts</option>
            <option value="actif">Actif</option>
            <option value="inactif">Inactif</option>
        </select>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4>{{ stats.total or 0 }}</h4>
                <p class="mb-0">Total conducteurs</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4>{{ stats.actif or 0 }}</h4>
                <p class="mb-0">Actifs</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4>{{ affectations_actives or 0 }}</h4>
                <p class="mb-0">Avec véhicule affecté</p>
            </div>
        </div>
    </div>
</div>

<!-- Tableau des conducteurs -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> Liste des conducteurs
                <span class="badge bg-secondary ms-2">{{ conducteurs|length }} conducteur(s)</span>
            </h5>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download"></i> Exporter
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('export_conducteurs', format='csv') }}">
                        <i class="fas fa-file-csv"></i> CSV
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('export_conducteurs', format='xlsx') }}">
                        <i class="fas fa-file-excel"></i> Excel (XLSX)
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('export_conducteurs', format='xls') }}">
                        <i class="fas fa-file-excel"></i> Excel (XLS)
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if conducteurs %}
        <div class="table-responsive">
            <table class="table table-hover" id="tableConducteurs">
                <thead>
                    <tr>
                        <th>Nom complet</th>
                        <th>Numéro de permis</th>
                        <th>Date du permis</th>
                        <th>Contact</th>
                        <th>Statut</th>
                        <th>Véhicule affecté</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for conducteur in conducteurs %}
                    <tr>
                        <td>
                            <strong>{{ conducteur.prenom }} {{ conducteur.nom }}</strong>
                        </td>
                        <td>
                            <code>{{ conducteur.numero_permis }}</code>
                        </td>
                        <td>
                            {% if conducteur.date_permis %}
                                {{ conducteur.date_permis }}
                                <br><small class="text-muted">
                                    {% set years = ((2025 - conducteur.date_permis.split('-')[0]|int) if conducteur.date_permis else 0) %}
                                    {{ years }} ans d'expérience
                                </small>
                            {% else %}
                                <span class="text-muted">Non renseignée</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if conducteur.telephone %}
                                <i class="fas fa-phone"></i> {{ conducteur.telephone }}<br>
                            {% endif %}
                            {% if conducteur.email %}
                                <i class="fas fa-envelope"></i> {{ conducteur.email }}
                            {% endif %}
                            {% if not conducteur.telephone and not conducteur.email %}
                                <span class="text-muted">Non renseigné</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if conducteur.statut == 'actif' %}
                                <span class="badge bg-success">Actif</span>
                            {% else %}
                                <span class="badge bg-secondary">Inactif</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if conducteur.vehicule_affecte %}
                                <span class="badge bg-info">{{ conducteur.vehicule_affecte }}</span>
                            {% else %}
                                <span class="text-muted">Aucun</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('voir_conducteur', id=conducteur.id) }}" 
                                   class="btn btn-outline-primary" title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('modifier_conducteur', id=conducteur.id) }}" 
                                   class="btn btn-outline-warning" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('supprimer_conducteur', id=conducteur.id) }}" 
                                   class="btn btn-outline-danger btn-delete" 
                                   data-item-name="{{ conducteur.prenom }} {{ conducteur.nom }}"
                                   title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun conducteur enregistré</h5>
            <p class="text-muted">Commencez par ajouter votre premier conducteur.</p>
            <a href="{{ url_for('ajouter_conducteur') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Ajouter un conducteur
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filtrage en temps réel
    const searchInput = document.getElementById('searchConducteurs');
    const filterStatut = document.getElementById('filterStatut');
    
    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statutFilter = filterStatut.value;
        const rows = document.querySelectorAll('#tableConducteurs tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const statut = row.querySelector('.badge').textContent.toLowerCase();
            
            const matchSearch = text.includes(searchTerm);
            const matchStatut = !statutFilter || statut.includes(statutFilter.toLowerCase());
            
            row.style.display = matchSearch && matchStatut ? '' : 'none';
        });
    }
    
    searchInput.addEventListener('keyup', filterTable);
    filterStatut.addEventListener('change', filterTable);
});
</script>
{% endblock %}
