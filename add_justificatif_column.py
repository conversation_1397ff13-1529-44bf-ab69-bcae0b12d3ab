#!/usr/bin/env python3
import sqlite3

# Connexion à la base de données
conn = sqlite3.connect('parc_automobile.db')
cursor = conn.cursor()

print("🔧 Ajout de la colonne justificatif à la table maintenances")
print("=" * 60)

try:
    # Vérifier si la colonne existe déjà
    cursor.execute("PRAGMA table_info(maintenances)")
    columns = [column[1] for column in cursor.fetchall()]
    
    if 'justificatif' in columns:
        print("✅ La colonne 'justificatif' existe déjà")
    else:
        # Ajouter la colonne justificatif
        cursor.execute('ALTER TABLE maintenances ADD COLUMN justificatif TEXT')
        conn.commit()
        print("✅ Colonne 'justificatif' ajoutée avec succès")
    
    # Afficher la structure mise à jour
    cursor.execute("PRAGMA table_info(maintenances)")
    columns_info = cursor.fetchall()
    
    print("\n📋 Structure de la table maintenances :")
    print("-" * 60)
    for col in columns_info:
        print(f"   {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
    
except Exception as e:
    print(f"❌ Erreur : {e}")

finally:
    conn.close()

print("\n🎉 Opération terminée !")