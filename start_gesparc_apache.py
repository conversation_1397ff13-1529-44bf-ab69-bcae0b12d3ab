#!/usr/bin/env python3
"""
Script de démarrage pour GesParc Auto en mode Apache (/gesparc)
"""

import os
import sys

# Définir les variables d'environnement pour le contexte Apache
os.environ['FLASK_ENV'] = 'production'
os.environ['APPLICATION_ROOT'] = '/gesparc'
os.environ['SCRIPT_NAME'] = '/gesparc'

# Changer vers le répertoire de l'application
app_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(app_dir)

# Ajouter le répertoire au path Python
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

# Importer et lancer l'application
from gesparc_app import gesparc_app

if __name__ == '__main__':
    print("=" * 60)
    print("🚗 GesParc Auto - Mode Apache (/gesparc)")
    print("=" * 60)
    print("Configuration:")
    print(f"  - Répertoire: {app_dir}")
    print(f"  - Context: /gesparc")
    print(f"  - Base de données: {gesparc_app.config.get('DATABASE')}")
    print(f"  - Application Root: {gesparc_app.config.get('APPLICATION_ROOT')}")
    print("")
    print("URLs d'accès:")
    print("  - Via Apache: http://localhost/gesparc")
    print("  - Direct Flask: http://localhost:5001")
    print("")
    print("Appuyez sur Ctrl+C pour arrêter l'application")
    print("=" * 60)
    
    try:
        gesparc_app.run(
            debug=False, 
            host='127.0.0.1', 
            port=5001,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n🛑 Application arrêtée par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur lors du démarrage: {e}")
        sys.exit(1)
