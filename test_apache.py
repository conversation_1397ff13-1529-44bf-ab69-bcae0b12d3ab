#!/usr/bin/env python3
"""
Script de test pour vérifier l'accès à GesParc Auto via Apache
"""

import requests
import time
import sys

def test_apache_access():
    """Teste l'accès via Apache"""
    print("🔍 Test d'accès à GesParc Auto via Apache")
    print("=" * 50)
    
    # Test 1: Accès au dossier gesparc
    print("1. Test d'accès au dossier gesparc...")
    try:
        response = requests.get("http://localhost/gesparc/", timeout=5)
        if response.status_code == 200:
            print("   ✅ Accès OK (Status: 200)")
        else:
            print(f"   ❌ Erreur d'accès (Status: {response.status_code})")
            return False
    except Exception as e:
        print(f"   ❌ Erreur de connexion: {e}")
        return False
    
    # Test 2: Vérifier si Flask est accessible
    print("2. Test d'accès direct à Flask...")
    try:
        response = requests.get("http://localhost:5001/", timeout=5)
        if response.status_code == 200:
            print("   ✅ Flask accessible (Status: 200)")
            flask_running = True
        else:
            print(f"   ⚠️ Flask non accessible (Status: {response.status_code})")
            flask_running = False
    except Exception as e:
        print(f"   ⚠️ Flask non accessible: {e}")
        flask_running = False
    
    # Test 3: Test des pages principales si Flask fonctionne
    if flask_running:
        print("3. Test des pages principales...")
        pages = [
            ("/", "Tableau de bord"),
            ("/vehicules", "Véhicules"),
            ("/conducteurs", "Conducteurs"),
            ("/rapports", "Rapports")
        ]
        
        for path, name in pages:
            try:
                response = requests.get(f"http://localhost:5001{path}", timeout=5)
                if response.status_code == 200:
                    print(f"   ✅ {name} OK")
                else:
                    print(f"   ❌ {name} erreur (Status: {response.status_code})")
            except Exception as e:
                print(f"   ❌ {name} erreur: {e}")
    
    # Test 4: Test des fichiers statiques
    print("4. Test des fichiers statiques...")
    static_files = [
        "/gesparc/static/css/style.css",
        "/gesparc/static/js/app.js"
    ]
    
    for file_path in static_files:
        try:
            response = requests.get(f"http://localhost{file_path}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {file_path} OK")
            else:
                print(f"   ⚠️ {file_path} non trouvé (Status: {response.status_code})")
        except Exception as e:
            print(f"   ❌ {file_path} erreur: {e}")
    
    print("\n" + "=" * 50)
    if flask_running:
        print("🎉 Configuration Apache + Flask : SUCCÈS")
        print("📍 Accès via Apache : http://localhost/gesparc/")
        print("📍 Accès direct Flask : http://localhost:5001/")
    else:
        print("⚠️ Apache fonctionne, mais Flask doit être démarré")
        print("💡 Lancez: python gesparc_app.py")
        print("📍 Accès via Apache : http://localhost/gesparc/")
    
    return True

def test_flask_only():
    """Teste uniquement Flask"""
    print("🔍 Test d'accès direct à Flask")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:5001/", timeout=5)
        if response.status_code == 200:
            print("✅ Flask accessible sur http://localhost:5001/")
            return True
        else:
            print(f"❌ Flask erreur (Status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ Flask non accessible: {e}")
        print("💡 Lancez: python gesparc_app.py")
        return False

if __name__ == "__main__":
    print("🚗 GesParc Auto - Test de Configuration")
    print("=" * 50)
    
    # Test Apache d'abord
    apache_ok = test_apache_access()
    
    print("\n")
    
    # Test Flask si Apache ne fonctionne pas
    if not apache_ok:
        flask_ok = test_flask_only()
    
    print("\n🏁 Tests terminés")
