#!/usr/bin/env python3
"""
Debug des rapports avancés
"""

import sqlite3
from datetime import datetime

def test_rapports_avances():
    """Teste les nouvelles requêtes des rapports avancés"""
    print("🔍 Debug des Rapports Avancés")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('parc_automobile.db')
        
        # Test 1: Statistiques de base
        print("1. Test statistiques de base...")
        stats = {}
        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]
        stats['total_conducteurs'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]
        stats['total_maintenances'] = conn.execute('SELECT COUNT(*) FROM maintenances').fetchone()[0]
        stats['total_affectations'] = conn.execute('SELECT COUNT(*) FROM affectations').fetchone()[0]
        print(f"   ✅ Statistiques de base: {stats}")
        
        # Test 2: Statistiques détaillées
        print("\n2. Test statistiques détaillées...")
        try:
            stats['vehicules_disponibles'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'").fetchone()[0]
            stats['vehicules_en_maintenance'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'en_maintenance'").fetchone()[0]
            stats['vehicules_affectes'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'affecte'").fetchone()[0]
            print(f"   ✅ Répartition véhicules: Disponibles={stats['vehicules_disponibles']}, Maintenance={stats['vehicules_en_maintenance']}, Affectés={stats['vehicules_affectes']}")
        except Exception as e:
            print(f"   ❌ Erreur statistiques détaillées: {e}")
        
        # Test 3: Analyses financières
        print("\n3. Test analyses financières...")
        try:
            cout_total_result = conn.execute('SELECT SUM(cout) FROM maintenances WHERE cout IS NOT NULL').fetchone()
            stats['cout_total_maintenances'] = cout_total_result[0] if cout_total_result[0] else 0
            
            cout_mois_result = conn.execute('''
                SELECT SUM(cout) FROM maintenances 
                WHERE cout IS NOT NULL 
                AND date_maintenance >= date('now', 'start of month')
            ''').fetchone()
            stats['cout_mois_maintenances'] = cout_mois_result[0] if cout_mois_result[0] else 0
            
            print(f"   ✅ Coûts: Total={stats['cout_total_maintenances']}, Mois={stats['cout_mois_maintenances']}")
        except Exception as e:
            print(f"   ❌ Erreur analyses financières: {e}")
        
        # Test 4: Métriques de performance
        print("\n4. Test métriques de performance...")
        try:
            stats['cout_moyen_vehicule'] = (stats['cout_total_maintenances'] / stats['total_vehicules']) if stats['total_vehicules'] > 0 else 0
            stats['taux_disponibilite'] = (stats['vehicules_disponibles'] / stats['total_vehicules'] * 100) if stats['total_vehicules'] > 0 else 0
            
            km_moyen_result = conn.execute('SELECT AVG(kilometrage) FROM vehicules WHERE kilometrage > 0').fetchone()
            stats['kilometrage_moyen'] = km_moyen_result[0] if km_moyen_result[0] else 0
            
            age_moyen_result = conn.execute('SELECT AVG(2025 - annee) FROM vehicules').fetchone()
            stats['age_moyen_parc'] = age_moyen_result[0] if age_moyen_result[0] else 0
            
            print(f"   ✅ Performance: Taux disponibilité={stats['taux_disponibilite']:.1f}%, KM moyen={stats['kilometrage_moyen']:.0f}, Âge moyen={stats['age_moyen_parc']:.1f} ans")
        except Exception as e:
            print(f"   ❌ Erreur métriques performance: {e}")
        
        # Test 5: Répartitions
        print("\n5. Test répartitions...")
        try:
            vehicules_par_statut = conn.execute('''
                SELECT statut, COUNT(*) as count
                FROM vehicules
                GROUP BY statut
                ORDER BY count DESC
            ''').fetchall()
            print(f"   ✅ Véhicules par statut: {len(vehicules_par_statut)} groupes")
            
            vehicules_par_carburant = conn.execute('''
                SELECT carburant, COUNT(*) as count
                FROM vehicules
                WHERE carburant IS NOT NULL AND carburant != ''
                GROUP BY carburant
                ORDER BY count DESC
            ''').fetchall()
            print(f"   ✅ Véhicules par carburant: {len(vehicules_par_carburant)} groupes")
            
            vehicules_par_marque = conn.execute('''
                SELECT marque, COUNT(*) as count
                FROM vehicules
                WHERE marque IS NOT NULL AND marque != ''
                GROUP BY marque
                ORDER BY count DESC
                LIMIT 10
            ''').fetchall()
            print(f"   ✅ Top marques: {len(vehicules_par_marque)} marques")
        except Exception as e:
            print(f"   ❌ Erreur répartitions: {e}")
        
        # Test 6: Analyses temporelles
        print("\n6. Test analyses temporelles...")
        try:
            maintenances_par_mois = conn.execute('''
                SELECT strftime('%Y-%m', date_maintenance) as mois, 
                       COUNT(*) as count,
                       SUM(CASE WHEN cout IS NOT NULL THEN cout ELSE 0 END) as cout_total
                FROM maintenances
                WHERE date_maintenance >= date('now', '-12 months')
                GROUP BY strftime('%Y-%m', date_maintenance)
                ORDER BY mois
            ''').fetchall()
            print(f"   ✅ Maintenances par mois: {len(maintenances_par_mois)} mois")
            
            maintenances_par_type = conn.execute('''
                SELECT type_maintenance, COUNT(*) as count,
                       AVG(CASE WHEN cout IS NOT NULL THEN cout ELSE 0 END) as cout_moyen
                FROM maintenances
                GROUP BY type_maintenance
                ORDER BY count DESC
            ''').fetchall()
            print(f"   ✅ Maintenances par type: {len(maintenances_par_type)} types")
        except Exception as e:
            print(f"   ❌ Erreur analyses temporelles: {e}")
        
        # Test 7: Alertes et prédictions
        print("\n7. Test alertes et prédictions...")
        try:
            vehicules_km_eleve = conn.execute('''
                SELECT immatriculation, marque, modele, kilometrage
                FROM vehicules 
                WHERE kilometrage > 40000 AND statut != 'en_maintenance'
                ORDER BY kilometrage DESC
            ''').fetchall()
            print(f"   ✅ Véhicules kilométrage élevé: {len(vehicules_km_eleve)} véhicules")
            
            vehicules_cout_eleve = conn.execute('''
                SELECT v.immatriculation, v.marque, v.modele, SUM(m.cout) as cout_total
                FROM vehicules v
                JOIN maintenances m ON v.id = m.vehicule_id
                WHERE m.cout IS NOT NULL
                GROUP BY v.id
                HAVING cout_total > 2000
                ORDER BY cout_total DESC
            ''').fetchall()
            print(f"   ✅ Véhicules coût élevé: {len(vehicules_cout_eleve)} véhicules")
        except Exception as e:
            print(f"   ❌ Erreur alertes: {e}")
        
        conn.close()
        print("\n✅ Tous les tests des requêtes sont passés")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur générale: {e}")
        return False

if __name__ == "__main__":
    test_rapports_avances()
