[{"label": "Flask", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "render_template", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "request", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "redirect", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "url_for", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "flash", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "jsonify", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "Response", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "Flask", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "render_template", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "request", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "redirect", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "url_for", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "flash", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "jsonify", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "sqlite3", "kind": 6, "isExtraImport": true, "importPath": "sqlite3", "description": "sqlite3", "detail": "sqlite3", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "date", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "date", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "csv", "kind": 6, "isExtraImport": true, "importPath": "csv", "description": "csv", "detail": "csv", "documentation": {}}, {"label": "io", "kind": 6, "isExtraImport": true, "importPath": "io", "description": "io", "detail": "io", "documentation": {}}, {"label": "openpyxl", "kind": 6, "isExtraImport": true, "importPath": "openpyxl", "description": "openpyxl", "detail": "openpyxl", "documentation": {}}, {"label": "Font", "importPath": "openpyxl.styles", "description": "openpyxl.styles", "isExtraImport": true, "detail": "openpyxl.styles", "documentation": {}}, {"label": "Pat<PERSON>Fill", "importPath": "openpyxl.styles", "description": "openpyxl.styles", "isExtraImport": true, "detail": "openpyxl.styles", "documentation": {}}, {"label": "Alignment", "importPath": "openpyxl.styles", "description": "openpyxl.styles", "isExtraImport": true, "detail": "openpyxl.styles", "documentation": {}}, {"label": "xlwt", "kind": 6, "isExtraImport": true, "importPath": "xlwt", "description": "xlwt", "detail": "xlwt", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "export_to_csv", "importPath": "export_utils", "description": "export_utils", "isExtraImport": true, "detail": "export_utils", "documentation": {}}, {"label": "export_to_excel_xlsx", "importPath": "export_utils", "description": "export_utils", "isExtraImport": true, "detail": "export_utils", "documentation": {}}, {"label": "export_to_excel_xls", "importPath": "export_utils", "description": "export_utils", "isExtraImport": true, "detail": "export_utils", "documentation": {}}, {"label": "get_export_filename", "importPath": "export_utils", "description": "export_utils", "isExtraImport": true, "detail": "export_utils", "documentation": {}}, {"label": "get_config", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "ImmatriculationMaroc", "importPath": "immatriculation_maroc", "description": "immatriculation_maroc", "isExtraImport": true, "detail": "immatriculation_maroc", "documentation": {}}, {"label": "GesparcAnalytics", "importPath": "matplotlib_analytics", "description": "matplotlib_analytics", "isExtraImport": true, "detail": "matplotlib_analytics", "documentation": {}}, {"label": "GesparcAnalytics", "importPath": "matplotlib_analytics", "description": "matplotlib_analytics", "isExtraImport": true, "detail": "matplotlib_analytics", "documentation": {}}, {"label": "re", "kind": 6, "isExtraImport": true, "importPath": "re", "description": "re", "detail": "re", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "matplotlib.pyplot", "kind": 6, "isExtraImport": true, "importPath": "matplotlib.pyplot", "description": "matplotlib.pyplot", "detail": "matplotlib.pyplot", "documentation": {}}, {"label": "matplotlib.dates", "kind": 6, "isExtraImport": true, "importPath": "matplotlib.dates", "description": "matplotlib.dates", "detail": "matplotlib.dates", "documentation": {}}, {"label": "seaborn", "kind": 6, "isExtraImport": true, "importPath": "seaborn", "description": "seaborn", "detail": "seaborn", "documentation": {}}, {"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "numpy", "kind": 6, "isExtraImport": true, "importPath": "numpy", "description": "numpy", "detail": "numpy", "documentation": {}}, {"label": "base64", "kind": 6, "isExtraImport": true, "importPath": "base64", "description": "base64", "detail": "base64", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "warnings", "kind": 6, "isExtraImport": true, "importPath": "warnings", "description": "warnings", "detail": "warnings", "documentation": {}}, {"label": "gesparc_app", "importPath": "gesparc_app", "description": "gesparc_app", "isExtraImport": true, "detail": "gesparc_app", "documentation": {}}, {"label": "gesparc_app", "importPath": "gesparc_app", "description": "gesparc_app", "isExtraImport": true, "detail": "gesparc_app", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "requests", "kind": 6, "isExtraImport": true, "importPath": "requests", "description": "requests", "detail": "requests", "documentation": {}}, {"label": "time", "kind": 6, "isExtraImport": true, "importPath": "time", "description": "time", "detail": "time", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "get_db_connection", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def get_db_connection():\n    \"\"\"Établit une connexion à la base de données SQLite\"\"\"\n    conn = sqlite3.connect(DATABASE)\n    conn.row_factory = sqlite3.Row\n    return conn\***********('/')\ndef index():\n    \"\"\"Page d'accueil avec tableau de bord\"\"\"\n    # Récupérer les statistiques\n    stats = {", "detail": "app", "documentation": {}}, {"label": "index", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def index():\n    \"\"\"Page d'accueil avec tableau de bord\"\"\"\n    # Récupérer les statistiques\n    stats = {\n        'total_vehicules': 0,\n        'vehicules_disponibles': 0,\n        'total_conducteurs': 0,\n        'maintenances_prevues': 0\n    }\n    maintenances_prochaines = []", "detail": "app", "documentation": {}}, {"label": "vehicules", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def vehicules():\n    return \"Page des véhicules - En cours de développement\"\***********('/vehicules/ajouter')\ndef ajouter_vehicule():\n    return \"Ajouter un véhicule - En cours de développement\"\***********('/conducteurs')\ndef conducteurs():\n    return \"Page des conducteurs - En cours de développement\"\***********('/conducteurs/ajouter')\ndef ajouter_conducteur():", "detail": "app", "documentation": {}}, {"label": "ajouter_vehicule", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def ajouter_vehicule():\n    return \"Ajouter un véhicule - En cours de développement\"\***********('/conducteurs')\ndef conducteurs():\n    return \"Page des conducteurs - En cours de développement\"\***********('/conducteurs/ajouter')\ndef ajouter_conducteur():\n    return \"Ajouter un conducteur - En cours de développement\"\***********('/maintenances')\ndef maintenances():", "detail": "app", "documentation": {}}, {"label": "conducteurs", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def conducteurs():\n    return \"Page des conducteurs - En cours de développement\"\***********('/conducteurs/ajouter')\ndef ajouter_conducteur():\n    return \"Ajouter un conducteur - En cours de développement\"\***********('/maintenances')\ndef maintenances():\n    return \"Page des maintenances - En cours de développement\"\***********('/maintenances/ajouter')\ndef ajouter_maintenance():", "detail": "app", "documentation": {}}, {"label": "ajouter_conducteur", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def ajouter_conducteur():\n    return \"Ajouter un conducteur - En cours de développement\"\***********('/maintenances')\ndef maintenances():\n    return \"Page des maintenances - En cours de développement\"\***********('/maintenances/ajouter')\ndef ajouter_maintenance():\n    return \"Ajouter une maintenance - En cours de développement\"\***********('/affectations')\ndef affectations():", "detail": "app", "documentation": {}}, {"label": "maintenances", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def maintenances():\n    return \"Page des maintenances - En cours de développement\"\***********('/maintenances/ajouter')\ndef ajouter_maintenance():\n    return \"Ajouter une maintenance - En cours de développement\"\***********('/affectations')\ndef affectations():\n    return \"Page des affectations - En cours de développement\"\***********('/rapports')\ndef rapports():", "detail": "app", "documentation": {}}, {"label": "ajouter_maintenance", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def ajouter_maintenance():\n    return \"Ajouter une maintenance - En cours de développement\"\***********('/affectations')\ndef affectations():\n    return \"Page des affectations - En cours de développement\"\***********('/rapports')\ndef rapports():\n    return \"Page des rapports - En cours de développement\"\nif __name__ == '__main__':\n    print(\"Démarrage de l'application GesParc Auto...\")", "detail": "app", "documentation": {}}, {"label": "affectations", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def affectations():\n    return \"Page des affectations - En cours de développement\"\***********('/rapports')\ndef rapports():\n    return \"Page des rapports - En cours de développement\"\nif __name__ == '__main__':\n    print(\"Démarrage de l'application GesParc Auto...\")\n    print(\"Application disponible sur: http://localhost:5001\")\n    app.run(debug=True, host='127.0.0.1', port=5001)", "detail": "app", "documentation": {}}, {"label": "rapports", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def rapports():\n    return \"Page des rapports - En cours de développement\"\nif __name__ == '__main__':\n    print(\"Démarrage de l'application GesParc Auto...\")\n    print(\"Application disponible sur: http://localhost:5001\")\n    app.run(debug=True, host='127.0.0.1', port=5001)", "detail": "app", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "app = Flask(__name__)\napp.secret_key = 'votre_cle_secrete_ici'  # À changer en production\n# Configuration de la base de données\nDATABASE = 'parc_automobile.db'\ndef get_db_connection():\n    \"\"\"Établit une connexion à la base de données SQLite\"\"\"\n    conn = sqlite3.connect(DATABASE)\n    conn.row_factory = sqlite3.Row\n    return conn\***********('/')", "detail": "app", "documentation": {}}, {"label": "app.secret_key", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "app.secret_key = 'votre_cle_secrete_ici'  # À changer en production\n# Configuration de la base de données\nDATABASE = 'parc_automobile.db'\ndef get_db_connection():\n    \"\"\"Établit une connexion à la base de données SQLite\"\"\"\n    conn = sqlite3.connect(DATABASE)\n    conn.row_factory = sqlite3.Row\n    return conn\***********('/')\ndef index():", "detail": "app", "documentation": {}}, {"label": "DATABASE", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "DATABASE = 'parc_automobile.db'\ndef get_db_connection():\n    \"\"\"Établit une connexion à la base de données SQLite\"\"\"\n    conn = sqlite3.connect(DATABASE)\n    conn.row_factory = sqlite3.Row\n    return conn\***********('/')\ndef index():\n    \"\"\"Page d'accueil avec tableau de bord\"\"\"\n    # Récupérer les statistiques", "detail": "app", "documentation": {}}, {"label": "Config", "kind": 6, "importPath": "config", "description": "config", "peekOfCode": "class Config:\n    \"\"\"Configuration de base\"\"\"\n    SECRET_KEY = os.environ.get('SECRET_KEY') or 'gesparc_secret_key_2025'\n    DATABASE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'parc_automobile.db')\n    # Configuration pour le déploiement\n    APPLICATION_ROOT = os.environ.get('APPLICATION_ROOT', '/')\n    PREFERRED_URL_SCHEME = 'http'\nclass DevelopmentConfig(Config):\n    \"\"\"Configuration pour le développement\"\"\"\n    DEBUG = True", "detail": "config", "documentation": {}}, {"label": "DevelopmentConfig", "kind": 6, "importPath": "config", "description": "config", "peekOfCode": "class DevelopmentConfig(Config):\n    \"\"\"Configuration pour le développement\"\"\"\n    DEBUG = True\n    HOST = '127.0.0.1'\n    PORT = 5001\n    APPLICATION_ROOT = '/'\nclass ProductionConfig(Config):\n    \"\"\"Configuration pour la production (Apache)\"\"\"\n    DEBUG = False\n    APPLICATION_ROOT = '/gesparc'", "detail": "config", "documentation": {}}, {"label": "ProductionConfig", "kind": 6, "importPath": "config", "description": "config", "peekOfCode": "class ProductionConfig(Config):\n    \"\"\"Configuration pour la production (Apache)\"\"\"\n    DEBUG = False\n    APPLICATION_ROOT = '/gesparc'\n    PREFERRED_URL_SCHEME = 'http'\nclass ApacheConfig(ProductionConfig):\n    \"\"\"Configuration spécifique pour Apache\"\"\"\n    # Variables d'environnement Apache\n    SCRIPT_NAME = '/gesparc'\n    @staticmethod", "detail": "config", "documentation": {}}, {"label": "ApacheConfig", "kind": 6, "importPath": "config", "description": "config", "peekOfCode": "class ApacheConfig(ProductionConfig):\n    \"\"\"Configuration spécifique pour Apache\"\"\"\n    # Variables d'environnement Apache\n    SCRIPT_NAME = '/gesparc'\n    @staticmethod\n    def init_app(app):\n        \"\"\"Initialisation spécifique pour Apache\"\"\"\n        # Configurer le préfixe URL\n        app.config['APPLICATION_ROOT'] = '/gesparc'\n        # Middleware pour gérer le préfixe", "detail": "config", "documentation": {}}, {"label": "get_config", "kind": 2, "importPath": "config", "description": "config", "peekOfCode": "def get_config():\n    \"\"\"Retourne la configuration appropriée selon l'environnement\"\"\"\n    env = os.environ.get('FLASK_ENV', 'development')\n    # Détecter si on est sous Apache\n    if os.environ.get('SCRIPT_NAME') or os.environ.get('APPLICATION_ROOT'):\n        return config['apache']\n    return config.get(env, config['default'])", "detail": "config", "documentation": {}}, {"label": "config", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "config = {\n    'development': DevelopmentConfig,\n    'production': ProductionConfig,\n    'apache': ApacheConfig,\n    'default': DevelopmentConfig\n}\ndef get_config():\n    \"\"\"Retourne la configuration appropriée selon l'environnement\"\"\"\n    env = os.environ.get('FLASK_ENV', 'development')\n    # Détecter si on est sous Apache", "detail": "config", "documentation": {}}, {"label": "test_rapports_queries", "kind": 2, "importPath": "debug_rapports", "description": "debug_rapports", "peekOfCode": "def test_rapports_queries():\n    \"\"\"Teste les requêtes SQL de la route rapports\"\"\"\n    print(\"🔍 Debug des Requêtes Rapports\")\n    print(\"=\" * 40)\n    try:\n        conn = sqlite3.connect('parc_automobile.db')\n        # Test 1: Statistiques générales\n        print(\"1. Test statistiques générales...\")\n        stats = {}\n        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]", "detail": "debug_rapports", "documentation": {}}, {"label": "test_rapports_avances", "kind": 2, "importPath": "debug_rapports_avances", "description": "debug_rapports_avances", "peekOfCode": "def test_rapports_avances():\n    \"\"\"Teste les nouvelles requêtes des rapports avancés\"\"\"\n    print(\"🔍 Debug des Rapports Avancés\")\n    print(\"=\" * 50)\n    try:\n        conn = sqlite3.connect('parc_automobile.db')\n        # Test 1: Statistiques de base\n        print(\"1. Test statistiques de base...\")\n        stats = {}\n        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]", "detail": "debug_rapports_avances", "documentation": {}}, {"label": "export_to_csv", "kind": 2, "importPath": "export_utils", "description": "export_utils", "peekOfCode": "def export_to_csv(data, headers, filename):\n    \"\"\"\n    Exporte des données au format CSV\n    Args:\n        data: Liste de dictionnaires ou tuples contenant les données\n        headers: Liste des en-têtes de colonnes\n        filename: Nom du fichier (sans extension)\n    Returns:\n        Response Flask avec le fichier CSV\n    \"\"\"", "detail": "export_utils", "documentation": {}}, {"label": "export_to_excel_xlsx", "kind": 2, "importPath": "export_utils", "description": "export_utils", "peekOfCode": "def export_to_excel_xlsx(data, headers, filename, sheet_name=\"Données\"):\n    \"\"\"\n    Exporte des données au format Excel XLSX (moderne)\n    Args:\n        data: Liste de dictionnaires ou tuples contenant les données\n        headers: Liste des en-têtes de colonnes\n        filename: Nom du fichier (sans extension)\n        sheet_name: Nom de la feuille Excel\n    Returns:\n        Response Flask avec le fichier Excel", "detail": "export_utils", "documentation": {}}, {"label": "export_to_excel_xls", "kind": 2, "importPath": "export_utils", "description": "export_utils", "peekOfCode": "def export_to_excel_xls(data, headers, filename, sheet_name=\"Données\"):\n    \"\"\"\n    Exporte des données au format Excel XLS (compatible anciennes versions)\n    Args:\n        data: Liste de dictionnaires ou tuples contenant les données\n        headers: Liste des en-têtes de colonnes\n        filename: Nom du fichier (sans extension)\n        sheet_name: Nom de la feuille Excel\n    Returns:\n        Response Flask avec le fichier Excel", "detail": "export_utils", "documentation": {}}, {"label": "header_to_db_key", "kind": 2, "importPath": "export_utils", "description": "export_utils", "peekOfCode": "def header_to_db_key(header):\n    \"\"\"\n    Convertit un en-tête d'affichage en clé de base de données\n    \"\"\"\n    mapping = {\n        'Immatriculation': 'immatriculation',\n        'Marque': 'marque',\n        'Mod<PERSON><PERSON>': 'modele',\n        'Ann<PERSON>': 'annee',\n        'Couleur': 'couleur',", "detail": "export_utils", "documentation": {}}, {"label": "format_value_for_export", "kind": 2, "importPath": "export_utils", "description": "export_utils", "peekOfCode": "def format_value_for_export(value):\n    \"\"\"\n    Formate une valeur pour l'export\n    \"\"\"\n    if value is None:\n        return ''\n    elif isinstance(value, (int, float)):\n        return value\n    elif isinstance(value, str):\n        return value.strip()", "detail": "export_utils", "documentation": {}}, {"label": "get_export_filename", "kind": 2, "importPath": "export_utils", "description": "export_utils", "peekOfCode": "def get_export_filename(base_name, export_type='csv'):\n    \"\"\"\n    Génère un nom de fichier avec timestamp\n    \"\"\"\n    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n    return f\"{base_name}_{timestamp}\"", "detail": "export_utils", "documentation": {}}, {"label": "PrefixMiddleware", "kind": 6, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "class PrefixMiddleware(object):\n    def __init__(self, app, prefix=''):\n        self.app = app\n        self.prefix = prefix\n    def __call__(self, environ, start_response):\n        if self.prefix and environ['PATH_INFO'].startswith(self.prefix):\n            environ['PATH_INFO'] = environ['PATH_INFO'][len(self.prefix):]\n            environ['SCRIPT_NAME'] = self.prefix\n            return self.app(environ, start_response)\n        elif self.prefix:", "detail": "gesparc_app", "documentation": {}}, {"label": "url_for_prefix", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def url_for_prefix(endpoint, **values):\n    \"\"\"Génère une URL en tenant compte du préfixe /gesparc\"\"\"\n    if USE_PREFIX:\n        return GESPARC_PREFIX + url_for(endpoint, **values)\n    else:\n        return url_for(endpoint, **values)\n# Rendre la fonction disponible dans les templates\n@gesparc_app.context_processor\ndef inject_url_helpers():\n    return dict(", "detail": "gesparc_app", "documentation": {}}, {"label": "inject_url_helpers", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def inject_url_helpers():\n    return dict(\n        url_for_prefix=url_for_prefix,\n        USE_PREFIX=USE_PREFIX,\n        GESPARC_PREFIX=GESPARC_PREFIX if USE_PREFIX else ''\n    )\n    def __call__(self, environ, start_response):\n        if environ['PATH_INFO'].startswith(self.prefix):\n            environ['PATH_INFO'] = environ['PATH_INFO'][len(self.prefix):]\n            environ['SCRIPT_NAME'] = self.prefix", "detail": "gesparc_app", "documentation": {}}, {"label": "format_prix_filter", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def format_prix_filter(prix):\n    \"\"\"Filtre Jinja2 pour formater les prix\"\"\"\n    return format_prix(prix)\n# Configuration de la base de données\nDATABASE = gesparc_app.config.get('DATABASE', 'parc_automobile.db')\n# Configuration de la devise\nDEVISE = 'MAD'  # <PERSON><PERSON><PERSON>\ndef format_prix(prix):\n    \"\"\"Formate un prix avec la devise MAD\"\"\"\n    if prix is None:", "detail": "gesparc_app", "documentation": {}}, {"label": "format_prix", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def format_prix(prix):\n    \"\"\"Formate un prix avec la devise MAD\"\"\"\n    if prix is None:\n        return '-'\n    return f\"{prix:,.2f}\".replace(',', ' ') + f\" {DEVISE}\"\ndef get_db_connection():\n    \"\"\"Établit une connexion à la base de données SQLite\"\"\"\n    conn = sqlite3.connect(DATABASE)\n    conn.row_factory = sqlite3.Row\n    return conn", "detail": "gesparc_app", "documentation": {}}, {"label": "get_db_connection", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def get_db_connection():\n    \"\"\"Établit une connexion à la base de données SQLite\"\"\"\n    conn = sqlite3.connect(DATABASE)\n    conn.row_factory = sqlite3.Row\n    return conn\n@gesparc_app.route('/')\ndef index():\n    \"\"\"Page d'accueil avec tableau de bord\"\"\"\n    # Récupérer les statistiques\n    stats = {", "detail": "gesparc_app", "documentation": {}}, {"label": "index", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def index():\n    \"\"\"Page d'accueil avec tableau de bord\"\"\"\n    # Récupérer les statistiques\n    stats = {\n        'total_vehicules': 0,\n        'vehicules_disponibles': 0,\n        'total_conducteurs': 0,\n        'maintenances_prevues': 0\n    }\n    maintenances_prochaines = []", "detail": "gesparc_app", "documentation": {}}, {"label": "vehicules", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def vehicules():\n    \"\"\"Liste des véhicules\"\"\"\n    try:\n        conn = get_db_connection()\n        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()\n        # Statistiques\n        stats = {}\n        stats['total'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]\n        statuts = conn.execute('''\n            SELECT statut, COUNT(*) as count", "detail": "gesparc_app", "documentation": {}}, {"label": "ajouter_vehicule", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def ajouter_vehicule():\n    \"\"\"Ajouter un nouveau véhicule\"\"\"\n    if request.method == 'POST':\n        try:\n            # Récupérer les données du formulaire\n            immatriculation_brute = request.form['immatriculation'].strip()\n            marque = request.form['marque']\n            modele = request.form['modele']\n            annee = int(request.form['annee'])\n            couleur = request.form.get('couleur', '')", "detail": "gesparc_app", "documentation": {}}, {"label": "voir_vehicule", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def voir_vehicule(id):\n    \"\"\"Voir les détails d'un véhicule\"\"\"\n    try:\n        conn = get_db_connection()\n        vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (id,)).fetchone()\n        if not vehicule:\n            flash('Véhicule non trouvé', 'error')\n            return redirect(url_for('vehicules'))\n        # Récupérer les maintenances du véhicule\n        maintenances = conn.execute('''", "detail": "gesparc_app", "documentation": {}}, {"label": "modifier_vehicule", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def modifier_vehicule(id):\n    \"\"\"Modifier un véhicule\"\"\"\n    try:\n        conn = get_db_connection()\n        vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (id,)).fetchone()\n        if not vehicule:\n            flash('Véhicule non trouvé', 'error')\n            return redirect(url_for('vehicules'))\n        if request.method == 'POST':\n            # Récupérer les données du formulaire", "detail": "gesparc_app", "documentation": {}}, {"label": "supprimer_vehicule", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def supprimer_vehicule(id):\n    \"\"\"Supprimer un véhicule\"\"\"\n    try:\n        conn = get_db_connection()\n        vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (id,)).fetchone()\n        if not vehicule:\n            flash('Véhicule non trouvé', 'error')\n            return redirect(url_for('vehicules'))\n        # Vérifier s'il y a des affectations actives\n        affectations_actives = conn.execute('''", "detail": "gesparc_app", "documentation": {}}, {"label": "conducteurs", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def conducteurs():\n    \"\"\"Liste des conducteurs\"\"\"\n    try:\n        conn = get_db_connection()\n        # Récupérer les conducteurs avec leurs véhicules affectés\n        conducteurs = conn.execute('''\n            SELECT c.*,\n                   CASE WHEN a.vehicule_id IS NOT NULL\n                        THEN v.immatriculation\n                        ELSE NULL", "detail": "gesparc_app", "documentation": {}}, {"label": "ajouter_conducteur", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def ajouter_conducteur():\n    \"\"\"Ajouter un nouveau conducteur\"\"\"\n    if request.method == 'POST':\n        try:\n            # R<PERSON>cupérer les données du formulaire\n            nom = request.form['nom'].strip()\n            prenom = request.form['prenom'].strip()\n            numero_permis = request.form['numero_permis'].strip()\n            date_permis = request.form.get('date_permis', '')\n            telephone = request.form.get('telephone', '').strip()", "detail": "gesparc_app", "documentation": {}}, {"label": "voir_conducteur", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def voir_conducteur(id):\n    \"\"\"Voir les détails d'un conducteur\"\"\"\n    try:\n        conn = get_db_connection()\n        conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (id,)).fetchone()\n        if not conducteur:\n            flash('Conducteur non trouvé', 'error')\n            return redirect(url_for('conducteurs'))\n        # Récupérer les affectations du conducteur\n        affectations = conn.execute('''", "detail": "gesparc_app", "documentation": {}}, {"label": "modifier_conducteur", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def modifier_conducteur(id):\n    \"\"\"Modifier un conducteur\"\"\"\n    try:\n        conn = get_db_connection()\n        conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (id,)).fetchone()\n        if not conducteur:\n            flash('Conducteur non trouvé', 'error')\n            return redirect(url_for('conducteurs'))\n        if request.method == 'POST':\n            # Récupérer les données du formulaire", "detail": "gesparc_app", "documentation": {}}, {"label": "supprimer_conducteur", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def supprimer_conducteur(id):\n    \"\"\"Supprimer un conducteur\"\"\"\n    try:\n        conn = get_db_connection()\n        conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (id,)).fetchone()\n        if not conducteur:\n            flash('Conducteur non trouvé', 'error')\n            return redirect(url_for('conducteurs'))\n        # Vérifier s'il y a des affectations actives\n        affectations_actives = conn.execute('''", "detail": "gesparc_app", "documentation": {}}, {"label": "maintenances", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def maintenances():\n    \"\"\"Liste des maintenances\"\"\"\n    try:\n        conn = get_db_connection()\n        maintenances = conn.execute('''\n            SELECT m.*, v.immatriculation, v.marque, v.modele\n            FROM maintenances m\n            JOIN vehicules v ON m.vehicule_id = v.id\n            ORDER BY m.date_maintenance DESC\n        ''').fetchall()", "detail": "gesparc_app", "documentation": {}}, {"label": "ajouter_maintenance", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def ajouter_maintenance():\n    \"\"\"Planifier une nouvelle maintenance\"\"\"\n    if request.method == 'POST':\n        try:\n            # Récupérer les données du formulaire\n            vehicule_id = int(request.form['vehicule_id'])\n            type_maintenance = request.form['type_maintenance']\n            description = request.form.get('description', '').strip()\n            date_maintenance = request.form['date_maintenance']\n            cout_estime = request.form.get('cout_estime', '')", "detail": "gesparc_app", "documentation": {}}, {"label": "demarrer_maintenance", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def demarrer_maintenance(id):\n    \"\"\"Démarrer une maintenance (planifiée -> en cours)\"\"\"\n    try:\n        conn = get_db_connection()\n        # Vérifier que la maintenance existe et est planifiée\n        maintenance = conn.execute('SELECT * FROM maintenances WHERE id = ?', (id,)).fetchone()\n        if not maintenance:\n            flash('Maintenance non trouvée', 'error')\n            conn.close()\n            return redirect(url_for('maintenances'))", "detail": "gesparc_app", "documentation": {}}, {"label": "terminer_maintenance", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def terminer_maintenance(id):\n    \"\"\"Terminer une maintenance (en cours -> terminée)\"\"\"\n    try:\n        conn = get_db_connection()\n        # Vérifier que la maintenance existe et est en cours\n        maintenance = conn.execute('SELECT * FROM maintenances WHERE id = ?', (id,)).fetchone()\n        if not maintenance:\n            flash('Maintenance non trouvée', 'error')\n            conn.close()\n            return redirect(url_for('maintenances'))", "detail": "gesparc_app", "documentation": {}}, {"label": "voir_maintenance", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def voir_maintenance(id):\n    \"\"\"Voir les détails d'une maintenance\"\"\"\n    try:\n        conn = get_db_connection()\n        # Récupérer la maintenance avec les détails du véhicule\n        maintenance = conn.execute('''\n            SELECT m.*, v.immatriculation, v.marque, v.modele, v.annee, v.kilometrage\n            FROM maintenances m\n            JOIN vehicules v ON m.vehicule_id = v.id\n            WHERE m.id = ?", "detail": "gesparc_app", "documentation": {}}, {"label": "modifier_maintenance", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def modifier_maintenance(id):\n    \"\"\"Modifier une maintenance\"\"\"\n    if request.method == 'POST':\n        try:\n            # R<PERSON>cupérer les données du formulaire\n            type_maintenance = request.form['type_maintenance']\n            description = request.form.get('description', '').strip()\n            date_maintenance = request.form['date_maintenance']\n            cout = request.form.get('cout', '')\n            garage = request.form.get('garage', '').strip()", "detail": "gesparc_app", "documentation": {}}, {"label": "valider_immatriculation_api", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def valider_immatriculation_api():\n    \"\"\"API pour valider une immatriculation en temps réel\"\"\"\n    try:\n        data = request.get_json()\n        immatriculation = data.get('immatriculation', '').strip()\n        if not immatriculation:\n            return jsonify({\n                'valide': False,\n                'message': 'Immatriculation vide'\n            })", "detail": "gesparc_app", "documentation": {}}, {"label": "guide_immatriculation", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def guide_immatriculation():\n    \"\"\"Guide des formats d'immatriculation marocaine\"\"\"\n    return render_template('guide_immatriculation.html')\n# Routes pour les affectations\n@gesparc_app.route('/affectations')\ndef affectations():\n    \"\"\"Liste des affectations\"\"\"\n    try:\n        conn = get_db_connection()\n        affectations = conn.execute('''", "detail": "gesparc_app", "documentation": {}}, {"label": "affectations", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def affectations():\n    \"\"\"Liste des affectations\"\"\"\n    try:\n        conn = get_db_connection()\n        affectations = conn.execute('''\n            SELECT a.*, v.immatriculation, v.marque, v.modele,\n                   c.nom, c.prenom\n            FROM affectations a\n            JOIN vehicules v ON a.vehicule_id = v.id\n            JOIN conducteurs c ON a.conducteur_id = c.id", "detail": "gesparc_app", "documentation": {}}, {"label": "ajouter_affectation", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def ajouter_affectation():\n    \"\"\"Ajouter une nouvelle affectation\"\"\"\n    if request.method == 'POST':\n        try:\n            # R<PERSON>cupérer les données du formulaire\n            vehicule_id = int(request.form['vehicule_id'])\n            conducteur_id = int(request.form['conducteur_id'])\n            date_debut = request.form['date_debut']\n            date_fin = request.form.get('date_fin', '')\n            commentaire = request.form.get('commentaire', '').strip()", "detail": "gesparc_app", "documentation": {}}, {"label": "terminer_affectation", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def terminer_affectation(id):\n    \"\"\"Terminer une affectation\"\"\"\n    try:\n        conn = get_db_connection()\n        # Récupérer l'affectation\n        affectation = conn.execute('''\n            SELECT a.*, v.immatriculation, c.nom, c.prenom\n            FROM affectations a\n            JOIN vehicules v ON a.vehicule_id = v.id\n            JOIN conducteurs c ON a.conducteur_id = c.id", "detail": "gesparc_app", "documentation": {}}, {"label": "rapports", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def rapports():\n    \"\"\"Page des rapports et statistiques\"\"\"\n    print(\"🔍 DEBUG: Début de la route rapports\")\n    try:\n        print(\"🔍 DEBUG: Connexion à la base de données\")\n        conn = get_db_connection()\n        # Statistiques générales\n        print(\"🔍 DEBUG: Calcul des statistiques générales\")\n        stats = {}\n        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]", "detail": "gesparc_app", "documentation": {}}, {"label": "api_dashboard_data", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def api_dashboard_data():\n    \"\"\"API pour récupérer les données du dashboard en temps réel\"\"\"\n    try:\n        conn = get_db_connection()\n        # Données de base\n        data = {\n            'timestamp': datetime.now().isoformat(),\n            'stats': {\n                'total_vehicules': conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0],\n                'vehicules_disponibles': conn.execute(\"SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'\").fetchone()[0],", "detail": "gesparc_app", "documentation": {}}, {"label": "api_dashboard_filters", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def api_dashboard_filters():\n    \"\"\"API pour appliquer des filtres au dashboard\"\"\"\n    try:\n        period = request.args.get('period', '30')\n        vehicle_type = request.args.get('vehicle_type', 'all')\n        status = request.args.get('status', 'all')\n        conn = get_db_connection()\n        # Construction de la requête avec filtres\n        where_conditions = []\n        params = []", "detail": "gesparc_app", "documentation": {}}, {"label": "export_rapports", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def export_rapports(format):\n    \"\"\"Exporter les rapports en différents formats\"\"\"\n    try:\n        if format == 'csv':\n            return export_rapports_csv()\n        elif format == 'excel':\n            return export_rapports_excel()\n        else:\n            flash('Format d\\'export non supporté', 'error')\n            return redirect(url_for('rapports'))", "detail": "gesparc_app", "documentation": {}}, {"label": "export_rapports_csv", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def export_rapports_csv():\n    \"\"\"Exporter les rapports en CSV\"\"\"\n    import csv\n    import io\n    from flask import make_response\n    try:\n        conn = get_db_connection()\n        output = io.StringIO()\n        writer = csv.writer(output)\n        # Export des véhicules", "detail": "gesparc_app", "documentation": {}}, {"label": "export_rapports_excel", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def export_rapports_excel():\n    \"\"\"Exporter les rapports en Excel\"\"\"\n    try:\n        # Tentative d'import d'openpyxl\n        try:\n            import openpyxl\n            from openpyxl.styles import Font, PatternFill\n        except ImportError:\n            flash('Module openpyxl non installé. Export Excel non disponible.', 'error')\n            return redirect(url_for('rapports'))", "detail": "gesparc_app", "documentation": {}}, {"label": "export_vehicules", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def export_vehicules(format):\n    \"\"\"Exporter la liste des véhicules\"\"\"\n    try:\n        conn = get_db_connection()\n        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()\n        conn.close()\n        headers = ['Immatriculation', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>uleur',\n                  'Kilométrage', 'Carburant', 'Statut', 'Date acquisition', 'Prix acquisition (MAD)']\n        filename = get_export_filename('vehicules', format)\n        if format == 'csv':", "detail": "gesparc_app", "documentation": {}}, {"label": "export_conducteurs", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def export_conducteurs(format):\n    \"\"\"Exporter la liste des conducteurs\"\"\"\n    try:\n        conn = get_db_connection()\n        conducteurs = conn.execute('SELECT * FROM conducteurs ORDER BY nom, prenom').fetchall()\n        conn.close()\n        headers = ['Nom', 'Prénom', 'Numéro permis', 'Date permis',\n                  'Téléphone', 'Email', 'Statut']\n        filename = get_export_filename('conducteurs', format)\n        if format == 'csv':", "detail": "gesparc_app", "documentation": {}}, {"label": "export_maintenances", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def export_maintenances(format):\n    \"\"\"Exporter la liste des maintenances\"\"\"\n    try:\n        conn = get_db_connection()\n        maintenances = conn.execute('''\n            SELECT m.type_maintenance, m.description, m.date_maintenance,\n                   m.cout, m.garage, m.statut, v.immatriculation, v.marque, v.modele\n            FROM maintenances m\n            JOIN vehicules v ON m.vehicule_id = v.id\n            ORDER BY m.date_maintenance DESC", "detail": "gesparc_app", "documentation": {}}, {"label": "export_affectations", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def export_affectations(format):\n    \"\"\"Exporter la liste des affectations\"\"\"\n    try:\n        conn = get_db_connection()\n        affectations = conn.execute('''\n            SELECT v.immatriculation, v.marque, v.modele, c.nom, c.prenom,\n                   a.date_debut, a.date_fin, a.statut, a.commentaire\n            FROM affectations a\n            JOIN vehicules v ON a.vehicule_id = v.id\n            JOIN conducteurs c ON a.conducteur_id = c.id", "detail": "gesparc_app", "documentation": {}}, {"label": "export_complet", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def export_complet(format):\n    \"\"\"Exporter toutes les données dans un fichier multi-feuilles (Excel uniquement)\"\"\"\n    try:\n        if format not in ['xlsx', 'xls']:\n            flash('Export complet disponible uniquement en format Excel', 'error')\n            return redirect(url_for('rapports'))\n        conn = get_db_connection()\n        # Récupérer toutes les données\n        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()\n        conducteurs = conn.execute('SELECT * FROM conducteurs ORDER BY nom, prenom').fetchall()", "detail": "gesparc_app", "documentation": {}}, {"label": "export_complet_excel", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def export_complet_excel(vehicules, conducteurs, maintenances, affectations, filename, format):\n    \"\"\"Exporte toutes les données dans un fichier Excel multi-feuilles\"\"\"\n    if format == 'xlsx':\n        import openpyxl\n        from openpyxl.styles import Font, PatternFill\n        wb = openpyxl.Workbook()\n        # Supprimer la feuille par défaut\n        wb.remove(wb.active)\n        # Feuille Véhicules\n        ws_vehicules = wb.create_sheet(\"Véhicules\")", "detail": "gesparc_app", "documentation": {}}, {"label": "add_data_to_sheet", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def add_data_to_sheet(worksheet, data, headers):\n    \"\"\"Ajoute des données à une feuille Excel avec formatage\"\"\"\n    from openpyxl.styles import Font, PatternFill, Alignment\n    # Styles pour les en-têtes\n    header_font = Font(bold=True, color=\"FFFFFF\")\n    header_fill = PatternFill(start_color=\"366092\", end_color=\"366092\", fill_type=\"solid\")\n    header_alignment = Alignment(horizontal=\"center\", vertical=\"center\")\n    # Écrire les en-têtes\n    for col, header in enumerate(headers, 1):\n        cell = worksheet.cell(row=1, column=col, value=header)", "detail": "gesparc_app", "documentation": {}}, {"label": "analytics_matplotlib", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def analytics_matplotlib():\n    \"\"\"Page des analytics avec graphiques Matplotlib\"\"\"\n    try:\n        analytics = GesparcAnalytics()\n        # G<PERSON><PERSON>rer tous les graphiques\n        charts = analytics.create_comprehensive_report()\n        return render_template('analytics_matplotlib.html', charts=charts)\n    except Exception as e:\n        flash(f'Erreur lors de la génération des analytics: {e}', 'error')\n        return render_template('analytics_matplotlib.html', charts={})", "detail": "gesparc_app", "documentation": {}}, {"label": "api_analytics_chart", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def api_analytics_chart(chart_type):\n    \"\"\"API pour générer un graphique spécifique\"\"\"\n    try:\n        analytics = GesparcAnalytics()\n        if chart_type == 'evolution':\n            period = request.args.get('period', 12, type=int)\n            chart_data = analytics.create_maintenance_evolution_chart(period)\n        elif chart_type == 'dashboard':\n            chart_data = analytics.create_vehicle_analysis_dashboard()\n        elif chart_type == 'costs':", "detail": "gesparc_app", "documentation": {}}, {"label": "download_analytics_chart", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def download_analytics_chart(chart_type):\n    \"\"\"Télécharger un graphique en PNG\"\"\"\n    try:\n        analytics = GesparcAnalytics()\n        if chart_type == 'evolution':\n            chart_data = analytics.create_maintenance_evolution_chart()\n        elif chart_type == 'dashboard':\n            chart_data = analytics.create_vehicle_analysis_dashboard()\n        elif chart_type == 'costs':\n            chart_data = analytics.create_maintenance_cost_analysis()", "detail": "gesparc_app", "documentation": {}}, {"label": "test_prefix", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def test_prefix():\n    \"\"\"Page de test pour vérifier la configuration du préfixe\"\"\"\n    return render_template('test_prefix.html')\n@gesparc_app.route('/api/prefix-info')\ndef api_prefix_info():\n    \"\"\"API pour obtenir les informations de configuration du préfixe\"\"\"\n    return jsonify({\n        'use_prefix': USE_PREFIX,\n        'prefix': GESPARC_PREFIX if USE_PREFIX else None,\n        'application_root': gesparc_app.config.get('APPLICATION_ROOT'),", "detail": "gesparc_app", "documentation": {}}, {"label": "api_prefix_info", "kind": 2, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "def api_prefix_info():\n    \"\"\"API pour obtenir les informations de configuration du préfixe\"\"\"\n    return jsonify({\n        'use_prefix': USE_PREFIX,\n        'prefix': GESPARC_PREFIX if USE_PREFIX else None,\n        'application_root': gesparc_app.config.get('APPLICATION_ROOT'),\n        'script_name': os.environ.get('SCRIPT_NAME'),\n        'request_url': request.url,\n        'request_path': request.path,\n        'request_script_root': request.script_root,", "detail": "gesparc_app", "documentation": {}}, {"label": "gesparc_app", "kind": 5, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "gesparc_app = Flask(__name__)\n# Charger la configuration appropriée\nconfig_class = get_config()\ngesparc_app.config.from_object(config_class)\n# Configuration pour Apache avec préfixe /gesparc\nGESPARC_PREFIX = '/gesparc'\n# Détecter si on est dans un contexte Apache ou si on veut forcer le préfixe\nUSE_PREFIX = (\n    os.environ.get('SCRIPT_NAME') == GESPARC_PREFIX or\n    os.environ.get('GESPARC_USE_PREFIX', '').lower() == 'true' or", "detail": "gesparc_app", "documentation": {}}, {"label": "config_class", "kind": 5, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "config_class = get_config()\ngesparc_app.config.from_object(config_class)\n# Configuration pour Apache avec préfixe /gesparc\nGESPARC_PREFIX = '/gesparc'\n# Détecter si on est dans un contexte Apache ou si on veut forcer le préfixe\nUSE_PREFIX = (\n    os.environ.get('SCRIPT_NAME') == GESPARC_PREFIX or\n    os.environ.get('GESPARC_USE_PREFIX', '').lower() == 'true' or\n    '--prefix' in sys.argv\n)", "detail": "gesparc_app", "documentation": {}}, {"label": "GESPARC_PREFIX", "kind": 5, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "GESPARC_PREFIX = '/gesparc'\n# Détecter si on est dans un contexte Apache ou si on veut forcer le préfixe\nUSE_PREFIX = (\n    os.environ.get('SCRIPT_NAME') == GESPARC_PREFIX or\n    os.environ.get('GESPARC_USE_PREFIX', '').lower() == 'true' or\n    '--prefix' in sys.argv\n)\nif USE_PREFIX:\n    gesparc_app.config['APPLICATION_ROOT'] = GESPARC_PREFIX\n    print(f\"🌐 Configuration Flask avec préfixe: {GESPARC_PREFIX}\")", "detail": "gesparc_app", "documentation": {}}, {"label": "USE_PREFIX", "kind": 5, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "USE_PREFIX = (\n    os.environ.get('SCRIPT_NAME') == GESPARC_PREFIX or\n    os.environ.get('GESPARC_USE_PREFIX', '').lower() == 'true' or\n    '--prefix' in sys.argv\n)\nif USE_PREFIX:\n    gesparc_app.config['APPLICATION_ROOT'] = GESPARC_PREFIX\n    print(f\"🌐 Configuration Flask avec préfixe: {GESPARC_PREFIX}\")\n# Initialisation spécifique pour Apache si nécessaire\nif hasattr(config_class, 'init_app'):", "detail": "gesparc_app", "documentation": {}}, {"label": "DATABASE", "kind": 5, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "DATABASE = gesparc_app.config.get('DATABASE', 'parc_automobile.db')\n# Configuration de la devise\nDEVISE = 'MAD'  # <PERSON><PERSON>ham <PERSON>ain\ndef format_prix(prix):\n    \"\"\"Formate un prix avec la devise MAD\"\"\"\n    if prix is None:\n        return '-'\n    return f\"{prix:,.2f}\".replace(',', ' ') + f\" {DEVISE}\"\ndef get_db_connection():\n    \"\"\"Établit une connexion à la base de données SQLite\"\"\"", "detail": "gesparc_app", "documentation": {}}, {"label": "DEVISE", "kind": 5, "importPath": "gesparc_app", "description": "gesparc_app", "peekOfCode": "DEVISE = 'MAD'  # <PERSON><PERSON><PERSON>\ndef format_prix(prix):\n    \"\"\"Formate un prix avec la devise MAD\"\"\"\n    if prix is None:\n        return '-'\n    return f\"{prix:,.2f}\".replace(',', ' ') + f\" {DEVISE}\"\ndef get_db_connection():\n    \"\"\"Établit une connexion à la base de données SQLite\"\"\"\n    conn = sqlite3.connect(DATABASE)\n    conn.row_factory = sqlite3.Row", "detail": "gesparc_app", "documentation": {}}, {"label": "ImmatriculationMaroc", "kind": 6, "importPath": "immatriculation_maroc", "description": "immatriculation_maroc", "peekOfCode": "class ImmatriculationMaroc:\n    \"\"\"Classe pour gérer les immatriculations marocaines\"\"\"\n    # Patterns pour les différents formats (basés sur la structure officielle)\n    PATTERNS = {\n        # Format standard actuel : NNNNN ل NN (chiffres + lettre + code territorial)\n        # Accepte de 1 à 5 chiffres pour le numéro séquentiel\n        'standard_actuel': r'^(\\d{1,5})\\s*([أبتثجحخدذرزسشصضطظعغفقكلمنهوي])\\s*(\\d{1,2})$',  # Lettre arabe\n        'standard_actuel_latin': r'^(\\d{1,5})\\s*([A-Z])\\s*(\\d{1,2})$',  # Lettre latine équivalente\n        # Format avec tirets (souvent utilisé pour la saisie)\n        # Accepte de 1 à 5 chiffres pour le numéro séquentiel", "detail": "immatriculation_maroc", "documentation": {}}, {"label": "tester_immatriculations", "kind": 2, "importPath": "immatriculation_maroc", "description": "immatriculation_maroc", "peekOfCode": "def tester_immatriculations():\n    \"\"\"Fonction de test pour les immatriculations\"\"\"\n    print(\"🇲🇦 Test des Immatriculations Marocaines\")\n    print(\"=\" * 50)\n    # Tests de validation avec les vrais formats marocains (y compris formats courts)\n    tests = [\n        '12345 A 1',     # Standard actuel (5 chiffres)\n        '1234 B 6',      # Standard actuel (4 chiffres)\n        '123 A 1',       # Format court (3 chiffres) - VALIDE\n        '12 C 16',       # Format court (2 chiffres) - VALIDE", "detail": "immatriculation_maroc", "documentation": {}}, {"label": "init_database", "kind": 2, "importPath": "init_db", "description": "init_db", "peekOfCode": "def init_database():\n    \"\"\"Initialise la base de données avec les tables nécessaires\"\"\"\n    # Supprimer la base existante si elle existe\n    if os.path.exists(DATABASE):\n        os.remove(DATABASE)\n        print(f\"Base de données existante supprimée: {DATABASE}\")\n    conn = sqlite3.connect(DATABASE)\n    print(f\"Création de la base de données: {DATABASE}\")\n    # Table des véhicules\n    conn.execute('''", "detail": "init_db", "documentation": {}}, {"label": "insert_sample_data", "kind": 2, "importPath": "init_db", "description": "init_db", "peekOfCode": "def insert_sample_data(conn):\n    \"\"\"Insère des données de test\"\"\"\n    print(\"Insertion des données de test...\")\n    # Véhicules de test\n    vehicules_test = [\n        ('AB-123-CD', 'Peugeot', '308', 2020, 'Blanc', 25000, 'Essence', 'disponible', '2020-01-15', 18000),\n        ('EF-456-GH', 'Renault', 'Clio', 2019, 'Rouge', 35000, 'Diesel', 'affecte', '2019-03-20', 15000),\n        ('IJ-789-KL', 'Citroën', 'C3', 2021, 'Bleu', 15000, 'Essence', 'disponible', '2021-06-10', 16500),\n        ('MN-012-OP', 'Volkswagen', 'Golf', 2018, 'Gris', 45000, 'Diesel', 'en_maintenance', '2018-09-05', 22000),\n    ]", "detail": "init_db", "documentation": {}}, {"label": "DATABASE", "kind": 5, "importPath": "init_db", "description": "init_db", "peekOfCode": "DATABASE = 'parc_automobile.db'\ndef init_database():\n    \"\"\"Initialise la base de données avec les tables nécessaires\"\"\"\n    # Supprimer la base existante si elle existe\n    if os.path.exists(DATABASE):\n        os.remove(DATABASE)\n        print(f\"Base de données existante supprimée: {DATABASE}\")\n    conn = sqlite3.connect(DATABASE)\n    print(f\"Création de la base de données: {DATABASE}\")\n    # Table des véhicules", "detail": "init_db", "documentation": {}}, {"label": "GesparcAnalytics", "kind": 6, "importPath": "matplotlib_analytics", "description": "matplotlib_analytics", "peekOfCode": "class GesparcAnalytics:\n    \"\"\"Générateur de graphiques analytics pour GesParc Auto\"\"\"\n    def __init__(self, db_path: str = 'parc_automobile.db'):\n        \"\"\"\n        Initialise le générateur d'analytics\n        Args:\n            db_path: Chemin vers la base de données SQLite\n        \"\"\"\n        self.db_path = db_path\n        self.setup_style()", "detail": "matplotlib_analytics", "documentation": {}}, {"label": "Vehicule", "kind": 6, "importPath": "models", "description": "models", "peekOfCode": "class Vehicule:\n    def __init__(self, id=None, immatriculation=None, marque=None, modele=None, \n                 annee=None, couleur=None, kilometrage=0, carburant=None, \n                 statut='disponible', date_acquisition=None, prix_acquisition=None):\n        self.id = id\n        self.immatriculation = immatriculation\n        self.marque = marque\n        self.modele = modele\n        self.annee = annee\n        self.couleur = couleur", "detail": "models", "documentation": {}}, {"label": "Conducteur", "kind": 6, "importPath": "models", "description": "models", "peekOfCode": "class Conducteur:\n    def __init__(self, id=None, nom=None, prenom=None, numero_permis=None, \n                 date_permis=None, telephone=None, email=None, statut='actif'):\n        self.id = id\n        self.nom = nom\n        self.prenom = prenom\n        self.numero_permis = numero_permis\n        self.date_permis = date_permis\n        self.telephone = telephone\n        self.email = email", "detail": "models", "documentation": {}}, {"label": "Maintenance", "kind": 6, "importPath": "models", "description": "models", "peekOfCode": "class Maintenance:\n    def __init__(self, id=None, vehicule_id=None, type_maintenance=None, description=None,\n                 date_maintenance=None, cout=None, kilometrage_maintenance=None, \n                 garage=None, statut='planifiee'):\n        self.id = id\n        self.vehicule_id = vehicule_id\n        self.type_maintenance = type_maintenance\n        self.description = description\n        self.date_maintenance = date_maintenance\n        self.cout = cout", "detail": "models", "documentation": {}}, {"label": "Affectation", "kind": 6, "importPath": "models", "description": "models", "peekOfCode": "class Affectation:\n    def __init__(self, id=None, vehicule_id=None, conducteur_id=None, date_debut=None,\n                 date_fin=None, statut='active', commentaire=None):\n        self.id = id\n        self.vehicule_id = vehicule_id\n        self.conducteur_id = conducteur_id\n        self.date_debut = date_debut\n        self.date_fin = date_fin\n        self.statut = statut\n        self.commentaire = commentaire", "detail": "models", "documentation": {}}, {"label": "get_db_connection", "kind": 2, "importPath": "models", "description": "models", "peekOfCode": "def get_db_connection():\n    \"\"\"Établit une connexion à la base de données SQLite\"\"\"\n    conn = sqlite3.connect(DATABASE)\n    conn.row_factory = sqlite3.Row\n    return conn\nclass Vehicule:\n    def __init__(self, id=None, immatriculation=None, marque=None, modele=None, \n                 annee=None, couleur=None, kilometrage=0, carburant=None, \n                 statut='disponible', date_acquisition=None, prix_acquisition=None):\n        self.id = id", "detail": "models", "documentation": {}}, {"label": "DATABASE", "kind": 5, "importPath": "models", "description": "models", "peekOfCode": "DATABASE = 'parc_automobile.db'\ndef get_db_connection():\n    \"\"\"Établit une connexion à la base de données SQLite\"\"\"\n    conn = sqlite3.connect(DATABASE)\n    conn.row_factory = sqlite3.Row\n    return conn\nclass Vehicule:\n    def __init__(self, id=None, immatriculation=None, marque=None, modele=None, \n                 annee=None, couleur=None, kilometrage=0, carburant=None, \n                 statut='disponible', date_acquisition=None, prix_acquisition=None):", "detail": "models", "documentation": {}}, {"label": "os.environ['FLASK_ENV']", "kind": 5, "importPath": "start_gesparc_apache", "description": "start_gesparc_apache", "peekOfCode": "os.environ['FLASK_ENV'] = 'production'\nos.environ['APPLICATION_ROOT'] = '/gesparc'\nos.environ['SCRIPT_NAME'] = '/gesparc'\n# Changer vers le répertoire de l'application\napp_dir = os.path.dirname(os.path.abspath(__file__))\nos.chdir(app_dir)\n# Ajouter le répertoire au path Python\nif app_dir not in sys.path:\n    sys.path.insert(0, app_dir)\n# Importer et lancer l'application", "detail": "start_gesparc_apache", "documentation": {}}, {"label": "os.environ['APPLICATION_ROOT']", "kind": 5, "importPath": "start_gesparc_apache", "description": "start_gesparc_apache", "peekOfCode": "os.environ['APPLICATION_ROOT'] = '/gesparc'\nos.environ['SCRIPT_NAME'] = '/gesparc'\n# Changer vers le répertoire de l'application\napp_dir = os.path.dirname(os.path.abspath(__file__))\nos.chdir(app_dir)\n# Ajouter le répertoire au path Python\nif app_dir not in sys.path:\n    sys.path.insert(0, app_dir)\n# Importer et lancer l'application\nfrom gesparc_app import gesparc_app", "detail": "start_gesparc_apache", "documentation": {}}, {"label": "os.environ['SCRIPT_NAME']", "kind": 5, "importPath": "start_gesparc_apache", "description": "start_gesparc_apache", "peekOfCode": "os.environ['SCRIPT_NAME'] = '/gesparc'\n# Changer vers le répertoire de l'application\napp_dir = os.path.dirname(os.path.abspath(__file__))\nos.chdir(app_dir)\n# Ajouter le répertoire au path Python\nif app_dir not in sys.path:\n    sys.path.insert(0, app_dir)\n# Importer et lancer l'application\nfrom gesparc_app import gesparc_app\nif __name__ == '__main__':", "detail": "start_gesparc_apache", "documentation": {}}, {"label": "app_dir", "kind": 5, "importPath": "start_gesparc_apache", "description": "start_gesparc_apache", "peekOfCode": "app_dir = os.path.dirname(os.path.abspath(__file__))\nos.chdir(app_dir)\n# Ajouter le répertoire au path Python\nif app_dir not in sys.path:\n    sys.path.insert(0, app_dir)\n# Importer et lancer l'application\nfrom gesparc_app import gesparc_app\nif __name__ == '__main__':\n    print(\"=\" * 60)\n    print(\"🚗 GesParc Auto - Mode Apache (/gesparc)\")", "detail": "start_gesparc_apache", "documentation": {}}, {"label": "start_with_prefix", "kind": 2, "importPath": "start_gesparc_prefix", "description": "start_gesparc_prefix", "peekOfCode": "def start_with_prefix():\n    \"\"\"Démarre GesParc Auto avec le préfixe /gesparc\"\"\"\n    print(\"🚀 Démarrage de GesParc Auto avec préfixe /gesparc\")\n    print(\"=\" * 60)\n    # Définir la variable d'environnement pour activer le préfixe\n    os.environ['GESPARC_USE_PREFIX'] = 'true'\n    os.environ['SCRIPT_NAME'] = '/gesparc'\n    # Vérifier que le fichier principal existe\n    app_file = Path('gesparc_app.py')\n    if not app_file.exists():", "detail": "start_gesparc_prefix", "documentation": {}}, {"label": "start_normal", "kind": 2, "importPath": "start_gesparc_prefix", "description": "start_gesparc_prefix", "peekOfCode": "def start_normal():\n    \"\"\"Démarre GesParc Auto sans préfixe (mode normal)\"\"\"\n    print(\"🚀 Démarrage de GesParc Auto (mode normal)\")\n    print(\"=\" * 50)\n    # S'assurer que les variables de préfixe ne sont pas définies\n    os.environ.pop('GESPARC_USE_PREFIX', None)\n    os.environ.pop('SCRIPT_NAME', None)\n    print(\"📍 Configuration:\")\n    print(f\"   Répertoire: {os.getcwd()}\")\n    print(f\"   Mode: Normal (sans préfixe)\")", "detail": "start_gesparc_prefix", "documentation": {}}, {"label": "show_help", "kind": 2, "importPath": "start_gesparc_prefix", "description": "start_gesparc_prefix", "peekOfCode": "def show_help():\n    \"\"\"Affiche l'aide\"\"\"\n    print(\"🔧 Script de Démarrage GesParc Auto\")\n    print(\"=\" * 40)\n    print()\n    print(\"Usage:\")\n    print(\"  python start_gesparc_prefix.py [option]\")\n    print()\n    print(\"Options:\")\n    print(\"  --prefix    Démarrer avec préfixe /gesparc\")", "detail": "start_gesparc_prefix", "documentation": {}}, {"label": "test_configuration", "kind": 2, "importPath": "start_gesparc_prefix", "description": "start_gesparc_prefix", "peekOfCode": "def test_configuration():\n    \"\"\"Teste la configuration avec et sans préfixe\"\"\"\n    print(\"🧪 Test de Configuration GesParc Auto\")\n    print(\"=\" * 45)\n    import requests\n    import time\n    # Test 1: Mode normal\n    print(\"\\n1. Test du mode normal...\")\n    os.environ.pop('GESPARC_USE_PREFIX', None)\n    os.environ.pop('SCRIPT_NAME', None)", "detail": "start_gesparc_prefix", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "start_gesparc_prefix", "description": "start_gesparc_prefix", "peekOfCode": "def main():\n    \"\"\"Fonction principale\"\"\"\n    if len(sys.argv) == 1:\n        # Aucun argument, demander à l'utilisateur\n        print(\"🔧 GesParc Auto - Sélection du Mode de Démarrage\")\n        print(\"=\" * 55)\n        print()\n        print(\"Choisissez le mode de démarrage:\")\n        print(\"  1. Mode normal (http://localhost:5001/)\")\n        print(\"  2. Mode préfixe (http://localhost:5001/gesparc/)\")", "detail": "start_gesparc_prefix", "documentation": {}}, {"label": "test_apache_access", "kind": 2, "importPath": "test_apache", "description": "test_apache", "peekOfCode": "def test_apache_access():\n    \"\"\"Teste l'accès via Apache\"\"\"\n    print(\"🔍 Test d'accès à GesParc Auto via Apache\")\n    print(\"=\" * 50)\n    # Test 1: Accès au dossier gesparc\n    print(\"1. Test d'accès au dossier gesparc...\")\n    try:\n        response = requests.get(\"http://localhost/gesparc/\", timeout=5)\n        if response.status_code == 200:\n            print(\"   ✅ Accès OK (Status: 200)\")", "detail": "test_apache", "documentation": {}}, {"label": "test_flask_only", "kind": 2, "importPath": "test_apache", "description": "test_apache", "peekOfCode": "def test_flask_only():\n    \"\"\"Teste uniquement Flask\"\"\"\n    print(\"🔍 Test d'accès direct à Flask\")\n    print(\"=\" * 30)\n    try:\n        response = requests.get(\"http://localhost:5001/\", timeout=5)\n        if response.status_code == 200:\n            print(\"✅ Flask accessible sur http://localhost:5001/\")\n            return True\n        else:", "detail": "test_apache", "documentation": {}}, {"label": "check_apache_running", "kind": 2, "importPath": "test_apache_config", "description": "test_apache_config", "peekOfCode": "def check_apache_running():\n    \"\"\"Vérifier si Apache est en cours d'exécution\"\"\"\n    try:\n        response = requests.get(\"http://localhost\", timeout=3)\n        return True\n    except:\n        return False\ndef check_flask_running():\n    \"\"\"Vérifier si Flask est en cours d'exécution\"\"\"\n    try:", "detail": "test_apache_config", "documentation": {}}, {"label": "check_flask_running", "kind": 2, "importPath": "test_apache_config", "description": "test_apache_config", "peekOfCode": "def check_flask_running():\n    \"\"\"Vérifier si Flask est en cours d'exécution\"\"\"\n    try:\n        response = requests.get(\"http://localhost:5001\", timeout=3)\n        return response.status_code == 200\n    except:\n        return False\ndef test_url(url, description):\n    \"\"\"Tester une URL spécifique\"\"\"\n    try:", "detail": "test_apache_config", "documentation": {}}, {"label": "test_url", "kind": 2, "importPath": "test_apache_config", "description": "test_apache_config", "peekOfCode": "def test_url(url, description):\n    \"\"\"Tester une URL spécifique\"\"\"\n    try:\n        response = requests.get(url, timeout=10, allow_redirects=True)\n        status = \"✅\" if response.status_code == 200 else \"❌\"\n        print(f\"{status} {description:<30} → {response.status_code}\")\n        if response.history:\n            print(f\"   ↳ Redirigé depuis: {response.history[0].url}\")\n            print(f\"   ↳ Vers: {response.url}\")\n        return response.status_code == 200", "detail": "test_apache_config", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_apache_config", "description": "test_apache_config", "peekOfCode": "def main():\n    print(\"🔍 Test de Configuration Apache pour GesParc Auto\")\n    print(\"=\" * 60)\n    # 1. Vérifier Apache\n    print(\"\\n1. Vérification d'Apache...\")\n    if check_apache_running():\n        print(\"   ✅ Apache est en cours d'exécution\")\n    else:\n        print(\"   ❌ Apache n'est pas accessible\")\n        print(\"   💡 Démarrez Apache et réessayez\")", "detail": "test_apache_config", "documentation": {}}, {"label": "check_apache_config", "kind": 2, "importPath": "test_apache_config", "description": "test_apache_config", "peekOfCode": "def check_apache_config():\n    \"\"\"Vérifier la configuration Apache\"\"\"\n    print(\"\\n🔧 Vérification de la configuration Apache...\")\n    apache_dir = \"c:\\\\Apache24\"\n    httpd_conf = f\"{apache_dir}\\\\conf\\\\httpd.conf\"\n    if not os.path.exists(httpd_conf):\n        print(f\"❌ Fichier httpd.conf non trouvé: {httpd_conf}\")\n        return False\n    # Vérifier les modules\n    with open(httpd_conf, 'r', encoding='utf-8', errors='ignore') as f:", "detail": "test_apache_config", "documentation": {}}, {"label": "test_boutons_maintenance", "kind": 2, "importPath": "test_boutons_maintenance", "description": "test_boutons_maintenance", "peekOfCode": "def test_boutons_maintenance():\n    \"\"\"Teste les boutons d'action des maintenances\"\"\"\n    print(\"🔧 Test des Boutons d'Action - Maintenances\")\n    print(\"=\" * 60)\n    base_url = \"http://localhost:5001\"\n    try:\n        # 1. Vérifier qu'il y a des maintenances\n        print(\"1. Vérification des maintenances existantes...\")\n        conn = sqlite3.connect('parc_automobile.db')\n        cursor = conn.cursor()", "detail": "test_boutons_maintenance", "documentation": {}}, {"label": "test_formats_courts", "kind": 2, "importPath": "test_formats_courts", "description": "test_formats_courts", "peekOfCode": "def test_formats_courts():\n    \"\"\"Teste les formats courts d'immatriculation\"\"\"\n    print(\"🔧 Test des Formats Courts d'Immatriculation\")\n    print(\"=\" * 60)\n    base_url = \"http://localhost:5001\"\n    # Formats courts à tester\n    formats_tests = [\n        # Formats courts valides\n        ('123 A 1', 'Format court 3 chiffres', True),\n        ('12 B 6', 'Format court 2 chiffres', True),", "detail": "test_formats_courts", "documentation": {}}, {"label": "test_exemples_specifiques", "kind": 2, "importPath": "test_formats_courts", "description": "test_formats_courts", "peekOfCode": "def test_exemples_specifiques():\n    \"\"\"Teste des exemples spécifiques de formats courts\"\"\"\n    print(\"\\n🎯 Test d'Exemples Spécifiques\")\n    print(\"=\" * 40)\n    from immatriculation_maroc import ImmatriculationMaroc\n    exemples = [\n        '1 A 1',      # Minimum absolu\n        '12 B 6',     # 2 chiffres\n        '123 C 16',   # 3 chiffres\n        '1234 D 26',  # 4 chiffres", "detail": "test_formats_courts", "documentation": {}}, {"label": "test_gesparc_context", "kind": 2, "importPath": "test_gesparc_context", "description": "test_gesparc_context", "peekOfCode": "def test_gesparc_context():\n    \"\"\"Teste l'accès à GesParc Auto dans le contexte /gesparc\"\"\"\n    print(\"🔍 Test de la configuration /gesparc\")\n    print(\"=\" * 50)\n    base_urls = [\n        \"http://localhost/gesparc\",\n        \"http://localhost:5001\"\n    ]\n    test_paths = [\n        \"/\",", "detail": "test_gesparc_context", "documentation": {}}, {"label": "test_url_generation", "kind": 2, "importPath": "test_gesparc_context", "description": "test_gesparc_context", "peekOfCode": "def test_url_generation():\n    \"\"\"Teste la génération d'URLs Flask\"\"\"\n    print(\"\\n🔗 Test de génération d'URLs\")\n    print(\"-\" * 30)\n    try:\n        # Importer l'app Flask\n        import os\n        os.chdir(\"c:/Apache24/htdocs/gesparc\")\n        from gesparc_app import gesparc_app\n        with gesparc_app.test_request_context('/gesparc/'):", "detail": "test_gesparc_context", "documentation": {}}, {"label": "test_maintenance_page", "kind": 2, "importPath": "test_maintenance", "description": "test_maintenance", "peekOfCode": "def test_maintenance_page():\n    \"\"\"Teste la page de planification de maintenance\"\"\"\n    print(\"🔍 Test de la page Planifier une Maintenance\")\n    print(\"=\" * 50)\n    base_url = \"http://localhost:5001\"\n    try:\n        # Test 1: Accès à la page\n        print(\"1. Test d'accès à la page...\")\n        response = requests.get(f\"{base_url}/maintenances/ajouter\", timeout=10)\n        print(f\"   Status: {response.status_code}\")", "detail": "test_maintenance", "documentation": {}}, {"label": "test_maintenance_submit", "kind": 2, "importPath": "test_maintenance_submit", "description": "test_maintenance_submit", "peekOfCode": "def test_maintenance_submit():\n    \"\"\"Teste la soumission du formulaire de maintenance\"\"\"\n    print(\"🔧 Test de soumission - Planifier une Maintenance\")\n    print(\"=\" * 60)\n    base_url = \"http://localhost:5001\"\n    try:\n        # P<PERSON>parer les données de test\n        future_date = (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d')\n        test_data = {\n            'vehicule_id': '1',", "detail": "test_maintenance_submit", "documentation": {}}, {"label": "test_matplotlib_module", "kind": 2, "importPath": "test_matplotlib_integration", "description": "test_matplotlib_integration", "peekOfCode": "def test_matplotlib_module():\n    \"\"\"Teste le module matplotlib_analytics directement\"\"\"\n    print(\"🔬 Test du Module Matplotlib Analytics\")\n    print(\"=\" * 50)\n    try:\n        analytics = GesparcAnalytics()\n        print(\"✅ Module GesparcAnalytics importé avec succès\")\n        # Test de récupération des données\n        data = analytics.get_data()\n        print(f\"✅ Données récupérées: {len(data)} tables\")", "detail": "test_matplotlib_integration", "documentation": {}}, {"label": "test_flask_integration", "kind": 2, "importPath": "test_matplotlib_integration", "description": "test_matplotlib_integration", "peekOfCode": "def test_flask_integration():\n    \"\"\"Teste l'intégration Flask\"\"\"\n    print(\"\\n🌐 Test de l'Intégration Flask\")\n    print(\"=\" * 40)\n    try:\n        # Test de la page principale\n        print(\"1. Test de la page Analytics Matplotlib...\")\n        response = requests.get(\"http://localhost:5001/analytics/matplotlib\", timeout=20)\n        if response.status_code == 200:\n            print(\"   ✅ Page accessible (200)\")", "detail": "test_matplotlib_integration", "documentation": {}}, {"label": "test_performance", "kind": 2, "importPath": "test_matplotlib_integration", "description": "test_matplotlib_integration", "peekOfCode": "def test_performance():\n    \"\"\"Teste les performances de génération\"\"\"\n    print(\"\\n⚡ Test de Performance\")\n    print(\"=\" * 30)\n    try:\n        analytics = GesparcAnalytics()\n        # Test de performance pour chaque type de graphique\n        chart_methods = [\n            ('Dashboard', analytics.create_vehicle_analysis_dashboard),\n            ('Évolution', analytics.create_maintenance_evolution_chart),", "detail": "test_matplotlib_integration", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_matplotlib_integration", "description": "test_matplotlib_integration", "peekOfCode": "def main():\n    \"\"\"Fonction principale de test\"\"\"\n    print(\"🧪 Test Complet de l'Intégration Matplotlib\")\n    print(\"=\" * 60)\n    # Attendre que l'application soit prête\n    print(\"⏳ Attente du démarrage de l'application...\")\n    time.sleep(3)\n    success1 = test_matplotlib_module()\n    success2 = test_flask_integration()\n    success3 = test_performance()", "detail": "test_matplotlib_integration", "documentation": {}}, {"label": "test_normal_mode", "kind": 2, "importPath": "test_prefix_configuration", "description": "test_prefix_configuration", "peekOfCode": "def test_normal_mode():\n    \"\"\"Teste Flask en mode normal (sans préfixe)\"\"\"\n    print(\"🔧 Test Mode Normal (sans préfixe)\")\n    print(\"=\" * 45)\n    base_url = \"http://localhost:5001\"\n    test_urls = [\n        \"/\",\n        \"/vehicules\", \n        \"/test-prefix\",\n        \"/api/prefix-info\"", "detail": "test_prefix_configuration", "documentation": {}}, {"label": "test_prefix_mode", "kind": 2, "importPath": "test_prefix_configuration", "description": "test_prefix_configuration", "peekOfCode": "def test_prefix_mode():\n    \"\"\"Teste Flask en mode préfixe\"\"\"\n    print(\"\\n🌐 Test Mode Préfixe (/gesparc)\")\n    print(\"=\" * 40)\n    # Démarrer Flask avec préfixe\n    print(\"🚀 Démarrage de Flask avec préfixe...\")\n    # Définir les variables d'environnement\n    env = os.environ.copy()\n    env['GESPARC_USE_PREFIX'] = 'true'\n    env['SCRIPT_NAME'] = '/gesparc'", "detail": "test_prefix_configuration", "documentation": {}}, {"label": "test_api_prefix_info", "kind": 2, "importPath": "test_prefix_configuration", "description": "test_prefix_configuration", "peekOfCode": "def test_api_prefix_info():\n    \"\"\"Teste l'API d'information du préfixe\"\"\"\n    print(\"\\n📊 Test API Prefix Info\")\n    print(\"=\" * 30)\n    try:\n        response = requests.get(\"http://localhost:5001/api/prefix-info\", timeout=5)\n        if response.ok:\n            data = response.json()\n            print(\"✅ API accessible\")\n            print(f\"   Use Prefix: {data.get('use_prefix')}\")", "detail": "test_prefix_configuration", "documentation": {}}, {"label": "test_wsgi_configuration", "kind": 2, "importPath": "test_prefix_configuration", "description": "test_prefix_configuration", "peekOfCode": "def test_wsgi_configuration():\n    \"\"\"Teste la configuration WSGI\"\"\"\n    print(\"\\n🔧 Test Configuration WSGI\")\n    print(\"=\" * 35)\n    wsgi_file = \"gesparc.wsgi\"\n    if os.path.exists(wsgi_file):\n        print(f\"✅ Fichier WSGI trouvé: {wsgi_file}\")\n        with open(wsgi_file, 'r', encoding='utf-8') as f:\n            content = f.read()\n        # Vérifier les configurations importantes", "detail": "test_prefix_configuration", "documentation": {}}, {"label": "create_apache_test_config", "kind": 2, "importPath": "test_prefix_configuration", "description": "test_prefix_configuration", "peekOfCode": "def create_apache_test_config():\n    \"\"\"Crée une configuration Apache de test\"\"\"\n    print(\"\\n📝 Génération Configuration Apache\")\n    print(\"=\" * 40)\n    config_content = \"\"\"# Configuration Apache pour GesParc Auto avec préfixe /gesparc\n# Option 1: WSGI (Recommandée)\nLoadModule wsgi_module modules/mod_wsgi.so\nWSGIScriptAlias /gesparc \"c:/Apache24/htdocs/gesparc/gesparc.wsgi\"\nWSGIPythonPath \"c:/Apache24/htdocs/gesparc\"\n<Directory \"c:/Apache24/htdocs/gesparc\">", "detail": "test_prefix_configuration", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_prefix_configuration", "description": "test_prefix_configuration", "peekOfCode": "def main():\n    \"\"\"Fonction principale\"\"\"\n    print(\"🧪 Test Complet Configuration Préfixe /gesparc\")\n    print(\"=\" * 60)\n    # Vérifier que Flask est démarré\n    try:\n        response = requests.get(\"http://localhost:5001/\", timeout=3)\n        if not response.ok:\n            print(\"⚠️ Flask ne semble pas démarré en mode normal\")\n            print(\"💡 Démarrez d'abord: python gesparc_app.py\")", "detail": "test_prefix_configuration", "documentation": {}}, {"label": "test_rapports", "kind": 2, "importPath": "test_rapports", "description": "test_rapports", "peekOfCode": "def test_rapports():\n    \"\"\"Teste la page des rapports et statistiques\"\"\"\n    print(\"📊 Test des Rapports et Statistiques\")\n    print(\"=\" * 60)\n    base_url = \"http://localhost:5001\"\n    try:\n        # Test 1: Accès à la page principale\n        print(\"1. Test d'accès à la page rapports...\")\n        response = requests.get(f\"{base_url}/rapports\", timeout=10)\n        if response.status_code == 200:", "detail": "test_rapports", "documentation": {}}, {"label": "test_performance_rapports", "kind": 2, "importPath": "test_rapports", "description": "test_rapports", "peekOfCode": "def test_performance_rapports():\n    \"\"\"Teste la performance de génération des rapports\"\"\"\n    print(\"\\n⚡ Test de Performance des Rapports\")\n    print(\"=\" * 40)\n    import time\n    try:\n        start_time = time.time()\n        response = requests.get(\"http://localhost:5001/rapports\", timeout=30)\n        end_time = time.time()\n        duration = end_time - start_time", "detail": "test_rapports", "documentation": {}}, {"label": "test_rapports_final", "kind": 2, "importPath": "test_rapports_final", "description": "test_rapports_final", "peekOfCode": "def test_rapports_final():\n    \"\"\"Teste la version finale des rapports\"\"\"\n    print(\"🎯 Test Final des Rapports Corrigés\")\n    print(\"=\" * 50)\n    try:\n        # Test 1: Accès à la page\n        print(\"1. Test d'accès à la page...\")\n        response = requests.get(\"http://localhost:5001/rapports\", timeout=10)\n        if response.status_code == 200:\n            print(\"   ✅ Page accessible (200)\")", "detail": "test_rapports_final", "documentation": {}}, {"label": "test_performance", "kind": 2, "importPath": "test_rapports_final", "description": "test_rapports_final", "peekOfCode": "def test_performance():\n    \"\"\"Teste la performance de la page\"\"\"\n    print(\"\\n⚡ Test de Performance\")\n    print(\"=\" * 30)\n    import time\n    try:\n        start_time = time.time()\n        response = requests.get(\"http://localhost:5001/rapports\", timeout=30)\n        end_time = time.time()\n        duration = end_time - start_time", "detail": "test_rapports_final", "documentation": {}}, {"label": "test_route_rapports", "kind": 2, "importPath": "test_route_rapports", "description": "test_route_rapports", "peekOfCode": "def test_route_rapports():\n    \"\"\"Simule exactement ce que fait la route rapports\"\"\"\n    print(\"🔍 Test de la Route Rapports\")\n    print(\"=\" * 40)\n    try:\n        conn = sqlite3.connect('parc_automobile.db')\n        # Statistiques générales\n        stats = {}\n        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]\n        stats['total_conducteurs'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]", "detail": "test_route_rapports", "documentation": {}}, {"label": "update_maintenance_table", "kind": 2, "importPath": "update_maintenance_db", "description": "update_maintenance_db", "peekOfCode": "def update_maintenance_table():\n    \"\"\"Met à jour la table maintenances avec les nouveaux champs\"\"\"\n    conn = sqlite3.connect('parc_automobile.db')\n    # Ajouter la colonne priorite\n    try:\n        conn.execute('ALTER TABLE maintenances ADD COLUMN priorite TEXT DEFAULT \"normale\"')\n        print('✅ Colonne priorite ajoutée')\n    except Exception as e:\n        print('ℹ️ Colonne priorite existe déjà ou erreur:', str(e))\n    # Ajouter la colonne date_realisation", "detail": "update_maintenance_db", "documentation": {}}]