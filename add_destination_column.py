#!/usr/bin/env python3
"""
Ajouter la colonne 'destination' à la table affectations
"""

import sqlite3

def add_destination_column():
    """Ajouter la colonne destination à la table affectations"""
    try:
        conn = sqlite3.connect('parc_automobile.db')
        cursor = conn.cursor()
        
        print("🔧 Ajout de la colonne 'destination' à la table affectations...")
        
        # Ajouter la colonne destination
        cursor.execute('''
            ALTER TABLE affectations 
            ADD COLUMN destination TEXT
        ''')
        
        conn.commit()
        print("✅ Colonne 'destination' ajoutée avec succès")
        
        # Vérifier que la colonne a été ajoutée
        cursor.execute("PRAGMA table_info(affectations)")
        columns = cursor.fetchall()
        
        destination_found = False
        for col in columns:
            if col[1] == 'destination':
                destination_found = True
                print(f"✅ Vérification: Colonne 'destination' trouvée - Type: {col[2]}")
                break
        
        if not destination_found:
            print("❌ Erreur: La colonne 'destination' n'a pas été trouvée après ajout")
        
        # Afficher la nouvelle structure
        print(f"\n📋 Structure mise à jour de la table 'affectations':")
        print("-" * 60)
        print(f"{'#':<3} {'Nom':<20} {'Type':<15} {'Null':<6}")
        print("-" * 60)
        
        for col in columns:
            cid, name, type_name, notnull, default_value, pk = col
            null_str = "NO" if notnull else "YES"
            print(f"{cid:<3} {name:<20} {type_name:<15} {null_str:<6}")
        
        conn.close()
        return True
        
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e).lower():
            print("⚠️  La colonne 'destination' existe déjà")
            return True
        else:
            print(f"❌ Erreur SQL: {e}")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Ajout de la colonne 'destination' à la table affectations...")
    success = add_destination_column()
    
    if success:
        print("\n🎉 SUCCÈS!")
        print("✅ La colonne 'destination' est maintenant disponible")
        print("📝 Vous pouvez maintenant modifier le formulaire et le backend")
    else:
        print("\n❌ ÉCHEC!")
        print("🔧 Vérifiez les erreurs ci-dessus")
