#!/usr/bin/env python3
"""
Test des fonctionnalités de rapports et statistiques
"""

import requests
import sys

def test_rapports():
    """Teste la page des rapports et statistiques"""
    print("📊 Test des Rapports et Statistiques")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    
    try:
        # Test 1: Accès à la page principale
        print("1. Test d'accès à la page rapports...")
        response = requests.get(f"{base_url}/rapports", timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Page accessible")
            content = response.text
            
            # Vérifier les éléments essentiels
            if "Rapports et Statistiques" in content:
                print("   ✅ Titre correct")
            else:
                print("   ❌ Titre manquant")
                
            if "chart.js" in content:
                print("   ✅ Bibliothèque Chart.js chargée")
            else:
                print("   ❌ Chart.js manquant")
                
            if "statutChart" in content:
                print("   ✅ Graphique statuts présent")
            else:
                print("   ❌ Graphique statuts manquant")
                
            if "Export CSV" in content:
                print("   ✅ Boutons d'export présents")
            else:
                print("   ❌ Boutons d'export manquants")
                
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
            return False
        
        # Test 2: Test des exports
        print("\n2. Test des fonctionnalités d'export...")
        
        # Test export CSV
        response = requests.get(f"{base_url}/rapports/export/csv", timeout=10)
        if response.status_code == 200:
            print("   ✅ Export CSV fonctionnel")
            if 'text/csv' in response.headers.get('Content-Type', ''):
                print("   ✅ Type MIME CSV correct")
            else:
                print("   ⚠️ Type MIME CSV incorrect")
        else:
            print(f"   ❌ Export CSV échoué: {response.status_code}")
        
        # Test export Excel
        response = requests.get(f"{base_url}/rapports/export/excel", timeout=10)
        if response.status_code == 200:
            print("   ✅ Export Excel fonctionnel")
            if 'spreadsheet' in response.headers.get('Content-Type', ''):
                print("   ✅ Type MIME Excel correct")
            else:
                print("   ⚠️ Type MIME Excel incorrect")
        else:
            print(f"   ❌ Export Excel échoué: {response.status_code}")
        
        # Test 3: Vérification des données
        print("\n3. Test de la présence de données...")
        
        # Vérifier s'il y a des données dans la base
        import sqlite3
        conn = sqlite3.connect('parc_automobile.db')
        cursor = conn.cursor()
        
        # Compter les véhicules
        cursor.execute('SELECT COUNT(*) FROM vehicules')
        nb_vehicules = cursor.fetchone()[0]
        print(f"   📊 Véhicules en base: {nb_vehicules}")
        
        # Compter les maintenances
        cursor.execute('SELECT COUNT(*) FROM maintenances')
        nb_maintenances = cursor.fetchone()[0]
        print(f"   🔧 Maintenances en base: {nb_maintenances}")
        
        # Compter les conducteurs
        cursor.execute('SELECT COUNT(*) FROM conducteurs')
        nb_conducteurs = cursor.fetchone()[0]
        print(f"   👥 Conducteurs en base: {nb_conducteurs}")
        
        conn.close()
        
        if nb_vehicules > 0:
            print("   ✅ Données véhicules disponibles pour les rapports")
        else:
            print("   ⚠️ Aucun véhicule - rapports limités")
            
        if nb_maintenances > 0:
            print("   ✅ Données maintenances disponibles pour les graphiques")
        else:
            print("   ⚠️ Aucune maintenance - graphiques limités")
        
        # Test 4: Vérification des graphiques
        print("\n4. Test des éléments graphiques...")
        
        response = requests.get(f"{base_url}/rapports", timeout=10)
        content = response.text
        
        graphiques = [
            ('statutChart', 'Graphique statuts'),
            ('carburantChart', 'Graphique carburants'),
            ('marqueChart', 'Graphique marques'),
            ('maintenancesChart', 'Graphique évolution maintenances')
        ]
        
        for chart_id, description in graphiques:
            if chart_id in content:
                print(f"   ✅ {description} configuré")
            else:
                print(f"   ❌ {description} manquant")
        
        # Test 5: Vérification des statistiques
        print("\n5. Test des statistiques affichées...")
        
        stats_elements = [
            ('total_vehicules', 'Total véhicules'),
            ('total_conducteurs', 'Total conducteurs'),
            ('total_maintenances', 'Total maintenances'),
            ('cout_total_maintenances', 'Coût total maintenances')
        ]
        
        for stat_var, description in stats_elements:
            if stat_var in content:
                print(f"   ✅ {description} affiché")
            else:
                print(f"   ❌ {description} manquant")
        
        print("\n" + "=" * 60)
        print("🏁 Tests des rapports terminés")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
        print("💡 Vérifiez que l'application Flask est démarrée")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def test_performance_rapports():
    """Teste la performance de génération des rapports"""
    print("\n⚡ Test de Performance des Rapports")
    print("=" * 40)
    
    import time
    
    try:
        start_time = time.time()
        response = requests.get("http://localhost:5001/rapports", timeout=30)
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"Temps de génération: {duration:.2f} secondes")
        
        if duration < 2:
            print("✅ Performance excellente (< 2s)")
        elif duration < 5:
            print("✅ Performance acceptable (< 5s)")
        elif duration < 10:
            print("⚠️ Performance lente (< 10s)")
        else:
            print("❌ Performance très lente (> 10s)")
            
        return duration < 10
        
    except Exception as e:
        print(f"❌ Erreur de test de performance: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Tests de la Section Rapports et Statistiques")
    print("=" * 70)
    
    success1 = test_rapports()
    success2 = test_performance_rapports()
    
    if success1 and success2:
        print("\n🎉 Tous les tests sont passés avec succès !")
        sys.exit(0)
    else:
        print("\n❌ Certains tests ont échoué")
        sys.exit(1)
