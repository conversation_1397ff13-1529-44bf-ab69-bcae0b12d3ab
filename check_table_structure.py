#!/usr/bin/env python3
"""
Vérifier la structure de la table affectations
"""

import sqlite3

def check_affectations_table():
    """Vérifier la structure de la table affectations"""
    try:
        conn = sqlite3.connect('parc_automobile.db')
        cursor = conn.cursor()
        
        # Obtenir la structure de la table
        cursor.execute("PRAGMA table_info(affectations)")
        columns = cursor.fetchall()
        
        print("📋 Structure de la table 'affectations':")
        print("-" * 60)
        print(f"{'#':<3} {'Nom':<20} {'Type':<15} {'Null':<6} {'Défaut':<10} {'PK':<3}")
        print("-" * 60)
        
        for col in columns:
            cid, name, type_name, notnull, default_value, pk = col
            null_str = "NO" if notnull else "YES"
            default_str = str(default_value) if default_value else ""
            pk_str = "YES" if pk else ""
            print(f"{cid:<3} {name:<20} {type_name:<15} {null_str:<6} {default_str:<10} {pk_str:<3}")
        
        # Vérifier si la colonne mission existe
        column_names = [col[1] for col in columns]
        
        print(f"\n🔍 Vérification de la colonne 'mission':")
        if 'mission' in column_names:
            print("  ✅ La colonne 'mission' existe déjà")
        else:
            print("  ❌ La colonne 'mission' n'existe pas")
            print("  💡 Il faut l'ajouter à la table")
        
        conn.close()
        return 'mission' in column_names
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == '__main__':
    check_affectations_table()
