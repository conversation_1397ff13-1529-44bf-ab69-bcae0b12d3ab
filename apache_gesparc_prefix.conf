# Configuration Apache pour GesParc Auto avec préfixe /gesparc

# Option 1: WSGI (Recommandée)
LoadModule wsgi_module modules/mod_wsgi.so

WSGIScriptAlias /gesparc "c:/Apache24/htdocs/gesparc/gesparc.wsgi"
WSGIPythonPath "c:/Apache24/htdocs/gesparc"

<Directory "c:/Apache24/htdocs/gesparc">
    WSGIApplicationGroup %{GLOBAL}
    WSGIScriptReloading On
    Require all granted
    
    # Variables d'environnement
    SetEnv SCRIPT_NAME /gesparc
    SetEnv GESPARC_USE_PREFIX true
</Directory>

# Option 2: Proxy (Alternative)
# LoadModule proxy_module modules/mod_proxy.so
# LoadModule proxy_http_module modules/mod_proxy_http.so
# 
# ProxyPass /gesparc/ http://127.0.0.1:5001/gesparc/
# ProxyPassReverse /gesparc/ http://127.0.0.1:5001/gesparc/
# ProxyPreserveHost On

# Fichiers statiques
Alias /gesparc/static "c:/Apache24/htdocs/gesparc/static"
<Directory "c:/Apache24/htdocs/gesparc/static">
    Require all granted
    Header set Cache-Control "max-age=86400"
</Directory>

# Sécurité
<FilesMatch "\.(py|pyc|pyo|db|wsgi|log)$">
    Require all denied
</FilesMatch>

# Logs
LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\"" gesparc_combined
CustomLog "logs/gesparc_access.log" gesparc_combined
ErrorLog "logs/gesparc_error.log"
