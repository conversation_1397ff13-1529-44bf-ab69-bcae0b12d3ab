#!/usr/bin/env python3
"""
Test de soumission du formulaire de planification de maintenance
"""

import requests
import sys
from datetime import datetime, timedelta

def test_maintenance_submit():
    """Teste la soumission du formulaire de maintenance"""
    print("🔧 Test de soumission - Planifier une Maintenance")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    
    try:
        # Préparer les données de test
        future_date = (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d')
        
        test_data = {
            'vehicule_id': '1',
            'type_maintenance': 'Vidange',
            'date_maintenance': future_date,
            'priorite': 'normale',
            'cout_estime': '300.50',
            'garage': 'Garage Test Auto',
            'description': 'Vidange moteur + changement filtre à huile - Test automatique',
            'statut': 'planifiee'
        }
        
        print("1. Données de test préparées:")
        for key, value in test_data.items():
            print(f"   {key}: {value}")
        
        # Test de soumission
        print("\n2. Soumission du formulaire...")
        response = requests.post(
            f"{base_url}/maintenances/ajouter",
            data=test_data,
            allow_redirects=False,
            timeout=10
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 302:  # Redirection après succès
            print("   ✅ Soumission réussie (redirection)")
            redirect_url = response.headers.get('Location', '')
            print(f"   Redirection vers: {redirect_url}")
            
            if '/maintenances' in redirect_url:
                print("   ✅ Redirection correcte vers la liste")
            else:
                print("   ⚠️ Redirection inattendue")
                
        elif response.status_code == 200:
            print("   ⚠️ Pas de redirection (possible erreur)")
            if "erreur" in response.text.lower():
                print("   ❌ Erreur détectée dans la réponse")
            else:
                print("   ✅ Page rechargée sans erreur")
        else:
            print(f"   ❌ Erreur HTTP: {response.status_code}")
            
        # Vérifier que la maintenance a été créée
        print("\n3. Vérification de la création...")
        list_response = requests.get(f"{base_url}/maintenances", timeout=10)
        
        if list_response.status_code == 200:
            content = list_response.text
            if "Garage Test Auto" in content:
                print("   ✅ Maintenance créée et visible dans la liste")
            elif "Vidange" in content:
                print("   ✅ Maintenance de type Vidange trouvée")
            else:
                print("   ⚠️ Maintenance non trouvée dans la liste")
        else:
            print(f"   ❌ Impossible de vérifier: {list_response.status_code}")
            
        # Test avec données manquantes
        print("\n4. Test de validation (données manquantes)...")
        invalid_data = {
            'vehicule_id': '',  # Manquant
            'type_maintenance': 'Révision',
            'date_maintenance': future_date
        }
        
        response = requests.post(
            f"{base_url}/maintenances/ajouter",
            data=invalid_data,
            allow_redirects=False,
            timeout=10
        )
        
        if response.status_code == 302:
            print("   ✅ Validation fonctionne (redirection sur erreur)")
        else:
            print(f"   ⚠️ Validation inattendue: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
        print("💡 Vérifiez que l'application Flask est démarrée")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🏁 Tests de soumission terminés")
    return True

if __name__ == "__main__":
    success = test_maintenance_submit()
    sys.exit(0 if success else 1)
