#!/usr/bin/env python3
"""
Test du nouveau champ Mission dans les affectations
"""

import sqlite3
from datetime import date

def test_champ_mission():
    """Test du champ Mission"""
    print("🧪 Test du Champ Mission dans les Affectations")
    print("=" * 50)
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect('parc_automobile.db')
        conn.row_factory = sqlite3.Row
        
        # Vérifier que la colonne mission existe
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(affectations)")
        columns = cursor.fetchall()
        
        mission_column = None
        for col in columns:
            if col[1] == 'mission':
                mission_column = col
                break
        
        if mission_column:
            print("✅ Colonne 'mission' trouvée dans la table affectations")
            print(f"   Type: {mission_column[2]}, Null: {'YES' if not mission_column[3] else 'NO'}")
        else:
            print("❌ Colonne 'mission' non trouvée")
            conn.close()
            return False
        
        # Tester l'insertion d'une affectation avec mission
        print(f"\n🔧 Test d'insertion d'affectation avec mission...")
        
        # Récupérer un véhicule disponible
        vehicule = conn.execute('''
            SELECT * FROM vehicules 
            WHERE statut = 'disponible' 
            LIMIT 1
        ''').fetchone()
        
        # Récupérer un conducteur actif
        conducteur = conn.execute('''
            SELECT * FROM conducteurs 
            WHERE statut = 'actif' 
            LIMIT 1
        ''').fetchone()
        
        if not vehicule:
            print("⚠️  Aucun véhicule disponible pour le test")
            # Créer un véhicule de test
            conn.execute('''
                INSERT INTO vehicules (immatriculation, marque, modele, annee, statut)
                VALUES ('TEST-001', 'Test', 'Mission', 2024, 'disponible')
            ''')
            vehicule = conn.execute('''
                SELECT * FROM vehicules WHERE immatriculation = 'TEST-001'
            ''').fetchone()
            print("✅ Véhicule de test créé")
        
        if not conducteur:
            print("⚠️  Aucun conducteur actif pour le test")
            # Créer un conducteur de test
            conn.execute('''
                INSERT INTO conducteurs (nom, prenom, statut)
                VALUES ('Test', 'Mission', 'actif')
            ''')
            conducteur = conn.execute('''
                SELECT * FROM conducteurs WHERE nom = 'Test' AND prenom = 'Mission'
            ''').fetchone()
            print("✅ Conducteur de test créé")
        
        # Données de test
        mission_test = "Livraison urgente - Transport de matériel médical"
        date_debut = date.today().isoformat()
        
        print(f"   Véhicule: {vehicule['immatriculation']}")
        print(f"   Conducteur: {conducteur['prenom']} {conducteur['nom']}")
        print(f"   Mission: {mission_test}")
        print(f"   Date début: {date_debut}")
        
        # Insérer l'affectation avec mission
        cursor.execute('''
            INSERT INTO affectations (vehicule_id, conducteur_id, date_debut, statut, mission)
            VALUES (?, ?, ?, 'active', ?)
        ''', (vehicule['id'], conducteur['id'], date_debut, mission_test))
        
        affectation_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ Affectation créée avec ID: {affectation_id}")
        
        # Vérifier que l'affectation a été créée avec la mission
        affectation = conn.execute('''
            SELECT a.*, v.immatriculation, c.prenom, c.nom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.id = ?
        ''', (affectation_id,)).fetchone()
        
        if affectation and affectation['mission'] == mission_test:
            print("✅ Mission correctement enregistrée")
            print(f"   Mission récupérée: {affectation['mission']}")
        else:
            print("❌ Erreur: Mission non enregistrée correctement")
            conn.close()
            return False
        
        # Test de récupération pour la liste des affectations
        print(f"\n📋 Test de récupération pour la liste...")
        
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele,
                   c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC, a.id DESC
            LIMIT 5
        ''').fetchall()
        
        print(f"✅ {len(affectations)} affectation(s) récupérée(s)")
        
        for aff in affectations:
            mission_display = aff['mission'] if aff['mission'] else 'Non définie'
            print(f"   ID {aff['id']}: {aff['immatriculation']} - {mission_display}")
        
        # Test de mise à jour d'une mission
        print(f"\n🔄 Test de mise à jour de mission...")
        
        nouvelle_mission = "Mission modifiée - Transport de personnel"
        conn.execute('''
            UPDATE affectations SET mission = ? WHERE id = ?
        ''', (nouvelle_mission, affectation_id))
        conn.commit()
        
        # Vérifier la mise à jour
        affectation_maj = conn.execute('''
            SELECT mission FROM affectations WHERE id = ?
        ''', (affectation_id,)).fetchone()
        
        if affectation_maj['mission'] == nouvelle_mission:
            print("✅ Mission mise à jour avec succès")
            print(f"   Nouvelle mission: {affectation_maj['mission']}")
        else:
            print("❌ Erreur lors de la mise à jour")
        
        # Test avec mission vide/null
        print(f"\n🔍 Test avec mission vide...")
        
        conn.execute('''
            INSERT INTO affectations (vehicule_id, conducteur_id, date_debut, statut, mission)
            VALUES (?, ?, ?, 'active', ?)
        ''', (vehicule['id'], conducteur['id'], date_debut, None))
        
        affectation_vide_id = cursor.lastrowid
        conn.commit()
        
        affectation_vide = conn.execute('''
            SELECT mission FROM affectations WHERE id = ?
        ''', (affectation_vide_id,)).fetchone()
        
        if affectation_vide['mission'] is None:
            print("✅ Mission NULL gérée correctement")
        else:
            print("❌ Problème avec mission NULL")
        
        # Nettoyage des données de test
        print(f"\n🧹 Nettoyage des données de test...")
        
        conn.execute('DELETE FROM affectations WHERE id IN (?, ?)', (affectation_id, affectation_vide_id))
        
        # Supprimer les données de test si créées
        if vehicule['immatriculation'] == 'TEST-001':
            conn.execute('DELETE FROM vehicules WHERE id = ?', (vehicule['id'],))
        
        if conducteur['nom'] == 'Test' and conducteur['prenom'] == 'Mission':
            conn.execute('DELETE FROM conducteurs WHERE id = ?', (conducteur['id'],))
        
        conn.commit()
        print("✅ Données de test nettoyées")
        
        conn.close()
        
        # Résumé des tests
        print(f"\n📊 Résumé des Tests:")
        print(f"  ✅ Colonne 'mission' présente dans la base")
        print(f"  ✅ Insertion avec mission fonctionnelle")
        print(f"  ✅ Récupération de mission correcte")
        print(f"  ✅ Mise à jour de mission opérationnelle")
        print(f"  ✅ Gestion des missions NULL/vides")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_interface_formulaire():
    """Test de l'interface du formulaire"""
    print(f"\n🎨 Test de l'Interface du Formulaire:")
    print("-" * 40)
    
    try:
        # Vérifier que le champ mission est ajouté au formulaire
        with open('templates/ajouter_affectation.html', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Vérifications
        checks = [
            ('name="mission"', 'Champ mission présent'),
            ('required', 'Champ mission obligatoire'),
            ('fas fa-bullseye', 'Icône mission présente'),
            ('Mission *', 'Label mission avec astérisque'),
            ('maxlength="255"', 'Limitation de longueur'),
            ('placeholder=', 'Placeholder informatif')
        ]
        
        for check, description in checks:
            if check in contenu:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description} - manquant")
        
        # Vérifier la page de liste
        with open('templates/affectations.html', 'r', encoding='utf-8') as f:
            contenu_liste = f.read()
        
        if '<th>Mission</th>' in contenu_liste:
            print(f"  ✅ Colonne Mission ajoutée à la liste")
        else:
            print(f"  ❌ Colonne Mission manquante dans la liste")
        
        if 'affectation.mission' in contenu_liste:
            print(f"  ✅ Affichage de la mission dans la liste")
        else:
            print(f"  ❌ Affichage de la mission manquant")
        
        # Vérifier la page de détail
        with open('templates/detail_affectation.html', 'r', encoding='utf-8') as f:
            contenu_detail = f.read()
        
        if 'affectation.mission' in contenu_detail:
            print(f"  ✅ Mission affichée dans la page de détail")
        else:
            print(f"  ❌ Mission manquante dans la page de détail")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur test interface: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Test du champ Mission dans les affectations...")
    
    # Tests
    db_success = test_champ_mission()
    interface_success = test_interface_formulaire()
    
    # Résultat final
    print(f"\n" + "="*50)
    if db_success and interface_success:
        print("🎉 CHAMP MISSION IMPLÉMENTÉ AVEC SUCCÈS!")
        print("✅ Base de données mise à jour")
        print("✅ Formulaire d'ajout modifié")
        print("✅ Liste des affectations mise à jour")
        print("✅ Page de détail enrichie")
        print("📋 Le champ Mission est maintenant opérationnel")
    else:
        print("⚠️  PROBLÈME DÉTECTÉ")
        print("🔧 Vérifiez les erreurs ci-dessus")
