{% extends "base.html" %}

{% block title %}Gestion du Budget - GesParc Auto{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-wallet text-success"></i> Gestion du Budget
                </h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('ajouter_operation_budget') }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Nouvelle Opération
                    </a>
                    <a href="{{ url_for('reapprovisionner_budget') }}" class="btn btn-primary">
                        <i class="fas fa-money-bill-wave"></i> Réapprovisionner
                    </a>
                    <a href="{{ url_for('config_budget') }}" class="btn btn-secondary">
                        <i class="fas fa-cog"></i> Configuration
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau de bord financier -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Recettes Totales</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(recettes).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-up fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Dépenses Totales</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(depenses).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card {% if solde_actuel >= config.seuil_alerte %}bg-info{% else %}bg-warning{% endif %} text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Solde Actuel</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(solde_actuel).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-balance-scale fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Seuil d'Alerte</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(config.seuil_alerte).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerte si solde faible -->
    {% if solde_actuel < config.seuil_alerte %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Attention !</strong> Le solde actuel ({{ '{:,.2f}'.format(solde_actuel).replace(',', ' ') }} MAD) 
                est inférieur au seuil d'alerte ({{ '{:,.2f}'.format(config.seuil_alerte).replace(',', ' ') }} MAD).
                <a href="{{ url_for('reapprovisionner_budget') }}" class="btn btn-sm btn-primary ms-2">
                    <i class="fas fa-money-bill-wave"></i> Réapprovisionner
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <!-- Liste des opérations -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i> Dernières Opérations
                    </h5>
                </div>
                <div class="card-body">
                    {% if operations %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Montant</th>
                                    <th>Catégorie</th>
                                    <th>Commentaire</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in operations %}
                                <tr>
                                    <td>
                                        <small>{{ operation.date_operation[:16].replace('T', ' ') }}</small>
                                    </td>
                                    <td>
                                        {% if operation.type == 'recette' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-arrow-up"></i> Recette
                                            </span>
                                        {% elif operation.type == 'depense' %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-arrow-down"></i> Dépense
                                            </span>
                                        {% else %}
                                            <span class="badge bg-primary">
                                                <i class="fas fa-money-bill-wave"></i> Réappro.
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ '{:,.2f}'.format(operation.montant).replace(',', ' ') }} MAD</strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ operation.categorie or '-' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ operation.commentaire[:50] }}{% if operation.commentaire|length > 50 %}...{% endif %}</small>
                                    </td>
                                    <td>
                                        {% if operation.statut == 'valide' %}
                                            <span class="badge bg-success">Valide</span>
                                        {% elif operation.statut == 'annule' %}
                                            <span class="badge bg-danger">Annulé</span>
                                        {% else %}
                                            <span class="badge bg-warning">En attente</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('modifier_operation_budget', id=operation.id) }}" 
                                               class="btn btn-outline-primary" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="{{ url_for('supprimer_operation_budget', id=operation.id) }}" 
                                                  class="d-inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette opération ?')">
                                                <button type="submit" class="btn btn-outline-danger" title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Aucune opération budgétaire enregistrée</p>
                        <a href="{{ url_for('ajouter_operation_budget') }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Ajouter une opération
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Statistiques par catégorie -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Répartition par Catégorie
                    </h5>
                </div>
                <div class="card-body">
                    {% if stats_categories %}
                    {% for stat in stats_categories %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <small class="text-muted">{{ stat.categorie or 'Non catégorisé' }}</small>
                            <br>
                            {% if stat.type == 'recette' %}
                                <span class="badge bg-success">Recette</span>
                            {% elif stat.type == 'depense' %}
                                <span class="badge bg-danger">Dépense</span>
                            {% else %}
                                <span class="badge bg-primary">Réappro.</span>
                            {% endif %}
                        </div>
                        <div class="text-end">
                            <strong>{{ '{:,.2f}'.format(stat.total).replace(',', ' ') }} MAD</strong>
                        </div>
                    </div>
                    <hr>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-chart-pie fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Aucune donnée disponible</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Configuration actuelle -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog"></i> Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">Budget Initial</small>
                        <br>
                        <strong>{{ '{:,.2f}'.format(config.budget_initial).replace(',', ' ') }} MAD</strong>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">Seuil d'Alerte</small>
                        <br>
                        <strong>{{ '{:,.2f}'.format(config.seuil_alerte).replace(',', ' ') }} MAD</strong>
                    </div>
                    {% if config.periode_debut and config.periode_fin %}
                    <div class="mb-2">
                        <small class="text-muted">Période</small>
                        <br>
                        <strong>{{ config.periode_debut }} - {{ config.periode_fin }}</strong>
                    </div>
                    {% endif %}
                    <div class="mt-3">
                        <a href="{{ url_for('config_budget') }}" class="btn btn-sm btn-secondary w-100">
                            <i class="fas fa-cog"></i> Modifier Configuration
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}