# Script PowerShell pour démarrer GesParc Auto
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "   🚗 GesParc Auto - Démarrage de l'application" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# Changer vers le répertoire de l'application
Set-Location "c:\Apache24\htdocs\gesparc"

# Vérifier si Python est disponible
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python détecté: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ ERREUR: Python n'est pas installé ou pas dans le PATH" -ForegroundColor Red
    Write-Host "Veuillez installer Python et réessayer" -ForegroundColor Yellow
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

# Vérifier si Flask est installé
try {
    python -c "import flask" 2>$null
    Write-Host "✅ Flask est installé" -ForegroundColor Green
} catch {
    Write-Host "📦 Installation de Flask..." -ForegroundColor Yellow
    pip install Flask
}

# Vérifier si la base de données existe
if (-not (Test-Path "parc_automobile.db")) {
    Write-Host "🗃️ Initialisation de la base de données..." -ForegroundColor Yellow
    python init_db.py
    Write-Host "✅ Base de données créée avec succès" -ForegroundColor Green
} else {
    Write-Host "✅ Base de données trouvée" -ForegroundColor Green
}

Write-Host ""
Write-Host "🚀 Démarrage de l'application Flask..." -ForegroundColor Green
Write-Host ""
Write-Host "📍 Application disponible sur:" -ForegroundColor Cyan
Write-Host "   - Direct: http://localhost:5001" -ForegroundColor White
Write-Host "   - Via Apache: http://localhost/gesparc" -ForegroundColor White
Write-Host ""
Write-Host "⚠️ Appuyez sur Ctrl+C pour arrêter l'application" -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# Lancer l'application
try {
    python gesparc_app.py
} catch {
    Write-Host "❌ Erreur lors du démarrage de l'application" -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour continuer"
}
