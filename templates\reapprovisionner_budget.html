{% extends "base.html" %}

{% block title %}Réapprovisionner Budget - GesParc Auto{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-money-bill-wave text-primary"></i> Réapprovisionner le Budget
                </h1>
                <a href="{{ url_for('budget') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour au Budget
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle"></i> Ajou<PERSON> des Fonds
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Information :</strong> Cette opération ajoutera des fonds au budget disponible.
                        Le montant sera comptabilisé comme un réapprovisionnement.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <!-- Montant -->
                        <div class="mb-4">
                            <label for="montant" class="form-label">
                                <i class="fas fa-coins text-primary"></i> Montant à ajouter (MAD) *
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control form-control-lg" id="montant" 
                                       name="montant" min="0.01" step="0.01" required
                                       placeholder="0.00" style="font-size: 1.2rem;">
                                <span class="input-group-text">MAD</span>
                            </div>
                            <div class="invalid-feedback">
                                Veuillez saisir un montant valide supérieur à 0
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-lightbulb"></i> Montants suggérés : 
                                <button type="button" class="btn btn-sm btn-outline-primary ms-1" onclick="setAmount(5000)">5 000</button>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-1" onclick="setAmount(10000)">10 000</button>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-1" onclick="setAmount(25000)">25 000</button>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-1" onclick="setAmount(50000)">50 000</button>
                            </small>
                        </div>

                        <!-- Date et heure -->
                        <div class="mb-3">
                            <label for="date_operation" class="form-label">
                                <i class="fas fa-calendar"></i> Date et heure *
                            </label>
                            <input type="datetime-local" class="form-control" id="date_operation"
                                   name="date_operation" required
                                   value="{{ datetime_default }}">
                            <div class="invalid-feedback">
                                Veuillez saisir la date et heure
                            </div>
                        </div>

                        <!-- Référence -->
                        <div class="mb-3">
                            <label for="reference" class="form-label">
                                <i class="fas fa-hashtag"></i> Référence
                            </label>
                            <input type="text" class="form-control" id="reference" 
                                   name="reference" maxlength="50"
                                   placeholder="REAPP-2025-001">
                            <small class="form-text text-muted">
                                Numéro de référence pour le suivi
                            </small>
                        </div>

                        <!-- Commentaire -->
                        <div class="mb-4">
                            <label for="commentaire" class="form-label">
                                <i class="fas fa-comment"></i> Commentaire
                            </label>
                            <textarea class="form-control" id="commentaire" name="commentaire" 
                                      rows="3" maxlength="500"
                                      placeholder="Réapprovisionnement mensuel du budget...">Réapprovisionnement budget</textarea>
                            <small class="form-text text-muted">
                                Motif ou description du réapprovisionnement
                            </small>
                        </div>

                        <!-- Résumé -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-clipboard-check"></i> Résumé de l'opération
                                </h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Type :</small><br>
                                        <span class="badge bg-primary">
                                            <i class="fas fa-money-bill-wave"></i> Réapprovisionnement
                                        </span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Catégorie :</small><br>
                                        <span class="badge bg-secondary">Réapprovisionnement</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('budget') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-money-bill-wave"></i> Réapprovisionner le Budget
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function setAmount(amount) {
    document.getElementById('montant').value = amount;
    // Ajouter un effet visuel
    const input = document.getElementById('montant');
    input.style.backgroundColor = '#e3f2fd';
    setTimeout(() => {
        input.style.backgroundColor = '';
    }, 500);
}

document.addEventListener('DOMContentLoaded', function() {
    // Formatage du montant en temps réel
    const montantInput = document.getElementById('montant');
    
    montantInput.addEventListener('input', function() {
        const value = parseFloat(this.value);
        if (!isNaN(value) && value > 0) {
            // Mise à jour visuelle du résumé si nécessaire
            console.log('Montant saisi:', value);
        }
    });
});
</script>
{% endblock %}