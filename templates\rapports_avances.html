{% extends "base.html" %}

{% block title %}Tableau de Bord - Rapports et Statistiques - GesParc Auto{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border: none;
        border-radius: 15px;
        overflow: hidden;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .stat-card {
        background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark, #0056b3));
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }
    
    .stat-icon {
        font-size: 3rem;
        opacity: 0.8;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }
    
    .chart-container-large {
        height: 400px;
    }
    
    .chart-container-small {
        height: 250px;
    }
    
    .kpi-badge {
        font-size: 0.9rem;
        padding: 8px 12px;
        border-radius: 20px;
        font-weight: 600;
    }
    
    .trend-up {
        color: #28a745;
    }
    
    .trend-down {
        color: #dc3545;
    }
    
    .trend-stable {
        color: #6c757d;
    }
    
    .filter-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .metric-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        border-left: 4px solid;
    }
    
    .metric-primary { border-left-color: #007bff; }
    .metric-success { border-left-color: #28a745; }
    .metric-warning { border-left-color: #ffc107; }
    .metric-danger { border-left-color: #dc3545; }
    .metric-info { border-left-color: #17a2b8; }
    
    .progress-ring {
        transform: rotate(-90deg);
    }
    
    .progress-ring-circle {
        transition: stroke-dasharray 0.35s;
        transform: rotate(-90deg);
        transform-origin: 50% 50%;
    }
    
    @media (max-width: 768px) {
        .chart-container {
            height: 250px;
        }
        
        .stat-icon {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- En-tête avec filtres -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <h1 class="mb-1">
                    <i class="fas fa-chart-line text-primary"></i> 
                    Tableau de Bord Analytics
                </h1>
                <p class="text-muted mb-0">Vue d'ensemble et analyses avancées du parc automobile</p>
            </div>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i> Actualiser
                </button>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download"></i> Exporter
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('export_rapports', format='csv') }}">
                            <i class="fas fa-file-csv"></i> Rapport CSV
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('export_rapports', format='excel') }}">
                            <i class="fas fa-file-excel"></i> Rapport Excel
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf"></i> Rapport PDF
                        </a></li>
                    </ul>
                </div>
                <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                    <i class="fas fa-print"></i> Imprimer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filtres avancés -->
<div class="filter-section">
    <div class="row">
        <div class="col-md-3">
            <label class="form-label">Période</label>
            <select class="form-select" id="periodFilter" onchange="updateDashboard()">
                <option value="7">7 derniers jours</option>
                <option value="30" selected>30 derniers jours</option>
                <option value="90">3 derniers mois</option>
                <option value="365">12 derniers mois</option>
                <option value="all">Toute la période</option>
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">Type de véhicule</label>
            <select class="form-select" id="vehicleTypeFilter" onchange="updateDashboard()">
                <option value="all">Tous les véhicules</option>
                <option value="essence">Essence</option>
                <option value="diesel">Diesel</option>
                <option value="electrique">Électrique</option>
                <option value="hybride">Hybride</option>
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">Statut</label>
            <select class="form-select" id="statusFilter" onchange="updateDashboard()">
                <option value="all">Tous les statuts</option>
                <option value="disponible">Disponible</option>
                <option value="affecte">Affecté</option>
                <option value="en_maintenance">En maintenance</option>
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">Actions</label>
            <div class="d-grid">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-filter"></i> Appliquer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- KPI Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
        <div class="card dashboard-card stat-card metric-primary">
            <div class="card-body position-relative">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1">{{ stats.total_vehicules or 0 }}</h3>
                        <p class="mb-0">Véhicules Total</p>
                        <small class="kpi-badge bg-light text-dark">
                            {{ stats.vehicules_disponibles or 0 }} disponibles
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-car stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
        <div class="card dashboard-card stat-card metric-success">
            <div class="card-body position-relative">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1">{{ "{:,.0f}"|format(stats.cout_total_maintenances or 0) }}</h3>
                        <p class="mb-0">Coût Total (MAD)</p>
                        <small class="kpi-badge bg-light text-dark">
                            {{ "{:,.0f}"|format(stats.cout_mois_maintenances or 0) }} ce mois
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-coins stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
        <div class="card dashboard-card stat-card metric-warning">
            <div class="card-body position-relative">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1">{{ stats.total_maintenances or 0 }}</h3>
                        <p class="mb-0">Maintenances</p>
                        <small class="kpi-badge bg-light text-dark">
                            {{ stats.maintenances_planifiees or 0 }} planifiées
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-tools stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
        <div class="card dashboard-card stat-card metric-info">
            <div class="card-body position-relative">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h3 class="mb-1">{{ stats.total_conducteurs or 0 }}</h3>
                        <p class="mb-0">Conducteurs</p>
                        <small class="kpi-badge bg-light text-dark">
                            {{ stats.affectations_actives or 0 }} actifs
                        </small>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-users stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Métriques avancées -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="metric-card metric-primary">
            <div class="metric-value">
                <h4 class="mb-1">{{ "{:.1f}"|format((stats.vehicules_disponibles / stats.total_vehicules * 100) if stats.total_vehicules > 0 else 0) }}%</h4>
                <small>Taux Disponibilité</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="metric-card metric-success">
            <div class="metric-value">
                <h4 class="mb-1">{{ "{:,.0f}"|format((stats.cout_total_maintenances / stats.total_vehicules) if stats.total_vehicules > 0 else 0) }}</h4>
                <small>Coût/Véhicule (MAD)</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="metric-card metric-warning">
            <div class="metric-value">
                <h4 class="mb-1">{{ "{:.1f}"|format((stats.total_maintenances / stats.total_vehicules) if stats.total_vehicules > 0 else 0) }}</h4>
                <small>Maintenances/Véhicule</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="metric-card metric-danger">
            <div class="metric-value">
                <h4 class="mb-1">{{ "{:.1f}"|format((stats.vehicules_en_maintenance / stats.total_vehicules * 100) if stats.total_vehicules > 0 else 0) }}%</h4>
                <small>En Maintenance</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="metric-card metric-info">
            <div class="metric-value">
                <h4 class="mb-1">{{ "{:.1f}"|format((stats.affectations_actives / stats.total_conducteurs * 100) if stats.total_conducteurs > 0 else 0) }}%</h4>
                <small>Taux Affectation</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="metric-card metric-primary">
            <div class="metric-value">
                <h4 class="mb-1">{{ "{:,.0f}"|format(stats.cout_mois_maintenances or 0) }}</h4>
                <small>Budget Mensuel</small>
            </div>
        </div>
    </div>
</div>

<!-- Graphiques principaux -->
<div class="row mb-4">
    <!-- Évolution des maintenances -->
    <div class="col-lg-8 mb-4">
        <div class="card dashboard-card h-100">
            <div class="card-header bg-transparent">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-primary"></i>
                        Évolution des Maintenances et Coûts
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary active" data-chart="maintenances">Maintenances</button>
                        <button class="btn btn-outline-secondary" data-chart="costs">Coûts</button>
                        <button class="btn btn-outline-secondary" data-chart="both">Les deux</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container chart-container-large">
                    <canvas id="evolutionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Répartition par statut (Donut) -->
    <div class="col-lg-4 mb-4">
        <div class="card dashboard-card h-100">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie text-success"></i>
                    Répartition par Statut
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="statutDonutChart"></canvas>
                </div>
                <div class="mt-3">
                    {% for item in vehicules_par_statut %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-primary">{{ item.statut|title }}</span>
                        <span class="fw-bold">{{ item.count }} véhicules</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graphiques secondaires -->
<div class="row mb-4">
    <!-- Analyse par carburant (Polar Area) -->
    <div class="col-lg-4 mb-4">
        <div class="card dashboard-card h-100">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-gas-pump text-warning"></i>
                    Répartition par Carburant
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="carburantPolarChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Top marques (Horizontal Bar) -->
    <div class="col-lg-4 mb-4">
        <div class="card dashboard-card h-100">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-industry text-info"></i>
                    Top Marques
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="marqueHorizontalChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Gauge de performance -->
    <div class="col-lg-4 mb-4">
        <div class="card dashboard-card h-100">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt text-danger"></i>
                    Performance Globale
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="chart-container">
                    <canvas id="performanceGauge"></canvas>
                </div>
                <div class="mt-3">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">Disponibilité</small>
                            <div class="fw-bold text-success">{{ "{:.1f}"|format((stats.vehicules_disponibles / stats.total_vehicules * 100) if stats.total_vehicules > 0 else 0) }}%</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Efficacité</small>
                            <div class="fw-bold text-primary">{{ "{:.1f}"|format(85.5) }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration globale des couleurs
    const colors = {
        primary: '#007bff',
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545',
        info: '#17a2b8',
        light: '#f8f9fa',
        dark: '#343a40',
        gradient: {
            primary: ['#007bff', '#0056b3'],
            success: ['#28a745', '#1e7e34'],
            warning: ['#ffc107', '#e0a800'],
            danger: ['#dc3545', '#c82333'],
            info: ['#17a2b8', '#138496']
        },
        palette: [
            '#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8',
            '#6f42c1', '#fd7e14', '#20c997', '#e83e8c', '#6c757d'
        ]
    };

    // Configuration globale Chart.js
    Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#495057';
    Chart.defaults.plugins.legend.position = 'bottom';
    Chart.defaults.plugins.legend.labels.usePointStyle = true;
    Chart.defaults.plugins.legend.labels.padding = 20;

    // 1. Graphique d'évolution des maintenances (Line Chart)
    {% if maintenances_par_mois %}
    const evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
    const evolutionChart = new Chart(evolutionCtx, {
        type: 'line',
        data: {
            labels: [{% for item in maintenances_par_mois %}'{{ item.mois }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Nombre de maintenances',
                data: [{% for item in maintenances_par_mois %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: colors.primary,
                backgroundColor: colors.primary + '20',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: colors.primary,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }, {
                label: 'Coût total (MAD)',
                data: [{% for item in maintenances_par_mois %}{{ item.cout_total or 0 }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: colors.success,
                backgroundColor: colors.success + '20',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                pointBackgroundColor: colors.success,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: colors.primary,
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 1) {
                                return context.dataset.label + ': ' + context.parsed.y.toLocaleString() + ' MAD';
                            }
                            return context.dataset.label + ': ' + context.parsed.y;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Période',
                        font: { weight: 'bold' }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Nombre de maintenances',
                        font: { weight: 'bold' }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    beginAtZero: true
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Coût (MAD)',
                        font: { weight: 'bold' }
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    beginAtZero: true
                }
            },
            elements: {
                point: {
                    hoverBackgroundColor: '#fff'
                }
            }
        }
    });
    {% endif %}

    // 2. Graphique donut pour les statuts
    {% if vehicules_par_statut %}
    const statutCtx = document.getElementById('statutDonutChart').getContext('2d');
    const statutChart = new Chart(statutCtx, {
        type: 'doughnut',
        data: {
            labels: [{% for item in vehicules_par_statut %}'{{ item.statut|title }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for item in vehicules_par_statut %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: colors.palette.slice(0, {{ vehicules_par_statut|length }}),
                borderColor: '#fff',
                borderWidth: 3,
                hoverBorderWidth: 5,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: colors.primary,
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1000
            }
        }
    });
    {% endif %}

    // 3. Graphique polar area pour les carburants
    {% if vehicules_par_carburant %}
    const carburantCtx = document.getElementById('carburantPolarChart').getContext('2d');
    const carburantChart = new Chart(carburantCtx, {
        type: 'polarArea',
        data: {
            labels: [{% for item in vehicules_par_carburant %}'{{ item.carburant }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for item in vehicules_par_carburant %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: [
                    colors.primary + '80',
                    colors.success + '80',
                    colors.warning + '80',
                    colors.danger + '80',
                    colors.info + '80'
                ],
                borderColor: [
                    colors.primary,
                    colors.success,
                    colors.warning,
                    colors.danger,
                    colors.info
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: colors.primary,
                    borderWidth: 1,
                    cornerRadius: 8
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    pointLabels: {
                        font: {
                            size: 12
                        }
                    }
                }
            }
        }
    });
    {% endif %}

    // 4. Graphique horizontal bar pour les marques
    {% if vehicules_par_marque %}
    const marqueCtx = document.getElementById('marqueHorizontalChart').getContext('2d');
    const marqueChart = new Chart(marqueCtx, {
        type: 'bar',
        data: {
            labels: [{% for item in vehicules_par_marque %}'{{ item.marque }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Nombre de véhicules',
                data: [{% for item in vehicules_par_marque %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: colors.palette.map(color => color + '80'),
                borderColor: colors.palette,
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: colors.primary,
                    borderWidth: 1,
                    cornerRadius: 8
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    title: {
                        display: true,
                        text: 'Nombre de véhicules',
                        font: { weight: 'bold' }
                    }
                },
                y: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
    {% endif %}

    // 5. Gauge de performance (Doughnut modifié)
    const performanceCtx = document.getElementById('performanceGauge').getContext('2d');
    const availabilityRate = {{ (stats.vehicules_disponibles / stats.total_vehicules * 100) if stats.total_vehicules > 0 else 0 }};
    const performanceChart = new Chart(performanceCtx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [availabilityRate, 100 - availabilityRate],
                backgroundColor: [
                    availabilityRate > 80 ? colors.success : availabilityRate > 60 ? colors.warning : colors.danger,
                    '#e9ecef'
                ],
                borderWidth: 0,
                cutout: '75%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: false
                }
            },
            animation: {
                animateRotate: true,
                duration: 2000
            }
        },
        plugins: [{
            beforeDraw: function(chart) {
                const width = chart.width;
                const height = chart.height;
                const ctx = chart.ctx;

                ctx.restore();
                const fontSize = (height / 114).toFixed(2);
                ctx.font = fontSize + "em sans-serif";
                ctx.textBaseline = "middle";
                ctx.fillStyle = colors.dark;

                const text = availabilityRate.toFixed(1) + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX, textY);
                ctx.save();
            }
        }]
    });

    // Fonctions utilitaires
    window.refreshDashboard = function() {
        location.reload();
    };

    window.updateDashboard = function() {
        // Mise à jour dynamique des données
        console.log('Mise à jour du tableau de bord...');
    };

    window.applyFilters = function() {
        const period = document.getElementById('periodFilter').value;
        const vehicleType = document.getElementById('vehicleTypeFilter').value;
        const status = document.getElementById('statusFilter').value;

        console.log('Filtres appliqués:', { period, vehicleType, status });
        // Ici on pourrait faire un appel AJAX pour mettre à jour les données
    };

    window.exportToPDF = function() {
        window.print();
    };

    window.exportTable = function(tableId) {
        const table = document.getElementById(tableId + 'Table');
        if (table) {
            // Logique d'export de table
            console.log('Export de la table:', tableId);
        }
    };

    window.printTable = function(tableId) {
        const table = document.getElementById(tableId + 'Table');
        if (table) {
            const printWindow = window.open('', '', 'height=600,width=800');
            printWindow.document.write('<html><head><title>Impression</title>');
            printWindow.document.write('<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">');
            printWindow.document.write('</head><body>');
            printWindow.document.write('<div class="container mt-4">');
            printWindow.document.write('<h3>Rapport GesParc Auto</h3>');
            printWindow.document.write(table.outerHTML);
            printWindow.document.write('</div></body></html>');
            printWindow.document.close();
            printWindow.print();
        }
    };

    // Animation des cartes au scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observer toutes les cartes
    document.querySelectorAll('.dashboard-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Gestion des boutons de graphique
    document.querySelectorAll('[data-chart]').forEach(button => {
        button.addEventListener('click', function() {
            // Retirer la classe active de tous les boutons
            this.parentElement.querySelectorAll('.btn').forEach(btn => {
                btn.classList.remove('active');
            });
            // Ajouter la classe active au bouton cliqué
            this.classList.add('active');

            const chartType = this.getAttribute('data-chart');
            console.log('Changement de vue graphique:', chartType);
            // Ici on pourrait modifier les données du graphique
        });
    });
});
</script>
{% endblock %}
