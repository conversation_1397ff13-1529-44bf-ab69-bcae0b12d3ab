#!/usr/bin/env python3
"""
Test complet de l'intégration Matplotlib dans GesParc Auto
"""

import requests
import sys
import time
from matplotlib_analytics import GesparcAnalytics

def test_matplotlib_module():
    """Teste le module matplotlib_analytics directement"""
    print("🔬 Test du Module Matplotlib Analytics")
    print("=" * 50)
    
    try:
        analytics = GesparcAnalytics()
        print("✅ Module GesparcAnalytics importé avec succès")
        
        # Test de récupération des données
        data = analytics.get_data()
        print(f"✅ Données récupérées: {len(data)} tables")
        
        for table_name, df in data.items():
            print(f"   📊 {table_name}: {len(df)} enregistrements")
        
        # Test de génération d'un graphique simple
        print("\n🎨 Test de génération de graphiques...")
        
        try:
            dashboard_chart = analytics.create_vehicle_analysis_dashboard()
            if dashboard_chart:
                print("✅ Dashboard véhicules généré")
            else:
                print("⚠️ Dashboard vide")
        except Exception as e:
            print(f"❌ Erreur dashboard: {e}")
        
        try:
            evolution_chart = analytics.create_maintenance_evolution_chart()
            if evolution_chart:
                print("✅ Graphique évolution généré")
            else:
                print("⚠️ Graphique évolution vide")
        except Exception as e:
            print(f"❌ Erreur évolution: {e}")
        
        try:
            cost_chart = analytics.create_maintenance_cost_analysis()
            if cost_chart:
                print("✅ Analyse coûts générée")
            else:
                print("⚠️ Analyse coûts vide")
        except Exception as e:
            print(f"❌ Erreur coûts: {e}")
        
        try:
            heatmap_chart = analytics.create_performance_heatmap()
            if heatmap_chart:
                print("✅ Heatmap performance générée")
            else:
                print("⚠️ Heatmap vide")
        except Exception as e:
            print(f"❌ Erreur heatmap: {e}")
        
        try:
            predictive_chart = analytics.create_predictive_analysis()
            if predictive_chart:
                print("✅ Analyse prédictive générée")
            else:
                print("⚠️ Analyse prédictive vide")
        except Exception as e:
            print(f"❌ Erreur prédictive: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur module: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flask_integration():
    """Teste l'intégration Flask"""
    print("\n🌐 Test de l'Intégration Flask")
    print("=" * 40)
    
    try:
        # Test de la page principale
        print("1. Test de la page Analytics Matplotlib...")
        response = requests.get("http://localhost:5001/analytics/matplotlib", timeout=20)
        
        if response.status_code == 200:
            print("   ✅ Page accessible (200)")
            
            # Vérifications du contenu
            content = response.text
            checks = [
                ("Analytics Matplotlib", "Titre principal"),
                ("Dashboard d'Analyse", "Section dashboard"),
                ("Évolution des Maintenances", "Section évolution"),
                ("Analyse des Coûts", "Section coûts"),
                ("Heatmap de Performance", "Section heatmap"),
                ("Analyse Prédictive", "Section prédictive"),
                ("chart-image", "Classes d'images"),
                ("refreshChart", "Fonctions JavaScript")
            ]
            
            for check, description in checks:
                if check in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ⚠️ {description} manquant")
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
            return False
        
        # Test des API
        print("\n2. Test des API de graphiques...")
        
        chart_types = ['dashboard', 'evolution', 'costs', 'heatmap', 'predictive']
        
        for chart_type in chart_types:
            try:
                api_response = requests.get(f"http://localhost:5001/api/analytics/chart/{chart_type}", timeout=15)
                
                if api_response.status_code == 200:
                    data = api_response.json()
                    if data.get('success'):
                        print(f"   ✅ API {chart_type} fonctionnelle")
                        if data.get('chart_data'):
                            print(f"      📊 Données graphique présentes")
                        else:
                            print(f"      ⚠️ Pas de données graphique")
                    else:
                        print(f"   ❌ API {chart_type} erreur: {data.get('error')}")
                else:
                    print(f"   ❌ API {chart_type} erreur HTTP: {api_response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ API {chart_type} exception: {e}")
        
        # Test des téléchargements
        print("\n3. Test des téléchargements...")
        
        try:
            download_response = requests.get("http://localhost:5001/analytics/download/dashboard", timeout=15)
            if download_response.status_code == 200:
                if download_response.headers.get('Content-Type') == 'image/png':
                    print("   ✅ Téléchargement PNG fonctionnel")
                else:
                    print("   ⚠️ Type de contenu inattendu")
            else:
                print(f"   ❌ Téléchargement erreur: {download_response.status_code}")
        except Exception as e:
            print(f"   ❌ Téléchargement exception: {e}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
        print("💡 Vérifiez que l'application Flask est démarrée")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def test_performance():
    """Teste les performances de génération"""
    print("\n⚡ Test de Performance")
    print("=" * 30)
    
    try:
        analytics = GesparcAnalytics()
        
        # Test de performance pour chaque type de graphique
        chart_methods = [
            ('Dashboard', analytics.create_vehicle_analysis_dashboard),
            ('Évolution', analytics.create_maintenance_evolution_chart),
            ('Coûts', analytics.create_maintenance_cost_analysis),
            ('Heatmap', analytics.create_performance_heatmap),
            ('Prédictive', analytics.create_predictive_analysis)
        ]
        
        total_time = 0
        
        for name, method in chart_methods:
            start_time = time.time()
            try:
                result = method()
                end_time = time.time()
                duration = end_time - start_time
                total_time += duration
                
                print(f"   {name}: {duration:.2f}s {'✅' if duration < 5 else '⚠️' if duration < 10 else '❌'}")
                
            except Exception as e:
                print(f"   {name}: Erreur - {e}")
        
        print(f"\nTemps total: {total_time:.2f}s")
        
        if total_time < 15:
            print("✅ Performance excellente (< 15s)")
        elif total_time < 30:
            print("✅ Performance acceptable (< 30s)")
        else:
            print("⚠️ Performance lente (> 30s)")
        
        return total_time < 60
        
    except Exception as e:
        print(f"❌ Erreur de test de performance: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 Test Complet de l'Intégration Matplotlib")
    print("=" * 60)
    
    # Attendre que l'application soit prête
    print("⏳ Attente du démarrage de l'application...")
    time.sleep(3)
    
    success1 = test_matplotlib_module()
    success2 = test_flask_integration()
    success3 = test_performance()
    
    print("\n" + "=" * 60)
    print("📋 Résumé des Tests")
    print("=" * 60)
    
    print(f"Module Matplotlib: {'✅ OK' if success1 else '❌ ÉCHEC'}")
    print(f"Intégration Flask: {'✅ OK' if success2 else '❌ ÉCHEC'}")
    print(f"Performance: {'✅ OK' if success3 else '❌ ÉCHEC'}")
    
    if success1 and success2 and success3:
        print("\n🎉 Tous les tests sont passés !")
        print("🚀 L'intégration Matplotlib est opérationnelle !")
        print("\n📍 Accès:")
        print("   URL: http://localhost:5001/analytics/matplotlib")
        print("   Navigation: Menu Rapports → Analytics Matplotlib")
        sys.exit(0)
    else:
        print("\n❌ Certains tests ont échoué")
        print("🔧 Vérifiez les erreurs ci-dessus")
        sys.exit(1)

if __name__ == "__main__":
    main()
