<?php
/**
 * Proxy Simple pour GesParc Auto
 * Redirige localhost/gesparc vers Flask sur port 5001
 */

// Configuration
$FLASK_URL = 'http://127.0.0.1:5001';

// Fonction pour vérifier si Flask fonctionne
function checkFlask($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 2);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode === 200;
}

// Vérifier si Flask est accessible
if (checkFlask($FLASK_URL)) {
    // Flask fonctionne, rediriger
    header("Location: $FLASK_URL");
    exit;
} else {
    // Flask ne fonctionne pas, afficher la page d'erreur
    ?>
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>GesParc Auto - Démarrage Requis</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
            }
            .main-card {
                box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                border-radius: 20px;
                overflow: hidden;
            }
            .header-section {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                padding: 2rem;
                text-align: center;
            }
            .content-section {
                background: white;
                padding: 2rem;
            }
            .step-item {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 1rem;
                margin: 0.5rem 0;
                border-left: 4px solid #007bff;
            }
            .btn-action {
                border-radius: 25px;
                padding: 0.75rem 2rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-10 col-lg-8">
                    <div class="main-card">
                        <div class="header-section">
                            <i class="fas fa-car fa-4x mb-3"></i>
                            <h1 class="mb-2">GesParc Auto</h1>
                            <p class="mb-0 opacity-75">Système de Gestion de Parc Automobile</p>
                        </div>
                        
                        <div class="content-section">
                            <div class="alert alert-warning d-flex align-items-center mb-4">
                                <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                                <div>
                                    <strong>Application Non Démarrée</strong><br>
                                    L'application Flask GesParc Auto doit être démarrée pour continuer.
                                </div>
                            </div>
                            
                            <h4 class="mb-3">
                                <i class="fas fa-play-circle text-primary"></i>
                                Instructions de Démarrage
                            </h4>
                            
                            <div class="step-item">
                                <strong>1. Ouvrir un Terminal</strong><br>
                                <small>Invite de commandes Windows ou PowerShell</small>
                            </div>
                            
                            <div class="step-item">
                                <strong>2. Naviguer vers le Répertoire</strong><br>
                                <code>cd c:\Apache24\htdocs\gesparc</code>
                            </div>
                            
                            <div class="step-item">
                                <strong>3. Lancer l'Application</strong><br>
                                <code>python gesparc_app.py</code>
                            </div>
                            
                            <div class="step-item">
                                <strong>4. Actualiser cette Page</strong><br>
                                <small>L'application se lancera automatiquement</small>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
                                <button onclick="location.reload()" class="btn btn-primary btn-action">
                                    <i class="fas fa-sync-alt"></i> Vérifier à Nouveau
                                </button>
                                <a href="http://localhost:5001" class="btn btn-outline-primary btn-action" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> Accès Direct
                                </a>
                            </div>
                            
                            <hr class="my-4">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-rocket text-success"></i> Scripts de Démarrage</h6>
                                    <ul class="list-unstyled">
                                        <li><code>start_gesparc.bat</code></li>
                                        <li><code>start_gesparc.ps1</code></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-info-circle text-info"></i> Informations</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>Port Flask:</strong> 5001</li>
                                        <li><strong>URL Directe:</strong> localhost:5001</li>
                                        <li><strong>Via Apache:</strong> localhost/gesparc</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i>
                                    Auto-vérification toutes les 10 secondes
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            // Auto-refresh toutes les 10 secondes
            setInterval(function() {
                location.reload();
            }, 10000);
            
            // Vérification immédiate au clic
            document.addEventListener('DOMContentLoaded', function() {
                // Ajouter un effet de pulsation au bouton principal
                const btn = document.querySelector('.btn-primary');
                if (btn) {
                    setInterval(function() {
                        btn.style.transform = 'scale(1.05)';
                        setTimeout(function() {
                            btn.style.transform = 'scale(1)';
                        }, 200);
                    }, 3000);
                }
            });
        </script>
    </body>
    </html>
    <?php
}
?>
