{% extends "base.html" %}

{% block title %}Analytics Matplotlib - GesParc Auto{% endblock %}

{% block extra_css %}
<style>
    .analytics-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border: none;
        border-radius: 15px;
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .analytics-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .chart-container {
        position: relative;
        text-align: center;
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .chart-image {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .chart-actions {
        margin-top: 15px;
    }
    
    .chart-actions .btn {
        margin: 0 5px;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 40px;
    }
    
    .no-data {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    
    .analytics-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 20px 20px;
    }
    
    .analytics-nav {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .nav-pills .nav-link {
        border-radius: 20px;
        margin: 0 5px;
        transition: all 0.3s ease;
    }
    
    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, #007bff, #0056b3);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,123,255,0.3);
    }
    
    @media (max-width: 768px) {
        .analytics-header {
            padding: 20px 0;
        }
        
        .chart-container {
            padding: 15px;
            margin: 15px 0;
        }
        
        .chart-actions .btn {
            margin: 5px 2px;
            font-size: 0.9rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="analytics-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-chart-area"></i> 
                    Analytics Matplotlib
                </h1>
                <p class="mb-0 opacity-75">Analyses visuelles avancées avec graphiques haute qualité</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg" onclick="refreshAllCharts()">
                    <i class="fas fa-sync-alt"></i> Actualiser Tout
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Navigation des graphiques -->
    <div class="analytics-nav">
        <ul class="nav nav-pills justify-content-center" id="chartTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="evolution-tab" data-bs-toggle="pill" 
                        data-bs-target="#evolution" type="button" role="tab">
                    <i class="fas fa-chart-line"></i> Évolution
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="dashboard-tab" data-bs-toggle="pill" 
                        data-bs-target="#dashboard" type="button" role="tab">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="costs-tab" data-bs-toggle="pill" 
                        data-bs-target="#costs" type="button" role="tab">
                    <i class="fas fa-coins"></i> Coûts
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="heatmap-tab" data-bs-toggle="pill" 
                        data-bs-target="#heatmap" type="button" role="tab">
                    <i class="fas fa-th"></i> Heatmap
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="predictive-tab" data-bs-toggle="pill"
                        data-bs-target="#predictive" type="button" role="tab">
                    <i class="fas fa-crystal-ball"></i> Prédictif
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="advanced-fleet-tab" data-bs-toggle="pill"
                        data-bs-target="#advanced-fleet" type="button" role="tab">
                    <i class="fas fa-rocket"></i> Analytics Avancés
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="financial-tab" data-bs-toggle="pill"
                        data-bs-target="#financial" type="button" role="tab">
                    <i class="fas fa-chart-pie"></i> Financier
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="correlation-tab" data-bs-toggle="pill"
                        data-bs-target="#correlation" type="button" role="tab">
                    <i class="fas fa-project-diagram"></i> Corrélations
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="operational-tab" data-bs-toggle="pill"
                        data-bs-target="#operational" type="button" role="tab">
                    <i class="fas fa-cogs"></i> Efficacité
                </button>
            </li>
        </ul>
    </div>

    <!-- Contenu des graphiques -->
    <div class="tab-content" id="chartTabsContent">
        <!-- Évolution des maintenances -->
        <div class="tab-pane fade show active" id="evolution" role="tabpanel">
            <div class="analytics-card">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line"></i> 
                            Évolution des Maintenances et Coûts
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="evolution-container">
                            {% if charts.evolution %}
                            <img src="data:image/png;base64,{{ charts.evolution }}" 
                                 class="chart-image" alt="Évolution des maintenances">
                            {% else %}
                            <div class="no-data">
                                <i class="fas fa-chart-line fa-3x mb-3"></i>
                                <h5>Aucune donnée d'évolution disponible</h5>
                                <p>Ajoutez des maintenances pour voir l'évolution temporelle</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="chart-actions">
                            <button class="btn btn-outline-primary" onclick="refreshChart('evolution')">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <a href="{{ url_for('download_analytics_chart', chart_type='evolution') }}" 
                               class="btn btn-outline-success">
                                <i class="fas fa-download"></i> Télécharger PNG
                            </a>
                            <button class="btn btn-outline-info" onclick="customizeChart('evolution')">
                                <i class="fas fa-cog"></i> Personnaliser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard véhicules -->
        <div class="tab-pane fade" id="dashboard" role="tabpanel">
            <div class="analytics-card">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tachometer-alt"></i> 
                            Dashboard d'Analyse des Véhicules
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="dashboard-container">
                            {% if charts.dashboard %}
                            <img src="data:image/png;base64,{{ charts.dashboard }}" 
                                 class="chart-image" alt="Dashboard véhicules">
                            {% else %}
                            <div class="no-data">
                                <i class="fas fa-tachometer-alt fa-3x mb-3"></i>
                                <h5>Aucune donnée de véhicule disponible</h5>
                                <p>Ajoutez des véhicules pour voir le dashboard</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="chart-actions">
                            <button class="btn btn-outline-primary" onclick="refreshChart('dashboard')">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <a href="{{ url_for('download_analytics_chart', chart_type='dashboard') }}" 
                               class="btn btn-outline-success">
                                <i class="fas fa-download"></i> Télécharger PNG
                            </a>
                            <button class="btn btn-outline-info" onclick="customizeChart('dashboard')">
                                <i class="fas fa-cog"></i> Personnaliser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analyse des coûts -->
        <div class="tab-pane fade" id="costs" role="tabpanel">
            <div class="analytics-card">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-coins"></i> 
                            Analyse Détaillée des Coûts
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="costs-container">
                            {% if charts.costs %}
                            <img src="data:image/png;base64,{{ charts.costs }}" 
                                 class="chart-image" alt="Analyse des coûts">
                            {% else %}
                            <div class="no-data">
                                <i class="fas fa-coins fa-3x mb-3"></i>
                                <h5>Aucune donnée de coût disponible</h5>
                                <p>Ajoutez des coûts de maintenance pour voir l'analyse</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="chart-actions">
                            <button class="btn btn-outline-primary" onclick="refreshChart('costs')">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <a href="{{ url_for('download_analytics_chart', chart_type='costs') }}" 
                               class="btn btn-outline-success">
                                <i class="fas fa-download"></i> Télécharger PNG
                            </a>
                            <button class="btn btn-outline-info" onclick="customizeChart('costs')">
                                <i class="fas fa-cog"></i> Personnaliser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Heatmap de performance -->
        <div class="tab-pane fade" id="heatmap" role="tabpanel">
            <div class="analytics-card">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-th"></i> 
                            Heatmap de Performance
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="heatmap-container">
                            {% if charts.heatmap %}
                            <img src="data:image/png;base64,{{ charts.heatmap }}" 
                                 class="chart-image" alt="Heatmap de performance">
                            {% else %}
                            <div class="no-data">
                                <i class="fas fa-th fa-3x mb-3"></i>
                                <h5>Données insuffisantes pour la heatmap</h5>
                                <p>Plus de données sont nécessaires pour générer la heatmap</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="chart-actions">
                            <button class="btn btn-outline-primary" onclick="refreshChart('heatmap')">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <a href="{{ url_for('download_analytics_chart', chart_type='heatmap') }}" 
                               class="btn btn-outline-success">
                                <i class="fas fa-download"></i> Télécharger PNG
                            </a>
                            <button class="btn btn-outline-info" onclick="customizeChart('heatmap')">
                                <i class="fas fa-cog"></i> Personnaliser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analyse prédictive -->
        <div class="tab-pane fade" id="predictive" role="tabpanel">
            <div class="analytics-card">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-crystal-ball"></i> 
                            Analyse Prédictive
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="predictive-container">
                            {% if charts.predictive %}
                            <img src="data:image/png;base64,{{ charts.predictive }}" 
                                 class="chart-image" alt="Analyse prédictive">
                            {% else %}
                            <div class="no-data">
                                <i class="fas fa-crystal-ball fa-3x mb-3"></i>
                                <h5>Données insuffisantes pour l'analyse prédictive</h5>
                                <p>Plus d'historique est nécessaire pour les prédictions</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="chart-actions">
                            <button class="btn btn-outline-primary" onclick="refreshChart('predictive')">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <a href="{{ url_for('download_analytics_chart', chart_type='predictive') }}" 
                               class="btn btn-outline-success">
                                <i class="fas fa-download"></i> Télécharger PNG
                            </a>
                            <button class="btn btn-outline-info" onclick="customizeChart('predictive')">
                                <i class="fas fa-cog"></i> Personnaliser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Avancés de la Flotte -->
        <div class="tab-pane fade" id="advanced-fleet" role="tabpanel">
            <div class="analytics-card">
                <div class="card">
                    <div class="card-header bg-gradient-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-rocket"></i>
                            Analytics Avancés de la Flotte
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="advanced-fleet-container">
                            {% if charts.advanced_fleet %}
                            <img src="data:image/png;base64,{{ charts.advanced_fleet }}"
                                 class="chart-image" alt="Analytics avancés de la flotte">
                            {% else %}
                            <div class="no-data">
                                <i class="fas fa-rocket fa-3x mb-3"></i>
                                <h5>Analytics avancés non disponibles</h5>
                                <p>Plus de données sont nécessaires pour les analytics avancés</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="chart-actions">
                            <button class="btn btn-outline-primary" onclick="refreshChart('advanced_fleet')">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <a href="{{ url_for('download_analytics_chart', chart_type='advanced_fleet') }}"
                               class="btn btn-outline-success">
                                <i class="fas fa-download"></i> Télécharger PNG
                            </a>
                            <button class="btn btn-outline-info" onclick="customizeChart('advanced_fleet')">
                                <i class="fas fa-cog"></i> Personnaliser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Financier -->
        <div class="tab-pane fade" id="financial" role="tabpanel">
            <div class="analytics-card">
                <div class="card">
                    <div class="card-header bg-gradient-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie"></i>
                            Dashboard Financier Avancé
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="financial-container">
                            {% if charts.financial %}
                            <img src="data:image/png;base64,{{ charts.financial }}"
                                 class="chart-image" alt="Dashboard financier">
                            {% else %}
                            <div class="no-data">
                                <i class="fas fa-chart-pie fa-3x mb-3"></i>
                                <h5>Dashboard financier non disponible</h5>
                                <p>Ajoutez des données de coûts pour voir l'analyse financière</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="chart-actions">
                            <button class="btn btn-outline-primary" onclick="refreshChart('financial')">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <a href="{{ url_for('download_analytics_chart', chart_type='financial') }}"
                               class="btn btn-outline-success">
                                <i class="fas fa-download"></i> Télécharger PNG
                            </a>
                            <button class="btn btn-outline-info" onclick="customizeChart('financial')">
                                <i class="fas fa-cog"></i> Personnaliser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analyse de Corrélation -->
        <div class="tab-pane fade" id="correlation" role="tabpanel">
            <div class="analytics-card">
                <div class="card">
                    <div class="card-header bg-gradient-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-project-diagram"></i>
                            Analyse de Corrélation
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="correlation-container">
                            {% if charts.correlation %}
                            <img src="data:image/png;base64,{{ charts.correlation }}"
                                 class="chart-image" alt="Analyse de corrélation">
                            {% else %}
                            <div class="no-data">
                                <i class="fas fa-project-diagram fa-3x mb-3"></i>
                                <h5>Analyse de corrélation non disponible</h5>
                                <p>Plus de données sont nécessaires pour l'analyse de corrélation</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="chart-actions">
                            <button class="btn btn-outline-primary" onclick="refreshChart('correlation')">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <a href="{{ url_for('download_analytics_chart', chart_type='correlation') }}"
                               class="btn btn-outline-success">
                                <i class="fas fa-download"></i> Télécharger PNG
                            </a>
                            <button class="btn btn-outline-info" onclick="customizeChart('correlation')">
                                <i class="fas fa-cog"></i> Personnaliser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard d'Efficacité Opérationnelle -->
        <div class="tab-pane fade" id="operational" role="tabpanel">
            <div class="analytics-card">
                <div class="card">
                    <div class="card-header bg-gradient-warning text-dark">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cogs"></i>
                            Dashboard d'Efficacité Opérationnelle
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="operational-container">
                            {% if charts.operational %}
                            <img src="data:image/png;base64,{{ charts.operational }}"
                                 class="chart-image" alt="Dashboard d'efficacité opérationnelle">
                            {% else %}
                            <div class="no-data">
                                <i class="fas fa-cogs fa-3x mb-3"></i>
                                <h5>Dashboard d'efficacité non disponible</h5>
                                <p>Plus de données opérationnelles sont nécessaires</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="chart-actions">
                            <button class="btn btn-outline-primary" onclick="refreshChart('operational')">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <a href="{{ url_for('download_analytics_chart', chart_type='operational') }}"
                               class="btn btn-outline-success">
                                <i class="fas fa-download"></i> Télécharger PNG
                            </a>
                            <button class="btn btn-outline-info" onclick="customizeChart('operational')">
                                <i class="fas fa-cog"></i> Personnaliser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation des cartes au chargement
    const cards = document.querySelectorAll('.analytics-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});

function refreshChart(chartType) {
    const container = document.getElementById(chartType + '-container');
    const loadingHtml = `
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-3">Génération du graphique...</p>
        </div>
    `;

    container.innerHTML = loadingHtml;

    fetch(`/api/analytics/chart/${chartType}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                container.innerHTML = `
                    <img src="data:image/png;base64,${data.chart_data}"
                         class="chart-image" alt="${chartType} chart">
                `;

                // Animation d'apparition
                const img = container.querySelector('.chart-image');
                img.style.opacity = '0';
                img.style.transform = 'scale(0.9)';
                img.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

                setTimeout(() => {
                    img.style.opacity = '1';
                    img.style.transform = 'scale(1)';
                }, 100);

                showNotification('Graphique actualisé avec succès', 'success');
            } else {
                container.innerHTML = `
                    <div class="no-data">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3 text-warning"></i>
                        <h5>Erreur de génération</h5>
                        <p>${data.error}</p>
                    </div>
                `;
                showNotification('Erreur lors de l\'actualisation', 'error');
            }
        })
        .catch(error => {
            container.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3 text-danger"></i>
                    <h5>Erreur de connexion</h5>
                    <p>Impossible de charger le graphique</p>
                </div>
            `;
            showNotification('Erreur de connexion', 'error');
            console.error('Erreur:', error);
        });
}

function refreshAllCharts() {
    const chartTypes = ['evolution', 'dashboard', 'costs', 'heatmap', 'predictive',
                       'advanced_fleet', 'financial', 'correlation', 'operational'];
    let completed = 0;

    showNotification('Actualisation de tous les graphiques...', 'info');

    chartTypes.forEach((chartType, index) => {
        setTimeout(() => {
            refreshChart(chartType);
            completed++;

            if (completed === chartTypes.length) {
                setTimeout(() => {
                    showNotification('Tous les graphiques ont été actualisés', 'success');
                }, 1000);
            }
        }, index * 500);
    });
}

function customizeChart(chartType) {
    showNotification('Fonctionnalité de personnalisation à venir', 'info');
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Raccourcis clavier
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 'r':
                e.preventDefault();
                refreshAllCharts();
                break;
            case '1':
                e.preventDefault();
                document.getElementById('evolution-tab').click();
                break;
            case '2':
                e.preventDefault();
                document.getElementById('dashboard-tab').click();
                break;
        }
    }
});
</script>
{% endblock %}
