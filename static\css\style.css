/* Styles personnalisés pour GesParc Auto */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
}

/* Boutons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Tables */
.table {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Formulaires */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Badges de statut */
.badge-disponible {
    background-color: var(--success-color) !important;
}

.badge-en-maintenance {
    background-color: var(--warning-color) !important;
    color: #000 !important;
}

.badge-hors-service {
    background-color: var(--danger-color) !important;
}

.badge-affecte {
    background-color: var(--info-color) !important;
    color: #000 !important;
}

/* Statistiques dashboard */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

/* Alertes personnalisées */
.alert-sm {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Responsive */
@media (max-width: 768px) {
    .card-body .row .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

/* Icônes de statut */
.status-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-disponible {
    background-color: var(--success-color);
}

.status-maintenance {
    background-color: var(--warning-color);
}

.status-hors-service {
    background-color: var(--danger-color);
}

/* Loader */
.spinner-custom {
    width: 2rem;
    height: 2rem;
    border: 0.25em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

/* Sidebar pour les filtres */
.filter-sidebar {
    background-color: #fff;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Graphiques */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}
