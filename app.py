from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'votre_cle_secrete_ici'  # À changer en production

# Configuration de la base de données
DATABASE = 'parc_automobile.db'

def get_db_connection():
    """Établit une connexion à la base de données SQLite"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def index():
    """Page d'accueil avec tableau de bord"""

    # Récupérer les statistiques
    stats = {
        'total_vehicules': 0,
        'vehicules_disponibles': 0,
        'total_conducteurs': 0,
        'maintenances_prevues': 0
    }

    maintenances_prochaines = []

    try:
        conn = get_db_connection()

        # Statistiques véhicules
        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]
        stats['vehicules_disponibles'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'").fetchone()[0]

        # Statistiques conducteurs
        stats['total_conducteurs'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]

        # Maintenances prévues
        stats['maintenances_prevues'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'planifiee'").fetchone()[0]

        # Maintenances à venir (prochains 30 jours)
        maintenances_prochaines = conn.execute('''
            SELECT m.*, v.immatriculation
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            WHERE m.statut = 'planifiee'
            AND date(m.date_maintenance) BETWEEN date('now') AND date('now', '+30 days')
            ORDER BY m.date_maintenance
            LIMIT 5
        ''').fetchall()

        conn.close()

    except Exception as e:
        print(f"Erreur lors de la récupération des statistiques: {e}")

    return render_template('index.html', stats=stats, maintenances_prochaines=maintenances_prochaines)

# Routes temporaires pour éviter les erreurs 404
@app.route('/vehicules')
def vehicules():
    return "Page des véhicules - En cours de développement"

@app.route('/vehicules/ajouter')
def ajouter_vehicule():
    return "Ajouter un véhicule - En cours de développement"

@app.route('/conducteurs')
def conducteurs():
    return "Page des conducteurs - En cours de développement"

@app.route('/conducteurs/ajouter')
def ajouter_conducteur():
    return "Ajouter un conducteur - En cours de développement"

@app.route('/maintenances')
def maintenances():
    return "Page des maintenances - En cours de développement"

@app.route('/maintenances/ajouter')
def ajouter_maintenance():
    return "Ajouter une maintenance - En cours de développement"

@app.route('/affectations')
def affectations():
    return "Page des affectations - En cours de développement"

@app.route('/rapports')
def rapports():
    return "Page des rapports - En cours de développement"

if __name__ == '__main__':
    print("Démarrage de l'application GesParc Auto...")
    print("Application disponible sur: http://localhost:5001")
    app.run(debug=True, host='127.0.0.1', port=5001)
