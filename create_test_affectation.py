#!/usr/bin/env python3
import sqlite3
from datetime import datetime

# Connexion à la base de données
conn = sqlite3.connect('parc_automobile.db')
cursor = conn.cursor()

print("🔧 Création d'une affectation de test")
print("=" * 40)

# Récupérer un véhicule disponible
cursor.execute('SELECT * FROM vehicules WHERE statut = "disponible" LIMIT 1')
vehicule = cursor.fetchone()

# Récupérer un conducteur actif
cursor.execute('SELECT * FROM conducteurs WHERE statut = "actif" LIMIT 1')
conducteur = cursor.fetchone()

if vehicule and conducteur:
    date_debut = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Créer une affectation de test
    cursor.execute('''
        INSERT INTO affectations (vehicule_id, conducteur_id, date_debut, statut, mission)
        VALUES (?, ?, ?, 'active', 'Test affectation pour validation date de fin')
    ''', (vehicule[0], conducteur[0], date_debut))
    
    # Mettre à jour le statut du véhicule
    cursor.execute('UPDATE vehicules SET statut = "affecte" WHERE id = ?', (vehicule[0],))
    
    conn.commit()
    
    # Récupérer l'ID de la nouvelle affectation
    cursor.execute('SELECT last_insert_rowid()')
    new_id = cursor.fetchone()[0]
    
    print(f"✅ Affectation créée (ID: {new_id})")
    print(f"   Véhicule: {vehicule[2]} - {vehicule[3]} {vehicule[4]}")
    print(f"   Conducteur: {conducteur[2]} {conducteur[1]}")
    print(f"   Date début: {date_debut}")
    print(f"   URL test: http://localhost:8080/affectations/{new_id}/terminer")
else:
    print("❌ Impossible de créer une affectation (véhicule ou conducteur manquant)")

conn.close()
print("\n🎉 Création terminée !")