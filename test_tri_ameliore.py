#!/usr/bin/env python3
"""
Test du tri amélioré des affectations avec critère secondaire
"""

import sqlite3
from datetime import datetime

def test_tri_ameliore():
    """Test du tri amélioré avec critère secondaire"""
    print("🧪 Test du Tri Amélioré des Affectations")
    print("=" * 45)
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect('parc_automobile.db')
        conn.row_factory = sqlite3.Row
        
        # Exécuter la requête améliorée
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele,
                   c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC, a.id DESC
        ''').fetchall()
        
        if not affectations:
            print("⚠️  Aucune affectation trouvée")
            conn.close()
            return False
        
        print(f"✅ {len(affectations)} affectation(s) trouvée(s)")
        print(f"\n📋 Tri amélioré (Date DESC, puis ID DESC):")
        print("-" * 85)
        print(f"{'ID':<4} {'Date Début':<12} {'Véhicule':<12} {'Conducteur':<20} {'Statut':<10} {'Ordre':<6}")
        print("-" * 85)
        
        tri_correct = True
        date_precedente = None
        id_precedent_meme_date = None
        
        for i, affectation in enumerate(affectations):
            # Afficher les informations
            date_debut = affectation['date_debut'] or 'Non définie'
            vehicule = affectation['immatriculation']
            conducteur = f"{affectation['prenom']} {affectation['nom']}"
            statut = affectation['statut']
            ordre = f"#{i+1}"
            
            print(f"{affectation['id']:<4} {date_debut:<12} {vehicule:<12} {conducteur:<20} {statut:<10} {ordre:<6}")
            
            # Vérifier l'ordre du tri amélioré
            if affectation['date_debut']:
                date_actuelle = affectation['date_debut']
                id_actuel = affectation['id']
                
                if date_precedente is not None:
                    # Vérifier que la date actuelle <= date précédente
                    if date_actuelle > date_precedente:
                        tri_correct = False
                        print(f"    ❌ ERREUR DATE: {date_actuelle} > {date_precedente}")
                    
                    # Si même date, vérifier que ID actuel <= ID précédent
                    elif date_actuelle == date_precedente and id_precedent_meme_date is not None:
                        if id_actuel > id_precedent_meme_date:
                            tri_correct = False
                            print(f"    ❌ ERREUR ID: {id_actuel} > {id_precedent_meme_date} (même date)")
                
                # Mettre à jour les valeurs précédentes
                if date_actuelle == date_precedente:
                    id_precedent_meme_date = id_actuel
                else:
                    id_precedent_meme_date = id_actuel
                    
                date_precedente = date_actuelle
        
        print("-" * 85)
        
        # Résultat du test
        if tri_correct:
            print("✅ TRI AMÉLIORÉ CORRECT")
            print("  📅 Dates triées par ordre décroissant")
            print("  🔢 IDs triés par ordre décroissant pour la même date")
        else:
            print("❌ TRI AMÉLIORÉ INCORRECT")
        
        # Analyse des groupes par date
        print(f"\n📊 Analyse par groupes de dates:")
        groupes_dates = {}
        for aff in affectations:
            date = aff['date_debut']
            if date not in groupes_dates:
                groupes_dates[date] = []
            groupes_dates[date].append(aff['id'])
        
        for date, ids in groupes_dates.items():
            print(f"  📅 {date}: {len(ids)} affectation(s) - IDs: {ids}")
            
            # Vérifier que les IDs sont triés par ordre décroissant
            ids_tries = sorted(ids, reverse=True)
            if ids == ids_tries:
                print(f"    ✅ IDs correctement triés pour cette date")
            else:
                print(f"    ❌ IDs mal triés: {ids} (attendu: {ids_tries})")
        
        # Comparaison avec l'ancien tri
        print(f"\n🔄 Comparaison avec l'ancien tri:")
        
        # Ancien tri (seulement par date)
        affectations_ancien = conn.execute('''
            SELECT a.id, a.date_debut
            FROM affectations a
            ORDER BY a.date_debut DESC
        ''').fetchall()
        
        print(f"  📋 Ancien tri (date seulement):")
        for i, aff in enumerate(affectations_ancien[:5]):
            print(f"    #{i+1}: ID {aff['id']}, Date {aff['date_debut']}")
        
        print(f"  📋 Nouveau tri (date + ID):")
        for i, aff in enumerate(affectations[:5]):
            print(f"    #{i+1}: ID {aff['id']}, Date {aff['date_debut']}")
        
        # Avantages du nouveau tri
        print(f"\n🎯 Avantages du tri amélioré:")
        print(f"  ✅ Ordre prévisible pour les affectations de même date")
        print(f"  ✅ Affectations les plus récentes (ID élevé) en premier")
        print(f"  ✅ Cohérence dans l'affichage entre les rechargements")
        print(f"  ✅ Facilite la navigation et la recherche")
        
        conn.close()
        return tri_correct
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_interface_visuelle():
    """Test de l'amélioration visuelle de l'interface"""
    print(f"\n🎨 Test de l'Interface Visuelle:")
    print("-" * 35)
    
    # Vérifier que l'indicateur visuel est ajouté
    try:
        with open('templates/affectations.html', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Chercher l'indicateur de tri
        if 'fa-sort-down' in contenu and 'text-primary' in contenu:
            print("  ✅ Indicateur visuel de tri ajouté")
            print("  📊 Icône fa-sort-down présente")
            print("  🎨 Couleur text-primary appliquée")
            
            # Chercher le titre explicatif
            if 'Trié par date décroissante' in contenu:
                print("  ✅ Tooltip explicatif présent")
                print("  💡 Aide utilisateur disponible")
            else:
                print("  ⚠️  Tooltip explicatif manquant")
        else:
            print("  ❌ Indicateur visuel manquant")
        
        # Vérifications supplémentaires
        if '<th>' in contenu and 'Date début' in contenu:
            print("  ✅ Structure du tableau préservée")
        
        print(f"\n🎯 Améliorations visuelles:")
        print(f"  📍 Indicateur de tri dans l'en-tête de colonne")
        print(f"  🔽 Icône fa-sort-down pour indiquer l'ordre décroissant")
        print(f"  💙 Couleur bleue (text-primary) pour attirer l'attention")
        print(f"  💬 Tooltip au survol pour expliquer le tri")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur test interface: {e}")
        return False

def test_performance_tri_ameliore():
    """Test de performance du tri amélioré"""
    print(f"\n⚡ Test de Performance du Tri Amélioré:")
    print("-" * 45)
    
    try:
        import time
        conn = sqlite3.connect('parc_automobile.db')
        
        # Mesurer le temps d'exécution du nouveau tri
        start_time = time.time()
        
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele,
                   c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC, a.id DESC
        ''').fetchall()
        
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000
        
        print(f"  📊 Nombre d'affectations: {len(affectations)}")
        print(f"  ⏱️  Temps d'exécution: {execution_time:.2f} ms")
        print(f"  🔄 Critères de tri: date_debut DESC, id DESC")
        
        if execution_time < 100:
            print(f"  ✅ Performance excellente")
        elif execution_time < 500:
            print(f"  ✅ Performance bonne")
        else:
            print(f"  ⚠️  Performance à surveiller")
        
        # Comparer avec l'ancien tri
        start_time_ancien = time.time()
        
        affectations_ancien = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele,
                   c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC
        ''').fetchall()
        
        end_time_ancien = time.time()
        execution_time_ancien = (end_time_ancien - start_time_ancien) * 1000
        
        print(f"\n  📈 Comparaison performance:")
        print(f"    Ancien tri: {execution_time_ancien:.2f} ms")
        print(f"    Nouveau tri: {execution_time:.2f} ms")
        
        difference = execution_time - execution_time_ancien
        if abs(difference) < 1:
            print(f"    ✅ Impact négligeable ({difference:+.2f} ms)")
        else:
            print(f"    📊 Différence: {difference:+.2f} ms")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur test performance: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Test du tri amélioré des affectations...")
    
    # Tests
    tri_success = test_tri_ameliore()
    interface_success = test_interface_visuelle()
    perf_success = test_performance_tri_ameliore()
    
    # Résultat final
    print(f"\n" + "="*45)
    if tri_success and interface_success and perf_success:
        print("🎉 TRI AMÉLIORÉ IMPLÉMENTÉ AVEC SUCCÈS!")
        print("✅ Tri par date décroissante + ID décroissant")
        print("🎨 Indicateur visuel ajouté dans l'interface")
        print("⚡ Performance maintenue")
        print("📋 Ordre prévisible et cohérent")
    else:
        print("⚠️  PROBLÈME DANS L'AMÉLIORATION")
        print("🔧 Vérifiez les modifications apportées")
