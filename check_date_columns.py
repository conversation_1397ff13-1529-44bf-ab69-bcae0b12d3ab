#!/usr/bin/env python3
"""
Vérifier les colonnes de date dans la table affectations
"""

import sqlite3

def check_date_columns():
    """Vérifier les types des colonnes de date"""
    try:
        conn = sqlite3.connect('parc_automobile.db')
        cursor = conn.cursor()
        
        # Obtenir la structure de la table
        cursor.execute("PRAGMA table_info(affectations)")
        columns = cursor.fetchall()
        
        print("📋 Colonnes de date dans la table 'affectations':")
        print("-" * 50)
        
        date_columns = []
        for col in columns:
            cid, name, type_name, notnull, default_value, pk = col
            if 'date' in name.lower():
                date_columns.append((name, type_name))
                print(f"  {name}: {type_name}")
        
        # Tester quelques affectations existantes
        print(f"\n📊 Exemples de données existantes:")
        print("-" * 50)
        
        affectations = cursor.execute('''
            SELECT id, date_debut, date_fin, date_creation
            FROM affectations
            ORDER BY id DESC
            LIMIT 5
        ''').fetchall()
        
        for aff in affectations:
            print(f"ID {aff[0]}:")
            print(f"  Date début: {aff[1]}")
            print(f"  Date fin: {aff[2]}")
            print(f"  Date création: {aff[3]}")
            print()
        
        conn.close()
        
        # Recommandations
        print("💡 Recommandations:")
        print("-" * 20)
        
        for name, type_name in date_columns:
            if type_name == 'DATE':
                print(f"  ⚠️  {name} ({type_name}) - Peut stocker date + heure mais format limité")
            elif type_name == 'TIMESTAMP':
                print(f"  ✅ {name} ({type_name}) - Parfait pour date + heure")
            else:
                print(f"  ❓ {name} ({type_name}) - Type inattendu")
        
        print(f"\n🔧 Actions recommandées:")
        print(f"  1. SQLite peut stocker datetime dans colonnes DATE")
        print(f"  2. Format recommandé: 'YYYY-MM-DD HH:MM:SS'")
        print(f"  3. Pas besoin de modifier la structure de table")
        print(f"  4. Modifier seulement le traitement côté application")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == '__main__':
    check_date_columns()
