#!/usr/bin/env python3
"""
Test des boutons d'action dans la page de gestion des maintenances
"""

import requests
import sys
import sqlite3

def test_boutons_maintenance():
    """Teste les boutons d'action des maintenances"""
    print("🔧 Test des Boutons d'Action - Maintenances")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    
    try:
        # 1. Vérifier qu'il y a des maintenances
        print("1. Vérification des maintenances existantes...")
        conn = sqlite3.connect('parc_automobile.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id, type_maintenance, statut FROM maintenances LIMIT 3')
        maintenances = cursor.fetchall()
        conn.close()
        
        if not maintenances:
            print("   ⚠️ Aucune maintenance trouvée")
            return False
        
        print(f"   ✅ {len(maintenances)} maintenance(s) trouvée(s)")
        for m in maintenances:
            print(f"      ID {m[0]}: {m[1]} - {m[2]}")
        
        # 2. Test d'accès à la page de détails
        print("\n2. Test d'accès aux détails...")
        maintenance_id = maintenances[0][0]
        response = requests.get(f"{base_url}/maintenances/{maintenance_id}/voir", timeout=10)
        
        if response.status_code == 200:
            print(f"   ✅ Page détails accessible (ID {maintenance_id})")
            if "Détails de la Maintenance" in response.text:
                print("   ✅ Contenu correct")
            else:
                print("   ⚠️ Contenu inattendu")
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
        
        # 3. Test d'accès à la page de modification
        print("\n3. Test d'accès à la modification...")
        response = requests.get(f"{base_url}/maintenances/{maintenance_id}/modifier", timeout=10)
        
        if response.status_code == 200:
            print(f"   ✅ Page modification accessible (ID {maintenance_id})")
            if "Modifier la Maintenance" in response.text:
                print("   ✅ Contenu correct")
            else:
                print("   ⚠️ Contenu inattendu")
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
        
        # 4. Test des actions selon le statut
        print("\n4. Test des actions selon le statut...")
        for m in maintenances:
            maintenance_id, type_maintenance, statut = m
            print(f"   Maintenance {maintenance_id} ({type_maintenance}) - Statut: {statut}")
            
            if statut == 'planifiee':
                # Test démarrage
                print(f"      Test démarrage possible: ✅")
                print(f"      Test modification possible: ✅")
            elif statut == 'en_cours':
                # Test finalisation
                print(f"      Test finalisation possible: ✅")
                print(f"      Test modification possible: ✅")
            elif statut == 'terminee':
                print(f"      Actions limitées (maintenance terminée): ✅")
        
        # 5. Test de la liste des maintenances avec boutons
        print("\n5. Test de la page liste avec boutons...")
        response = requests.get(f"{base_url}/maintenances", timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Vérifier la présence des boutons
            if 'btn-outline-success' in content:
                print("   ✅ Boutons d'action présents")
            else:
                print("   ❌ Boutons d'action manquants")
                
            if 'fas fa-play' in content:
                print("   ✅ Bouton démarrer présent")
            else:
                print("   ⚠️ Bouton démarrer non trouvé")
                
            if 'fas fa-edit' in content:
                print("   ✅ Bouton modifier présent")
            else:
                print("   ⚠️ Bouton modifier non trouvé")
                
            if 'fas fa-eye' in content:
                print("   ✅ Bouton voir présent")
            else:
                print("   ⚠️ Bouton voir non trouvé")
                
            # Vérifier qu'il n'y a plus de disabled
            if 'disabled' in content:
                print("   ⚠️ Certains boutons sont encore désactivés")
            else:
                print("   ✅ Tous les boutons sont actifs")
        else:
            print(f"   ❌ Erreur d'accès à la liste: {response.status_code}")
        
        # 6. Test des routes d'action (simulation)
        print("\n6. Test des routes d'action...")
        
        # Test route voir
        response = requests.get(f"{base_url}/maintenances/{maintenance_id}/voir", timeout=5)
        print(f"   Route voir: {'✅' if response.status_code == 200 else '❌'} ({response.status_code})")
        
        # Test route modifier
        response = requests.get(f"{base_url}/maintenances/{maintenance_id}/modifier", timeout=5)
        print(f"   Route modifier: {'✅' if response.status_code == 200 else '❌'} ({response.status_code})")
        
        # Note: Les routes POST (démarrer/terminer) ne sont pas testées pour éviter de modifier les données
        print("   Routes POST (démarrer/terminer): Non testées (préservation des données)")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
        print("💡 Vérifiez que l'application Flask est démarrée")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🏁 Tests des boutons d'action terminés")
    return True

if __name__ == "__main__":
    success = test_boutons_maintenance()
    sys.exit(0 if success else 1)
