# 🚀 Installation Apache pour GesParc Auto

## 🎯 Objectif
Configurer Apache pour accéder à GesParc Auto via `http://localhost/gesparc`

## 📋 Prérequis
- ✅ Apache 2.4 installé dans `c:\Apache24`
- ✅ Python 3.8+ avec Flask
- ✅ GesParc Auto dans `c:\Apache24\htdocs\gesparc`

## 🔧 Installation Automatique

### Option 1 : Script Batch (Recommandé)
```batch
# Exécuter en tant qu'administrateur
configure_apache.bat
```

### Option 2 : Script PowerShell
```powershell
# Exécuter en tant qu'administrateur
powershell -ExecutionPolicy Bypass -File configure_apache.ps1
```

## 🔧 Installation Manuelle

### Étape 1 : Sauvegarder la configuration Apache
```batch
copy c:\Apache24\conf\httpd.conf c:\Apache24\conf\httpd.conf.backup
```

### Étape 2 : Activer les modules nécessaires
Ajouter ces lignes dans `c:\Apache24\conf\httpd.conf` :
```apache
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule proxy_module modules/mod_proxy.so
LoadModule proxy_http_module modules/mod_proxy_http.so
```

### Étape 3 : Inclure la configuration GesParc
Ajouter à la fin de `httpd.conf` :
```apache
# GesParc Auto Configuration
Include "c:/Apache24/htdocs/gesparc/gesparc-apache.conf"
```

### Étape 4 : Vérifier la configuration
```batch
c:\Apache24\bin\httpd.exe -t
```

### Étape 5 : Redémarrer Apache
```batch
net stop Apache2.4
net start Apache2.4
```

## 🚀 Démarrage de l'Application

### 1. Démarrer Flask
```batch
cd c:\Apache24\htdocs\gesparc
python gesparc_app.py
```

### 2. Tester l'accès
- **Via Apache** : `http://localhost/gesparc`
- **Direct Flask** : `http://localhost:5001`

## ✅ Validation

### Script de Test
```batch
python test_apache_config.py
```

### Tests Manuels
1. **Page d'accueil** : `http://localhost/gesparc`
2. **Véhicules** : `http://localhost/gesparc/vehicules`
3. **Conducteurs** : `http://localhost/gesparc/conducteurs`
4. **Exports** : `http://localhost/gesparc/export/vehicules/csv`

## 🔍 Dépannage

### Problème : Apache ne démarre pas
**Cause** : Erreur de configuration
**Solution** :
1. Vérifier la syntaxe : `httpd.exe -t`
2. Consulter les logs : `c:\Apache24\logs\error.log`
3. Restaurer la sauvegarde si nécessaire

### Problème : 404 sur /gesparc
**Cause** : Configuration proxy incorrecte
**Solution** :
1. Vérifier que Flask fonctionne sur le port 5001
2. Contrôler la configuration dans `gesparc-apache.conf`
3. Redémarrer Apache

### Problème : 500 Internal Server Error
**Cause** : Erreur Flask ou proxy
**Solution** :
1. Vérifier les logs Apache
2. Tester l'accès direct Flask
3. Contrôler les permissions

### Problème : Fichiers statiques non chargés
**Cause** : Configuration Alias incorrecte
**Solution** :
1. Vérifier le chemin dans `gesparc-apache.conf`
2. Contrôler les permissions du dossier `static`

## 📊 Architecture

```
Utilisateur → Apache (port 80) → Proxy → Flask (port 5001)
                ↓
            /gesparc → http://127.0.0.1:5001/
```

## 🔒 Sécurité

### Protections Activées
- ✅ Blocage des fichiers `.py`, `.db`, `.wsgi`
- ✅ Headers de sécurité
- ✅ Pas d'indexation des répertoires sensibles

### Recommandations
1. **Firewall** : Bloquer l'accès direct au port 5001
2. **HTTPS** : Configurer SSL en production
3. **Logs** : Surveiller les accès

## 📈 Performance

### Optimisations
- ✅ Cache des fichiers statiques
- ✅ Compression gzip (si activée)
- ✅ Keep-Alive pour les connexions

### Monitoring
- **Logs Apache** : `c:\Apache24\logs\access.log`
- **Logs Erreurs** : `c:\Apache24\logs\error.log`
- **Performance Flask** : Console de l'application

## 🔄 Maintenance

### Mise à Jour
1. Arrêter Flask
2. Mettre à jour les fichiers
3. Redémarrer Flask
4. Tester les fonctionnalités

### Sauvegarde
```batch
# Configuration Apache
copy c:\Apache24\conf\httpd.conf httpd.conf.backup

# Application GesParc
xcopy c:\Apache24\htdocs\gesparc backup\gesparc /E /I
```

## 📞 Support

### Logs à Consulter
1. **Apache Access** : `c:\Apache24\logs\access.log`
2. **Apache Error** : `c:\Apache24\logs\error.log`
3. **Flask Console** : Terminal où Flask est lancé

### Commandes Utiles
```batch
# Tester la configuration Apache
c:\Apache24\bin\httpd.exe -t

# Voir les modules chargés
c:\Apache24\bin\httpd.exe -M

# Redémarrer Apache
net stop Apache2.4 && net start Apache2.4

# Tester Flask directement
curl http://localhost:5001
```

## ✅ Checklist de Validation

- [ ] Apache démarre sans erreur
- [ ] Flask accessible sur port 5001
- [ ] `http://localhost/gesparc` fonctionne
- [ ] Navigation entre les pages
- [ ] Exports CSV/Excel fonctionnels
- [ ] Fichiers statiques chargés
- [ ] Pas d'erreurs dans les logs

---

**Configuration testée avec :**
- Apache 2.4.x
- Python 3.8+
- Flask 2.3+
- Windows 10/11/Server
