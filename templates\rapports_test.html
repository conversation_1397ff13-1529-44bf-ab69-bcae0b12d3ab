{% extends "base.html" %}

{% block title %}Test Rapports Avancés - GesParc Auto{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-chart-line text-primary"></i> 
                Test Tableau de Bord Avancé
            </h1>
        </div>
    </div>

    <!-- Test des statistiques -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ stats.total_vehicules or 0 }}</h3>
                    <p class="mb-0">Véhicules Total</p>
                    <small>{{ stats.vehicules_disponibles or 0 }} disponibles</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ "{:,.0f}"|format(stats.cout_total_maintenances or 0) }}</h3>
                    <p class="mb-0">Coût Total (MAD)</p>
                    <small>{{ "{:,.0f}"|format(stats.cout_mois_maintenances or 0) }} ce mois</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h3>{{ stats.total_maintenances or 0 }}</h3>
                    <p class="mb-0">Maintenances</p>
                    <small>{{ stats.maintenances_planifiees or 0 }} planifiées</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ stats.total_conducteurs or 0 }}</h3>
                    <p class="mb-0">Conducteurs</p>
                    <small>{{ stats.affectations_actives or 0 }} actifs</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Test des métriques -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4>{{ "{:.1f}"|format(stats.taux_disponibilite or 0) }}%</h4>
                    <small>Taux Disponibilité</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4>{{ "{:,.0f}"|format(stats.cout_moyen_vehicule or 0) }}</h4>
                    <small>Coût/Véhicule (MAD)</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4>{{ "{:,.0f}"|format(stats.kilometrage_moyen or 0) }}</h4>
                    <small>KM Moyen</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4>{{ "{:.1f}"|format(stats.age_moyen_parc or 0) }}</h4>
                    <small>Âge Moyen (ans)</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4>{{ stats.nb_alertes or 0 }}</h4>
                    <small>Alertes</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4>{{ stats.nb_predictions or 0 }}</h4>
                    <small>Prédictions</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Test des graphiques simples -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Véhicules par Statut</h5>
                </div>
                <div class="card-body">
                    {% if vehicules_par_statut %}
                    <canvas id="testStatutChart" height="200"></canvas>
                    {% else %}
                    <p class="text-muted">Aucune donnée</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Véhicules par Carburant</h5>
                </div>
                <div class="card-body">
                    {% if vehicules_par_carburant %}
                    <canvas id="testCarburantChart" height="200"></canvas>
                    {% else %}
                    <p class="text-muted">Aucune donnée</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Test des alertes -->
    {% if stats.alertes %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Alertes et Notifications</h5>
                </div>
                <div class="card-body">
                    {% for alerte in stats.alertes %}
                    <div class="alert alert-{{ alerte.niveau }} alert-dismissible">
                        <strong>{{ alerte.type|title }}:</strong> {{ alerte.message }}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Test des prédictions -->
    {% if stats.predictions %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Prédictions de Maintenance</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Véhicule</th>
                                    <th>Type</th>
                                    <th>Probabilité</th>
                                    <th>Délai Estimé</th>
                                    <th>Coût Estimé</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for prediction in stats.predictions %}
                                <tr>
                                    <td>{{ prediction.vehicule }}</td>
                                    <td>{{ prediction.type }}</td>
                                    <td>{{ "{:.1f}"|format(prediction.probabilite) }}%</td>
                                    <td>{{ prediction.delai_estime }}</td>
                                    <td>{{ "{:,.0f}"|format(prediction.cout_estime) }} MAD</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Test des données brutes -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Données de Debug</h5>
                </div>
                <div class="card-body">
                    <pre>{{ stats|pprint }}</pre>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test graphique simple
    {% if vehicules_par_statut %}
    const ctx1 = document.getElementById('testStatutChart').getContext('2d');
    new Chart(ctx1, {
        type: 'pie',
        data: {
            labels: [{% for item in vehicules_par_statut %}'{{ item.statut }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for item in vehicules_par_statut %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    {% endif %}

    {% if vehicules_par_carburant %}
    const ctx2 = document.getElementById('testCarburantChart').getContext('2d');
    new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: [{% for item in vehicules_par_carburant %}'{{ item.carburant }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for item in vehicules_par_carburant %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: ['#17a2b8', '#fd7e14', '#6f42c1']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    {% endif %}
});
</script>
{% endblock %}
