#!/usr/bin/env python3
"""
Script pour mettre à jour la table maintenances avec les nouveaux champs
"""

import sqlite3

def update_maintenance_table():
    """Met à jour la table maintenances avec les nouveaux champs"""
    conn = sqlite3.connect('parc_automobile.db')
    
    # Ajouter la colonne priorite
    try:
        conn.execute('ALTER TABLE maintenances ADD COLUMN priorite TEXT DEFAULT "normale"')
        print('✅ Colonne priorite ajoutée')
    except Exception as e:
        print('ℹ️ Colonne priorite existe déjà ou erreur:', str(e))
    
    # Ajouter la colonne date_realisation
    try:
        conn.execute('ALTER TABLE maintenances ADD COLUMN date_realisation DATE')
        print('✅ Colonne date_realisation ajoutée')
    except Exception as e:
        print('ℹ️ Colonne date_realisation existe déjà ou erreur:', str(e))
    
    # Ajouter la colonne notes_technicien
    try:
        conn.execute('ALTER TABLE maintenances ADD COLUMN notes_technicien TEXT')
        print('✅ Colonne notes_technicien ajoutée')
    except Exception as e:
        print('ℹ️ Colonne notes_technicien existe déjà ou erreur:', str(e))
    
    conn.commit()
    conn.close()
    print('✅ Base de données mise à jour avec succès')

if __name__ == '__main__':
    update_maintenance_table()
