{% extends "base.html" %}

{% block title %}Détail Affectation - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-eye"></i> Détail de l'Affectation</h1>
            <div>
                <a href="{{ url_for('affectations') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
                {% if affectation.statut != 'terminee' %}
                <a href="{{ url_for('modifier_affectation', id=affectation.id) }}" class="btn btn-primary ms-2">
                    <i class="fas fa-edit"></i> Modifier
                </a>
                {% endif %}
                {% if affectation.statut == 'active' %}
                <a href="{{ url_for('terminer_affectation', id=affectation.id) }}" class="btn btn-warning ms-2">
                    <i class="fas fa-stop"></i> Terminer l'affectation
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Informations principales -->
<div class="row">
    <div class="col-lg-8">
        <!-- Carte principale de l'affectation -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Informations de l'Affectation
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3"><i class="fas fa-car"></i> Véhicule</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>Immatriculation:</strong></td>
                                <td><span class="badge bg-primary fs-6">{{ affectation.immatriculation }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Marque/Modèle:</strong></td>
                                <td>{{ affectation.marque }} {{ affectation.modele }}</td>
                            </tr>
                            <tr>
                                <td><strong>Année:</strong></td>
                                <td>{{ affectation.annee }}</td>
                            </tr>
                            <tr>
                                <td><strong>Couleur:</strong></td>
                                <td>{{ affectation.couleur }}</td>
                            </tr>
                            <tr>
                                <td><strong>Carburant:</strong></td>
                                <td>{{ affectation.carburant }}</td>
                            </tr>
                            <tr>
                                <td><strong>Kilométrage actuel:</strong></td>
                                <td><strong>{{ '{:,}'.format(affectation.kilometrage or 0).replace(',', ' ') }} km</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Statut véhicule:</strong></td>
                                <td>
                                    {% if affectation.statut_vehicule == 'disponible' %}
                                        <span class="badge bg-success">Disponible</span>
                                    {% elif affectation.statut_vehicule == 'affecte' %}
                                        <span class="badge bg-primary">Affecté</span>
                                    {% elif affectation.statut_vehicule == 'en_maintenance' %}
                                        <span class="badge bg-warning">En maintenance</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ affectation.statut_vehicule }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3"><i class="fas fa-user"></i> Conducteur</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>Nom complet:</strong></td>
                                <td><span class="text-primary">{{ affectation.prenom }} {{ affectation.nom }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Téléphone:</strong></td>
                                <td>
                                    {% if affectation.telephone %}
                                        <a href="tel:{{ affectation.telephone }}">{{ affectation.telephone }}</a>
                                    {% else %}
                                        <span class="text-muted">Non renseigné</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>
                                    {% if affectation.email %}
                                        <a href="mailto:{{ affectation.email }}">{{ affectation.email }}</a>
                                    {% else %}
                                        <span class="text-muted">Non renseigné</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>N° Permis:</strong></td>
                                <td>{{ affectation.numero_permis or 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Statut conducteur:</strong></td>
                                <td>
                                    {% if affectation.statut_conducteur == 'actif' %}
                                        <span class="badge bg-success">Actif</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ affectation.statut_conducteur }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Détails de l'affectation -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt"></i> Détails de l'Affectation
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>Date de début:</strong></td>
                                <td>{{ affectation.date_debut | format_datetime }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date de fin:</strong></td>
                                <td>
                                    {% if affectation.date_fin %}
                                        {{ affectation.date_fin | format_datetime }}
                                    {% else %}
                                        <span class="text-muted">En cours</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Durée:</strong></td>
                                <td>
                                    {% if duree_affectation is not none %}
                                        <strong>{{ duree_affectation }} jour(s)</strong>
                                    {% else %}
                                        <span class="text-muted">Non calculable</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Statut:</strong></td>
                                <td>
                                    {% if affectation.statut == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                    {% elif affectation.statut == 'terminee' %}
                                        <span class="badge bg-secondary">Terminée</span>
                                    {% else %}
                                        <span class="badge bg-warning">{{ affectation.statut }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>Kilométrage début:</strong></td>
                                <td>
                                    {% if affectation.kilometrage_debut %}
                                        {{ '{:,}'.format(affectation.kilometrage_debut).replace(',', ' ') }} km
                                    {% else %}
                                        <span class="text-muted">Non renseigné</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Kilométrage fin:</strong></td>
                                <td>
                                    {% if affectation.kilometrage_fin %}
                                        {{ '{:,}'.format(affectation.kilometrage_fin).replace(',', ' ') }} km
                                    {% else %}
                                        <span class="text-muted">Non renseigné</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Km parcourus:</strong></td>
                                <td>
                                    {% if km_parcourus is not none %}
                                        <strong class="text-primary">{{ '{:,}'.format(km_parcourus).replace(',', ' ') }} km</strong>
                                    {% else %}
                                        <span class="text-muted">Non calculable</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Budget carburant:</strong></td>
                                <td>
                                    {% if affectation.budget_carburant %}
                                        {{ '{:,.2f}'.format(affectation.budget_carburant).replace(',', ' ') }} MAD
                                    {% else %}
                                        <span class="text-muted">Non défini</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if affectation.mission %}
                <hr>
                <h6><i class="fas fa-bullseye"></i> Mission</h6>
                <div class="alert alert-primary">
                    <strong>{{ affectation.mission }}</strong>
                </div>
                {% endif %}

                {% if affectation.destination %}
                <hr>
                <h6><i class="fas fa-map-marker-alt"></i> Destination</h6>
                <div class="alert alert-info">
                    <strong>{{ affectation.destination }}</strong>
                </div>
                {% endif %}

                {% if affectation.commentaire %}
                <hr>
                <h6><i class="fas fa-comment"></i> Commentaire initial</h6>
                <div class="alert alert-light">
                    <em>{{ affectation.commentaire }}</em>
                </div>
                {% endif %}
                
                {% if affectation.statut == 'terminee' %}
                <hr>
                <h6><i class="fas fa-flag-checkered"></i> Informations de fin d'affectation</h6>
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>Motif de fin:</strong></td>
                                <td>{{ affectation.motif_fin or 'Non spécifié' }}</td>
                            </tr>
                            <tr>
                                <td><strong>État véhicule fin:</strong></td>
                                <td>
                                    {% if affectation.etat_vehicule_fin == 'bon' %}
                                        <span class="badge bg-success">Bon état</span>
                                    {% elif affectation.etat_vehicule_fin == 'moyen' %}
                                        <span class="badge bg-warning">État moyen</span>
                                    {% elif affectation.etat_vehicule_fin == 'maintenance_requise' %}
                                        <span class="badge bg-warning">Maintenance requise</span>
                                    {% elif affectation.etat_vehicule_fin == 'reparation_requise' %}
                                        <span class="badge bg-danger">Réparation requise</span>
                                    {% else %}
                                        {{ affectation.etat_vehicule_fin or 'Non spécifié' }}
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>Carburant consommé:</strong></td>
                                <td>
                                    {% if affectation.carburant_consomme %}
                                        {{ affectation.carburant_consomme }} L
                                    {% else %}
                                        <span class="text-muted">Non renseigné</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Coût carburant:</strong></td>
                                <td>
                                    {% if affectation.cout_carburant %}
                                        {{ '{:,.2f}'.format(affectation.cout_carburant).replace(',', ' ') }} MAD
                                    {% else %}
                                        <span class="text-muted">Non renseigné</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if affectation.commentaire_fin %}
                <h6><i class="fas fa-comment-dots"></i> Commentaires de fin</h6>
                <div class="alert alert-light">
                    <em>{{ affectation.commentaire_fin }}</em>
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sidebar avec statistiques -->
    <div class="col-lg-4">
        <!-- Statistiques rapides -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i> Statistiques
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h4 class="text-primary mb-1">
                                {% if duree_affectation is not none %}
                                    {{ duree_affectation }}
                                {% else %}
                                    -
                                {% endif %}
                            </h4>
                            <small class="text-muted">Jour(s)</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h4 class="text-success mb-1">
                                {% if km_parcourus is not none %}
                                    {{ '{:,}'.format(km_parcourus).replace(',', ' ') }}
                                {% else %}
                                    -
                                {% endif %}
                            </h4>
                            <small class="text-muted">Km parcourus</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h4 class="text-warning mb-1">{{ nb_maintenances_periode }}</h4>
                            <small class="text-muted">Maintenances</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h4 class="text-danger mb-1">
                                {% if cout_maintenance_periode > 0 %}
                                    {{ '{:,.0f}'.format(cout_maintenance_periode).replace(',', ' ') }}
                                {% else %}
                                    0
                                {% endif %}
                            </h4>
                            <small class="text-muted">MAD maintenance</small>
                        </div>
                    </div>
                </div>
                
                {% if km_parcourus and duree_affectation and duree_affectation > 0 %}
                <hr>
                <div class="text-center">
                    <h6 class="text-muted">Moyenne journalière</h6>
                    <h4 class="text-info">{{ '{:.1f}'.format(km_parcourus / duree_affectation) }} km/jour</h4>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools"></i> Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if affectation.statut == 'active' %}
                    <a href="{{ url_for('terminer_affectation', id=affectation.id) }}" 
                       class="btn btn-warning">
                        <i class="fas fa-stop"></i> Terminer l'affectation
                    </a>
                    {% endif %}
                    
                    <a href="{{ url_for('voir_vehicule', id=affectation.vehicule_id) }}" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-car"></i> Voir le véhicule
                    </a>
                    
                    <a href="{{ url_for('voir_conducteur', id=affectation.conducteur_id) }}" 
                       class="btn btn-outline-info">
                        <i class="fas fa-user"></i> Voir le conducteur
                    </a>
                    
                    <a href="{{ url_for('maintenances') }}?vehicule_id={{ affectation.vehicule_id }}" 
                       class="btn btn-outline-success">
                        <i class="fas fa-wrench"></i> Maintenances du véhicule
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Historique des maintenances -->
{% if maintenances %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i> Historique des Maintenances du Véhicule
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Description</th>
                                <th>Coût (MAD)</th>
                                <th>Statut</th>
                                <th>Période</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for maintenance in maintenances %}
                            <tr>
                                <td>{{ maintenance.date_maintenance }}</td>
                                <td>{{ maintenance.type_maintenance }}</td>
                                <td>{{ maintenance.description or '-' }}</td>
                                <td>
                                    {% if maintenance.cout %}
                                        {{ '{:,.2f}'.format(maintenance.cout).replace(',', ' ') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if maintenance.statut == 'planifiee' %}
                                        <span class="badge bg-warning">Planifiée</span>
                                    {% elif maintenance.statut == 'en_cours' %}
                                        <span class="badge bg-info">En cours</span>
                                    {% elif maintenance.statut == 'terminee' %}
                                        <span class="badge bg-success">Terminée</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ maintenance.statut }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if maintenance.periode == 'pendant' %}
                                        <span class="badge bg-primary">Pendant affectation</span>
                                    {% elif maintenance.periode == 'avant' %}
                                        <span class="badge bg-secondary">Avant affectation</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">Après affectation</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
