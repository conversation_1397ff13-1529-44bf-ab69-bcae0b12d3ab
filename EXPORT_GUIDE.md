# 📊 Guide d'Export - GesParc Auto

## 🎯 Fonctionnalités d'Export Disponibles

L'application GesParc Auto propose plusieurs options d'export pour toutes les données :

### 📋 Types d'Export

#### 1. **CSV (Comma Separated Values)**
- ✅ Format universel compatible avec Excel, LibreOffice, Google Sheets
- ✅ Séparateur : point-virgule (;)
- ✅ Encodage : UTF-8
- ✅ Taille de fichier optimisée

#### 2. **Excel XLSX (Moderne)**
- ✅ Format Excel moderne (2007+)
- ✅ Formatage avancé (couleurs, styles)
- ✅ Colonnes auto-ajustées
- ✅ Support multi-feuilles

#### 3. **Excel XLS (Compatible)**
- ✅ Format Excel classique (97-2003)
- ✅ Compatible avec anciennes versions
- ✅ Formatage de base
- ✅ Taille limitée (65 536 lignes max)

### 🗂️ Données Exportables

#### 🚗 Véhicules
**Colonnes exportées :**
- Immatriculation
- Marque
- Modèle
- Année
- Couleur
- Kilométrage
- Carburant
- Statut
- Date d'acquisition
- Prix d'acquisition

**Accès :** Menu Véhicules → Bouton "Exporter"

#### 👥 Conducteurs
**Colonnes exportées :**
- Nom
- Prénom
- Numéro de permis
- Date du permis
- Téléphone
- Email
- Statut

**Accès :** Menu Conducteurs → Bouton "Exporter"

#### 🔧 Maintenances
**Colonnes exportées :**
- Véhicule (immatriculation)
- Marque
- Modèle
- Type de maintenance
- Description
- Date de maintenance
- Coût
- Garage
- Statut

**Accès :** Menu Maintenances → Bouton "Exporter"

#### 🔄 Affectations
**Colonnes exportées :**
- Véhicule (immatriculation)
- Marque
- Modèle
- Nom du conducteur
- Prénom du conducteur
- Date de début
- Date de fin
- Statut
- Commentaire

**Accès :** Menu Affectations → Bouton "Exporter"

### 📊 Export Complet

#### 🗃️ Excel Multi-feuilles (XLSX)
**Contenu :**
- **Feuille 1 :** Véhicules
- **Feuille 2 :** Conducteurs
- **Feuille 3 :** Maintenances
- **Feuille 4 :** Affectations

**Avantages :**
- ✅ Toutes les données en un seul fichier
- ✅ Navigation facile entre les feuilles
- ✅ Formatage professionnel
- ✅ Idéal pour les rapports complets

**Accès :** Menu Rapports → "Export Complet" → Excel Multi-feuilles

## 🚀 Comment Utiliser

### 📥 Export Simple

1. **Naviguer** vers la section désirée (Véhicules, Conducteurs, etc.)
2. **Cliquer** sur le bouton "Exporter" (icône de téléchargement)
3. **Choisir** le format souhaité dans le menu déroulant
4. **Le téléchargement** commence automatiquement

### 📊 Export Complet

1. **Aller** dans le menu "Rapports"
2. **Cliquer** sur "Export Complet"
3. **Sélectionner** "Excel Multi-feuilles (XLSX)"
4. **Récupérer** le fichier complet avec toutes les données

### 📁 Noms des Fichiers

Les fichiers exportés suivent cette convention :
```
[type]_[AAAAMMJJ]_[HHMMSS].[extension]

Exemples :
- vehicules_20250713_143022.csv
- conducteurs_20250713_143045.xlsx
- gesparc_complet_20250713_143100.xlsx
```

## 🔧 Utilisation des Fichiers Exportés

### 📈 Excel / LibreOffice Calc

1. **Ouvrir** le fichier directement
2. **Les données** sont formatées et prêtes à l'emploi
3. **Créer** des graphiques et tableaux croisés dynamiques
4. **Filtrer** et trier selon vos besoins

### 📊 Google Sheets

1. **Importer** le fichier CSV ou Excel
2. **Choisir** l'encodage UTF-8 pour les CSV
3. **Sélectionner** le séparateur point-virgule (;) pour les CSV

### 🗄️ Base de Données

1. **Utiliser** les fichiers CSV pour l'import
2. **Mapper** les colonnes selon votre schéma
3. **Vérifier** les types de données

## ⚡ Conseils d'Utilisation

### 🎯 Choix du Format

- **CSV** : Pour l'import dans d'autres systèmes
- **XLSX** : Pour l'analyse et les rapports modernes
- **XLS** : Pour la compatibilité avec d'anciens systèmes
- **Multi-feuilles** : Pour les rapports complets

### 📊 Analyse des Données

#### Véhicules
- Analyser la répartition par marque/carburant
- Suivre l'évolution du kilométrage
- Calculer la dépréciation

#### Maintenances
- Identifier les coûts par véhicule
- Planifier les budgets maintenance
- Analyser les fréquences d'intervention

#### Affectations
- Suivre l'utilisation des véhicules
- Optimiser les affectations
- Analyser les durées d'utilisation

### 🔒 Sécurité

- ✅ Les exports contiennent des données sensibles
- ✅ Protéger les fichiers par mot de passe si nécessaire
- ✅ Ne pas partager les données personnelles sans autorisation
- ✅ Supprimer les fichiers temporaires après utilisation

## 🛠️ Dépannage

### ❌ Export ne fonctionne pas
1. Vérifier que l'application Flask est démarrée
2. Contrôler les permissions de téléchargement du navigateur
3. Essayer un autre format d'export

### 📁 Fichier corrompu
1. Réessayer l'export
2. Vérifier l'espace disque disponible
3. Tester avec un autre navigateur

### 🔤 Problèmes d'encodage
1. Utiliser Excel pour ouvrir les CSV
2. Spécifier l'encodage UTF-8 lors de l'import
3. Préférer le format XLSX pour éviter les problèmes

## 📞 Support

En cas de problème avec les exports :
1. Vérifier les logs de l'application Flask
2. Contrôler les messages d'erreur dans le navigateur
3. Tester avec des données réduites

---

**Version :** 1.0.0  
**Formats supportés :** CSV, XLSX, XLS  
**Encodage :** UTF-8  
**Séparateur CSV :** Point-virgule (;)
