#!/usr/bin/env python3
"""
Script de migration pour uniformiser le format des dates de fin d'affectation
Convertit les date_fin au format date simple (YYYY-MM-DD) vers le format datetime (YYYY-MM-DDTHH:MM)
"""

import sqlite3
import re
from datetime import datetime

def migrate_date_fin():
    """Migre les dates de fin vers le format datetime avec heure"""
    
    # Connexion à la base de données
    conn = sqlite3.connect('parc_automobile.db')
    cursor = conn.cursor()
    
    try:
        # Récupérer toutes les affectations avec date_fin
        cursor.execute('''
            SELECT id, date_debut, date_fin, statut 
            FROM affectations 
            WHERE date_fin IS NOT NULL AND date_fin != ''
        ''')
        
        affectations = cursor.fetchall()
        
        print(f"🔍 Trouvé {len(affectations)} affectations avec date_fin")
        print("=" * 60)
        
        updates_needed = 0
        
        for affectation in affectations:
            id_affectation, date_debut, date_fin, statut = affectation
            
            # Vérifier si date_fin est au format date simple (YYYY-MM-DD)
            if re.match(r'^\d{4}-\d{2}-\d{2}$', date_fin):
                # Date au format simple, besoin de conversion
                updates_needed += 1
                
                # Ajouter l'heure 23:59 par défaut pour les dates de fin
                nouvelle_date_fin = f"{date_fin}T23:59"
                
                print(f"📝 ID {id_affectation}: '{date_fin}' → '{nouvelle_date_fin}'")
                
                # Mettre à jour la base de données
                cursor.execute('''
                    UPDATE affectations 
                    SET date_fin = ? 
                    WHERE id = ?
                ''', (nouvelle_date_fin, id_affectation))
                
            elif 'T' in date_fin:
                # Date déjà au format datetime
                print(f"✅ ID {id_affectation}: '{date_fin}' (déjà au bon format)")
            else:
                print(f"⚠️  ID {id_affectation}: Format non reconnu '{date_fin}'")
        
        print("=" * 60)
        
        if updates_needed > 0:
            # Confirmer les changements
            print(f"💾 {updates_needed} affectations mises à jour")
            conn.commit()
            print("✅ Migration terminée avec succès!")
            
            # Vérifier les résultats
            print("\n🔍 Vérification des résultats:")
            cursor.execute('''
                SELECT id, date_debut, date_fin, statut 
                FROM affectations 
                WHERE date_fin IS NOT NULL AND date_fin != ''
                ORDER BY id
            ''')
            
            resultats = cursor.fetchall()
            for resultat in resultats:
                id_aff, debut, fin, stat = resultat
                print(f"   ID {id_aff}: Début={debut}, Fin={fin}, Statut={stat}")
                
        else:
            print("ℹ️  Aucune mise à jour nécessaire - toutes les dates sont déjà au bon format")
            
    except Exception as e:
        print(f"❌ Erreur lors de la migration: {e}")
        conn.rollback()
        
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚗 GesParc Auto - Migration des dates de fin d'affectation")
    print("=" * 60)
    migrate_date_fin()