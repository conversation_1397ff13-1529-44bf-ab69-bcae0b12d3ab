# 🌐 Accès via Apache - GesParc Auto

## 🚀 Démarrage Rapide

### Méthode 1 : Script Automatique
```bash
# Windows (Batch)
start_gesparc.bat

# Windows (PowerShell)
powershell -ExecutionPolicy Bypass -File start_gesparc.ps1
```

### Méthode 2 : Manuel
```bash
# 1. Aller dans le dossier
cd c:\Apache24\htdocs\gesparc

# 2. Lancer Flask
python gesparc_app.py
```

## 🌐 Accès à l'Application

Une fois Flask démarré, l'application est accessible via :

### 🎯 URL Principales
- **Via Apache** : `http://localhost/gesparc`
- **Direct Flask** : `http://localhost:5001`

### 📱 Pages Disponibles
- **Tableau de bord** : `http://localhost/gesparc/`
- **Véhicules** : `http://localhost/gesparc/vehicules`
- **Conducteurs** : `http://localhost/gesparc/conducteurs`
- **Maintenances** : `http://localhost/gesparc/maintenances`
- **Affectations** : `http://localhost/gesparc/affectations`
- **Rapports** : `http://localhost/gesparc/rapports`

## ⚙️ Configuration Apache

### Fichiers de Configuration
- `.htaccess` : Configuration de réécriture d'URL
- `index.php` : Proxy intelligent vers Flask
- `apache_config.conf` : Configuration Apache complète

### Fonctionnement
1. Apache reçoit la requête sur `/gesparc`
2. Le fichier `index.php` vérifie si Flask fonctionne
3. Si Flask est actif → Redirection transparente
4. Si Flask est inactif → Page d'aide avec instructions

## 🔧 Dépannage

### Flask ne démarre pas
```bash
# Vérifier Python
python --version

# Installer Flask si nécessaire
pip install Flask

# Initialiser la base de données
python init_db.py
```

### Apache ne fonctionne pas
1. Vérifier qu'Apache est démarré
2. Vérifier les permissions du dossier
3. Consulter les logs : `c:\Apache24\logs\error.log`

### Erreur 403 Forbidden
1. Vérifier les permissions du dossier `gesparc`
2. S'assurer que `.htaccess` est présent
3. Vérifier la configuration Apache

### Page blanche ou erreur PHP
1. Vérifier que PHP est activé dans Apache
2. Consulter les logs PHP
3. Tester l'accès direct : `http://localhost:5001`

## 📊 Statut de l'Application

### Vérifications Automatiques
Le fichier `index.php` effectue automatiquement :
- ✅ Test de connexion à Flask
- ✅ Redirection intelligente
- ✅ Gestion des erreurs
- ✅ Page d'aide si nécessaire

### Indicateurs Visuels
- **Flask actif** : Redirection automatique vers l'application
- **Flask inactif** : Page d'aide avec instructions de démarrage

## 🎯 Avantages de cette Configuration

### ✅ Flexibilité
- Accès via Apache (port 80) ou direct (port 5001)
- Démarrage/arrêt indépendant de Flask
- Configuration Apache standard

### ✅ Simplicité
- Un seul script pour démarrer
- Détection automatique des problèmes
- Instructions claires en cas d'erreur

### ✅ Performance
- Fichiers statiques servis par Apache
- Cache configuré pour les ressources
- Proxy intelligent pour les requêtes dynamiques

## 🔒 Sécurité

### Protections Mises en Place
- Fichiers Python non accessibles via web
- Base de données protégée
- Headers de sécurité configurés
- Accès restreint aux fichiers sensibles

## 📈 Monitoring

### Logs à Surveiller
- **Apache** : `c:\Apache24\logs\access.log`
- **Erreurs Apache** : `c:\Apache24\logs\error.log`
- **Flask** : Console où Flask est lancé

### Commandes Utiles
```bash
# Vérifier les ports utilisés
netstat -an | findstr :80
netstat -an | findstr :5001

# Tester la connectivité
curl http://localhost/gesparc
curl http://localhost:5001
```

---

**Configuration testée avec :**
- Apache 2.4
- PHP 7.4+
- Python 3.8+
- Flask 2.3+
