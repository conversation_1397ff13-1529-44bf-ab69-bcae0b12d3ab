{% extends "base.html" %}

{% block title %}Modifier Affectation - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit"></i> Modifier l'Affectation</h1>
            <div>
                <a href="{{ url_for('detail_affectation', id=affectation.id) }}" class="btn btn-info me-2">
                    <i class="fas fa-eye"></i> Voir détails
                </a>
                <a href="{{ url_for('affectations') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit"></i> Modification de l'affectation
                </h5>
            </div>
            <div class="card-body">
                <!-- Informations actuelles -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Affectation actuelle</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Véhicule:</strong><br>
                            <span class="text-primary">{{ affectation.immatriculation }}</span><br>
                            <small class="text-muted">{{ affectation.marque }} {{ affectation.modele }}</small>
                        </div>
                        <div class="col-md-4">
                            <strong>Conducteur:</strong><br>
                            <span class="text-primary">{{ affectation.prenom }} {{ affectation.nom }}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>Statut:</strong><br>
                            {% if affectation.statut == 'active' %}
                                <span class="badge bg-success">Active</span>
                            {% elif affectation.statut == 'terminee' %}
                                <span class="badge bg-secondary">Terminée</span>
                            {% else %}
                                <span class="badge bg-warning">{{ affectation.statut }}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Véhicule -->
                        <div class="col-md-6 mb-3">
                            <label for="vehicule_id" class="form-label">
                                <i class="fas fa-car"></i> Véhicule *
                            </label>
                            <select class="form-select" id="vehicule_id" name="vehicule_id" required>
                                {% for vehicule in vehicules %}
                                <option value="{{ vehicule.id }}" 
                                        {% if vehicule.id == affectation.vehicule_id %}selected{% endif %}>
                                    {{ vehicule.immatriculation }} - {{ vehicule.marque }} {{ vehicule.modele }}
                                    {% if vehicule.statut != 'disponible' and vehicule.id != affectation.vehicule_id %}
                                        ({{ vehicule.statut }})
                                    {% endif %}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner un véhicule
                            </div>
                        </div>

                        <!-- Conducteur -->
                        <div class="col-md-6 mb-3">
                            <label for="conducteur_id" class="form-label">
                                <i class="fas fa-user"></i> Conducteur *
                            </label>
                            <select class="form-select" id="conducteur_id" name="conducteur_id" required>
                                {% for conducteur in conducteurs %}
                                <option value="{{ conducteur.id }}" 
                                        {% if conducteur.id == affectation.conducteur_id %}selected{% endif %}>
                                    {{ conducteur.prenom }} {{ conducteur.nom }}
                                    {% if conducteur.telephone %}
                                        - {{ conducteur.telephone }}
                                    {% endif %}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner un conducteur
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Date de début -->
                        <div class="col-md-6 mb-3">
                            <label for="date_debut" class="form-label">
                                <i class="fas fa-calendar-plus"></i> Date et heure de début *
                            </label>
                            <input type="datetime-local" class="form-control" id="date_debut" 
                                   name="date_debut" required
                                   value="{{ affectation.date_debut }}">
                            <div class="invalid-feedback">
                                Veuillez saisir la date de début
                            </div>
                        </div>

                        <!-- Date de fin -->
                        <div class="col-md-6 mb-3">
                            <label for="date_fin" class="form-label">
                                <i class="fas fa-calendar-minus"></i> Date et heure de fin
                            </label>
                            <input type="datetime-local" class="form-control" id="date_fin" 
                                   name="date_fin"
                                   value="{{ affectation.date_fin or '' }}">
                            <small class="form-text text-muted">
                                Laisser vide si l'affectation est toujours active
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Mission -->
                        <div class="col-md-6 mb-3">
                            <label for="mission" class="form-label">
                                <i class="fas fa-tasks"></i> Mission *
                            </label>
                            <input type="text" class="form-control" id="mission" 
                                   name="mission" required
                                   value="{{ affectation.mission or '' }}"
                                   placeholder="Ex: Transport personnel, Livraison, Mission commerciale...">
                            <div class="invalid-feedback">
                                Veuillez saisir la mission
                            </div>
                        </div>

                        <!-- Destination -->
                        <div class="col-md-6 mb-3">
                            <label for="destination" class="form-label">
                                <i class="fas fa-map-marker-alt"></i> Destination
                            </label>
                            <input type="text" class="form-control" id="destination" 
                                   name="destination"
                                   value="{{ affectation.destination or '' }}"
                                   placeholder="Ex: Casablanca, Rabat, Région Nord...">
                        </div>
                    </div>

                    <div class="row">
                        <!-- Kilométrage actuel -->
                        <div class="col-md-6 mb-3">
                            <label for="kilometrage_actuel" class="form-label">
                                <i class="fas fa-tachometer-alt"></i> Kilométrage au début (km)
                            </label>
                            <input type="number" class="form-control" id="kilometrage_actuel" 
                                   name="kilometrage_actuel" min="0"
                                   value="{{ affectation.kilometrage or '' }}"
                                   placeholder="Ex: 125000">
                            <small class="form-text text-muted">
                                Kilométrage du véhicule au moment de l'affectation
                            </small>
                        </div>

                        <!-- Budget carburant -->
                        <div class="col-md-6 mb-3">
                            <label for="budget_carburant" class="form-label">
                                <i class="fas fa-gas-pump"></i> Budget carburant (MAD)
                            </label>
                            <input type="number" class="form-control" id="budget_carburant" 
                                   name="budget_carburant" min="0" step="0.01"
                                   value="{{ affectation.budget_carburant or '' }}"
                                   placeholder="Ex: 2000.00">
                            <small class="form-text text-muted">
                                Budget alloué pour le carburant
                            </small>
                        </div>
                    </div>

                    <!-- Commentaire -->
                    <div class="mb-3">
                        <label for="commentaire" class="form-label">
                            <i class="fas fa-comment"></i> Commentaire
                        </label>
                        <textarea class="form-control" id="commentaire" name="commentaire" 
                                  rows="3" placeholder="Informations complémentaires...">{{ affectation.commentaire or '' }}</textarea>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer les modifications
                            </button>
                            <a href="{{ url_for('detail_affectation', id=affectation.id) }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                        </div>
                        
                        {% if affectation.statut == 'active' %}
                        <div>
                            <a href="{{ url_for('terminer_affectation', id=affectation.id) }}" 
                               class="btn btn-warning">
                                <i class="fas fa-stop"></i> Terminer l'affectation
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    });
    
    // Validation des dates
    const dateDebut = document.getElementById('date_debut');
    const dateFin = document.getElementById('date_fin');
    
    function validateDates() {
        if (dateDebut.value && dateFin.value) {
            if (new Date(dateFin.value) <= new Date(dateDebut.value)) {
                dateFin.setCustomValidity('La date de fin doit être postérieure à la date de début');
            } else {
                dateFin.setCustomValidity('');
            }
        } else {
            dateFin.setCustomValidity('');
        }
    }
    
    dateDebut.addEventListener('change', validateDates);
    dateFin.addEventListener('change', validateDates);
});
</script>
{% endblock %}