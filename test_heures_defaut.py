#!/usr/bin/env python3
"""
Test des heures par défaut dans le formulaire d'affectation
"""

import sqlite3
from datetime import date, datetime

def test_heures_defaut():
    """Test des heures par défaut 00:00 et 23:59"""
    print("🧪 Test des Heures par Défaut dans les Affectations")
    print("=" * 55)
    
    try:
        # Test de génération des valeurs par défaut
        print("🔧 Test de génération des valeurs par défaut...")
        
        # Simuler la logique du backend
        date_today = date.today()
        datetime_debut_default = date_today.strftime('%Y-%m-%dT00:00')
        datetime_fin_default = date_today.strftime('%Y-%m-%dT23:59')
        
        print(f"   Date d'aujourd'hui: {date_today}")
        print(f"   Début par défaut: {datetime_debut_default}")
        print(f"   Fin par défaut: {datetime_fin_default}")
        
        # Vérifier les formats
        if datetime_debut_default.endswith('T00:00'):
            print("   ✅ Heure de début correcte (00:00)")
        else:
            print("   ❌ Erreur heure de début")
            return False
        
        if datetime_fin_default.endswith('T23:59'):
            print("   ✅ Heure de fin correcte (23:59)")
        else:
            print("   ❌ Erreur heure de fin")
            return False
        
        # Test d'insertion avec les heures par défaut
        print(f"\n📊 Test d'insertion avec heures par défaut...")
        
        conn = sqlite3.connect('parc_automobile.db')
        conn.row_factory = sqlite3.Row
        
        # Récupérer un véhicule et conducteur pour le test
        vehicule = conn.execute('''
            SELECT * FROM vehicules 
            WHERE statut = 'disponible' 
            LIMIT 1
        ''').fetchone()
        
        conducteur = conn.execute('''
            SELECT * FROM conducteurs 
            WHERE statut = 'actif' 
            LIMIT 1
        ''').fetchone()
        
        if not vehicule or not conducteur:
            print("⚠️  Pas de véhicule/conducteur disponible pour le test")
            # Créer des données de test
            conn.execute('''
                INSERT OR IGNORE INTO vehicules (immatriculation, marque, modele, annee, statut)
                VALUES ('TEST-H-001', 'Test', 'Heures', 2024, 'disponible')
            ''')
            conn.execute('''
                INSERT OR IGNORE INTO conducteurs (nom, prenom, statut)
                VALUES ('Test', 'Heures', 'actif')
            ''')
            conn.commit()
            
            vehicule = conn.execute('''
                SELECT * FROM vehicules WHERE immatriculation = 'TEST-H-001'
            ''').fetchone()
            conducteur = conn.execute('''
                SELECT * FROM conducteurs WHERE nom = 'Test' AND prenom = 'Heures'
            ''').fetchone()
        
        # Insérer une affectation avec les heures par défaut
        mission_test = "Test affectation avec heures par défaut"
        
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO affectations (vehicule_id, conducteur_id, date_debut, date_fin, statut, mission)
            VALUES (?, ?, ?, ?, 'active', ?)
        ''', (vehicule['id'], conducteur['id'], datetime_debut_default, datetime_fin_default, mission_test))
        
        affectation_id = cursor.lastrowid
        conn.commit()
        
        print(f"   ✅ Affectation créée avec ID: {affectation_id}")
        
        # Vérifier les données stockées
        affectation = conn.execute('''
            SELECT * FROM affectations WHERE id = ?
        ''', (affectation_id,)).fetchone()
        
        print(f"   📅 Date début stockée: {affectation['date_debut']}")
        print(f"   📅 Date fin stockée: {affectation['date_fin']}")
        
        # Vérifier que les heures sont correctes
        if affectation['date_debut'].endswith('T00:00'):
            print("   ✅ Heure de début stockée correctement (00:00)")
        else:
            print(f"   ❌ Erreur heure de début stockée: {affectation['date_debut']}")
        
        if affectation['date_fin'].endswith('T23:59'):
            print("   ✅ Heure de fin stockée correctement (23:59)")
        else:
            print(f"   ❌ Erreur heure de fin stockée: {affectation['date_fin']}")
        
        # Test de validation de l'ordre des dates
        print(f"\n✅ Test de validation de l'ordre...")
        
        debut_dt = datetime.strptime(affectation['date_debut'], '%Y-%m-%dT%H:%M')
        fin_dt = datetime.strptime(affectation['date_fin'], '%Y-%m-%dT%H:%M')
        
        if debut_dt < fin_dt:
            print("   ✅ Ordre des dates correct (début < fin)")
            duree = fin_dt - debut_dt
            print(f"   📊 Durée de l'affectation: {duree}")
        else:
            print("   ❌ Erreur: Date de fin antérieure ou égale à la date de début")
        
        # Test de formatage pour affichage
        print(f"\n🎨 Test de formatage pour affichage...")
        
        # Simuler le filtre format_datetime
        def format_datetime_filter(date_str):
            if not date_str:
                return '-'
            
            try:
                if 'T' in date_str:
                    dt = datetime.strptime(date_str, '%Y-%m-%dT%H:%M')
                    return dt.strftime('%d/%m/%Y à %H:%M')
                else:
                    return date_str
            except (ValueError, TypeError):
                return date_str
        
        debut_formate = format_datetime_filter(affectation['date_debut'])
        fin_formatee = format_datetime_filter(affectation['date_fin'])
        
        print(f"   📅 Début formaté: {debut_formate}")
        print(f"   📅 Fin formatée: {fin_formatee}")
        
        # Vérifier que les heures sont bien affichées
        if '00:00' in debut_formate:
            print("   ✅ Heure de début affichée (00:00)")
        if '23:59' in fin_formatee:
            print("   ✅ Heure de fin affichée (23:59)")
        
        # Test de différents scénarios d'heures
        print(f"\n🔍 Test de différents scénarios...")
        
        scenarios = [
            ('2024-07-25T00:00', '2024-07-25T23:59', 'Même jour complet'),
            ('2024-07-25T08:00', '2024-07-25T17:00', 'Journée de travail'),
            ('2024-07-25T00:00', '2024-07-26T23:59', 'Deux jours complets'),
            ('2024-07-25T14:30', '2024-07-25T16:45', 'Période courte')
        ]
        
        for debut, fin, description in scenarios:
            debut_dt = datetime.strptime(debut, '%Y-%m-%dT%H:%M')
            fin_dt = datetime.strptime(fin, '%Y-%m-%dT%H:%M')
            duree = fin_dt - debut_dt
            
            debut_affiche = format_datetime_filter(debut)
            fin_affichee = format_datetime_filter(fin)
            
            print(f"   📊 {description}:")
            print(f"      Du {debut_affiche} au {fin_affichee}")
            print(f"      Durée: {duree}")
        
        # Nettoyage
        print(f"\n🧹 Nettoyage des données de test...")
        conn.execute('DELETE FROM affectations WHERE id = ?', (affectation_id,))
        
        if vehicule['immatriculation'] == 'TEST-H-001':
            conn.execute('DELETE FROM vehicules WHERE id = ?', (vehicule['id'],))
        
        if conducteur['nom'] == 'Test' and conducteur['prenom'] == 'Heures':
            conn.execute('DELETE FROM conducteurs WHERE id = ?', (conducteur['id'],))
        
        conn.commit()
        conn.close()
        print("✅ Données de test nettoyées")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_interface_heures_defaut():
    """Test de l'interface avec heures par défaut"""
    print(f"\n🎨 Test de l'Interface avec Heures par Défaut:")
    print("-" * 45)
    
    try:
        # Vérifier les modifications dans le template
        with open('templates/ajouter_affectation.html', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        checks = [
            ('datetime_debut_default', 'Variable début par défaut utilisée'),
            ('datetime_fin_default', 'Variable fin par défaut utilisée'),
            ('value="{{ datetime_debut_default }}"', 'Valeur par défaut début'),
            ('value="{{ datetime_fin_default }}"', 'Valeur par défaut fin'),
            ('validateDateOrder', 'Fonction de validation des dates'),
            ('T23:59', 'Heure 23:59 dans le JavaScript')
        ]
        
        for check, description in checks:
            if check in contenu:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description} - manquant")
        
        # Vérifier les modifications dans le backend
        with open('gesparc_app.py', 'r', encoding='utf-8') as f:
            contenu_backend = f.read()
        
        backend_checks = [
            ('datetime_debut_default', 'Variable début générée'),
            ('datetime_fin_default', 'Variable fin générée'),
            ('T00:00', 'Heure 00:00 générée'),
            ('T23:59', 'Heure 23:59 générée'),
            ('strftime(\'%Y-%m-%dT00:00\')', 'Format début correct'),
            ('strftime(\'%Y-%m-%dT23:59\')', 'Format fin correct')
        ]
        
        for check, description in backend_checks:
            if check in contenu_backend:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description} - manquant")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur test interface: {e}")
        return False

def test_validation_javascript():
    """Test de la validation JavaScript"""
    print(f"\n🔧 Test de la Validation JavaScript:")
    print("-" * 35)
    
    try:
        # Simuler la validation JavaScript
        def validate_date_order(debut_str, fin_str):
            """Simuler la validation JavaScript"""
            if not debut_str or not fin_str:
                return True
            
            try:
                debut = datetime.strptime(debut_str, '%Y-%m-%dT%H:%M')
                fin = datetime.strptime(fin_str, '%Y-%m-%dT%H:%M')
                return debut < fin
            except:
                return False
        
        # Scénarios de test
        scenarios = [
            ('2024-07-25T00:00', '2024-07-25T23:59', True, 'Même jour complet'),
            ('2024-07-25T08:00', '2024-07-25T17:00', True, 'Journée de travail'),
            ('2024-07-25T23:59', '2024-07-25T00:00', False, 'Ordre inversé'),
            ('2024-07-25T12:00', '2024-07-25T12:00', False, 'Même heure'),
            ('2024-07-25T00:00', '2024-07-26T23:59', True, 'Deux jours'),
            ('', '2024-07-25T23:59', True, 'Début vide'),
            ('2024-07-25T00:00', '', True, 'Fin vide')
        ]
        
        for debut, fin, attendu, description in scenarios:
            resultat = validate_date_order(debut, fin)
            status = "✅ PASS" if resultat == attendu else "❌ FAIL"
            print(f"  {status} {description}: {debut} → {fin} = {resultat}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur test validation: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Test des heures par défaut dans les affectations...")
    
    # Tests
    db_success = test_heures_defaut()
    interface_success = test_interface_heures_defaut()
    validation_success = test_validation_javascript()
    
    # Résultat final
    print(f"\n" + "="*55)
    if db_success and interface_success and validation_success:
        print("🎉 HEURES PAR DÉFAUT IMPLÉMENTÉES AVEC SUCCÈS!")
        print("✅ Heure de début par défaut: 00:00")
        print("✅ Heure de fin par défaut: 23:59")
        print("✅ Validation JavaScript opérationnelle")
        print("✅ Interface mise à jour correctement")
        print("📅 Les affectations utilisent maintenant les bonnes heures par défaut")
    else:
        print("⚠️  PROBLÈME DÉTECTÉ")
        print("🔧 Vérifiez les erreurs ci-dessus")
