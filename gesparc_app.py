#!/usr/bin/env python3
"""
Application Flask pour la gestion de parc automobile - GesParc Auto
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_from_directory
import sqlite3
import os
import sys
from datetime import datetime
from export_utils import export_to_csv, export_to_excel_xlsx, export_to_excel_xls, get_export_filename
from config import get_config
from immatriculation_maroc import ImmatriculationMaroc
from matplotlib_analytics import GesparcAnalytics

# Créer l'application Flask
gesparc_app = Flask(__name__)

# Charger la configuration appropriée
config_class = get_config()
gesparc_app.config.from_object(config_class)

# Configuration pour Apache avec préfixe /gesparc
GESPARC_PREFIX = '/gesparc'

# Détecter si on est dans un contexte Apache ou si on veut forcer le préfixe
USE_PREFIX = (
    os.environ.get('SCRIPT_NAME') == GESPARC_PREFIX or
    os.environ.get('GESPARC_USE_PREFIX', '').lower() == 'true' or
    '--prefix' in sys.argv
)

if USE_PREFIX:
    gesparc_app.config['APPLICATION_ROOT'] = GESPARC_PREFIX
    print(f"🌐 Configuration Flask avec préfixe: {GESPARC_PREFIX}")

# Initialisation spécifique pour Apache si nécessaire
if hasattr(config_class, 'init_app'):
    config_class.init_app(gesparc_app)

# Middleware pour gérer le préfixe /gesparc
class PrefixMiddleware(object):
    def __init__(self, app, prefix=''):
        self.app = app
        self.prefix = prefix

    def __call__(self, environ, start_response):
        if self.prefix and environ['PATH_INFO'].startswith(self.prefix):
            environ['PATH_INFO'] = environ['PATH_INFO'][len(self.prefix):]
            environ['SCRIPT_NAME'] = self.prefix
            return self.app(environ, start_response)
        elif self.prefix:
            # Si on a un préfixe mais que l'URL ne commence pas par le préfixe
            start_response('404', [('Content-Type', 'text/plain')])
            return [b"Cette URL n'appartient pas a l'application."]
        else:
            return self.app(environ, start_response)

# Appliquer le middleware si nécessaire
if USE_PREFIX:
    gesparc_app.wsgi_app = PrefixMiddleware(gesparc_app.wsgi_app, prefix=GESPARC_PREFIX)

# Fonction helper pour les URLs avec préfixe
def url_for_prefix(endpoint, **values):
    """Génère une URL en tenant compte du préfixe /gesparc"""
    if USE_PREFIX:
        return GESPARC_PREFIX + url_for(endpoint, **values)
    else:
        return url_for(endpoint, **values)

# Rendre la fonction disponible dans les templates
@gesparc_app.context_processor
def inject_url_helpers():
    return dict(
        url_for_prefix=url_for_prefix,
        USE_PREFIX=USE_PREFIX,
        GESPARC_PREFIX=GESPARC_PREFIX if USE_PREFIX else ''
    )

    def __call__(self, environ, start_response):
        if environ['PATH_INFO'].startswith(self.prefix):
            environ['PATH_INFO'] = environ['PATH_INFO'][len(self.prefix):]
            environ['SCRIPT_NAME'] = self.prefix
            return self.app(environ, start_response)
        else:
            start_response('404', [('Content-Type', 'text/plain')])
            return ["This url does not belong to the app.".encode()]

# Appliquer le middleware si on est sous Apache
if os.environ.get('SCRIPT_NAME') or gesparc_app.config.get('APPLICATION_ROOT', '/') != '/':
    prefix = gesparc_app.config.get('APPLICATION_ROOT', '/gesparc')
    gesparc_app.wsgi_app = PrefixMiddleware(gesparc_app.wsgi_app, prefix=prefix)

# Rendre les fonctions utilitaires disponibles dans les templates
@gesparc_app.template_filter('format_prix')
def format_prix_filter(prix):
    """Filtre Jinja2 pour formater les prix"""
    return format_prix(prix)

@gesparc_app.template_filter('format_datetime')
def format_datetime_filter(date_str):
    """Filtre Jinja2 pour formater les dates avec l'heure"""
    if not date_str:
        return '-'

    try:
        # Essayer de parser différents formats
        if 'T' in date_str:
            # Format datetime-local (YYYY-MM-DDTHH:MM)
            dt = datetime.strptime(date_str, '%Y-%m-%dT%H:%M')
            return dt.strftime('%d/%m/%Y à %H:%M')
        elif ' ' in date_str:
            # Format avec espace (YYYY-MM-DD HH:MM:SS)
            if len(date_str) > 16:  # Avec secondes
                dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
            else:  # Sans secondes
                dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M')
            return dt.strftime('%d/%m/%Y à %H:%M')
        else:
            # Format date seulement (YYYY-MM-DD)
            dt = datetime.strptime(date_str, '%Y-%m-%d')
            return dt.strftime('%d/%m/%Y')
    except (ValueError, TypeError):
        # Si le parsing échoue, retourner la valeur originale
        return date_str

@gesparc_app.template_filter('format_date')
def format_date_filter(date_str):
    """Filtre Jinja2 pour formater les dates seulement"""
    if not date_str:
        return '-'

    try:
        # Extraire seulement la partie date
        if 'T' in date_str:
            date_part = date_str.split('T')[0]
        elif ' ' in date_str:
            date_part = date_str.split(' ')[0]
        else:
            date_part = date_str

        dt = datetime.strptime(date_part, '%Y-%m-%d')
        return dt.strftime('%d/%m/%Y')
    except (ValueError, TypeError):
        return date_str

# Configuration de la base de données
DATABASE = gesparc_app.config.get('DATABASE', 'parc_automobile.db')

# Configuration de la devise
DEVISE = 'MAD'  # Dirham Marocain

def format_prix(prix):
    """Formate un prix avec la devise MAD"""
    if prix is None:
        return '-'
    return f"{prix:,.2f}".replace(',', ' ') + f" {DEVISE}"

def get_db_connection():
    """Établit une connexion à la base de données SQLite"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@gesparc_app.route('/')
def index():
    """Page d'accueil avec tableau de bord"""
    
    # Récupérer les statistiques
    stats = {
        'total_vehicules': 0,
        'vehicules_disponibles': 0,
        'total_conducteurs': 0,
        'maintenances_prevues': 0
    }
    
    maintenances_prochaines = []
    
    try:
        conn = get_db_connection()
        
        # Statistiques véhicules
        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]
        stats['vehicules_disponibles'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'").fetchone()[0]
        
        # Statistiques conducteurs
        stats['total_conducteurs'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]
        
        # Maintenances prévues
        stats['maintenances_prevues'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'planifiee'").fetchone()[0]
        
        # Maintenances à venir (prochains 30 jours)
        maintenances_prochaines = conn.execute('''
            SELECT m.*, v.immatriculation 
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            WHERE m.statut = 'planifiee' 
            AND date(m.date_maintenance) BETWEEN date('now') AND date('now', '+30 days')
            ORDER BY m.date_maintenance
            LIMIT 5
        ''').fetchall()
        
        conn.close()
        
    except Exception as e:
        print(f"Erreur lors de la récupération des statistiques: {e}")
    
    return render_template('index.html', stats=stats, maintenances_prochaines=maintenances_prochaines)

# Routes pour la gestion des véhicules
@gesparc_app.route('/vehicules')
def vehicules():
    """Liste des véhicules"""
    try:
        conn = get_db_connection()
        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()

        # Statistiques
        stats = {}
        stats['total'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]

        statuts = conn.execute('''
            SELECT statut, COUNT(*) as count
            FROM vehicules
            GROUP BY statut
        ''').fetchall()

        for statut in statuts:
            stats[statut['statut']] = statut['count']

        conn.close()

        return render_template('vehicules.html', vehicules=vehicules, stats=stats)

    except Exception as e:
        flash(f'Erreur lors de la récupération des véhicules: {e}', 'error')
        return render_template('vehicules.html', vehicules=[], stats={})

@gesparc_app.route('/vehicules/ajouter', methods=['GET', 'POST'])
def ajouter_vehicule():
    """Ajouter un nouveau véhicule"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            immatriculation_brute = request.form['immatriculation'].strip()
            marque = request.form['marque']
            modele = request.form['modele']
            annee = int(request.form['annee'])
            couleur = request.form.get('couleur', '')
            kilometrage = int(request.form.get('kilometrage', 0))
            carburant = request.form['carburant']
            statut = request.form.get('statut', 'disponible')
            date_acquisition = request.form.get('date_acquisition', '')

            # Validation de l'immatriculation marocaine
            validation_immat = ImmatriculationMaroc.valider(immatriculation_brute)
            if not validation_immat['valide']:
                flash(f'Immatriculation invalide: {validation_immat["message"]}', 'error')
                return render_template('ajouter_vehicule.html')

            # Utiliser l'immatriculation formatée
            immatriculation = validation_immat['immatriculation_formatee']

            # Validation basique
            if not all([immatriculation, marque, modele, annee, carburant]):
                flash('Veuillez remplir tous les champs obligatoires', 'error')
                return render_template('ajouter_vehicule.html')

            # Vérifier si l'immatriculation existe déjà
            conn = get_db_connection()
            existing = conn.execute('SELECT id FROM vehicules WHERE immatriculation = ?', (immatriculation,)).fetchone()
            if existing:
                flash(f'Un véhicule avec l\'immatriculation {immatriculation} existe déjà', 'error')
                conn.close()
                return render_template('ajouter_vehicule.html')

            # Insérer le véhicule
            conn.execute('''
                INSERT INTO vehicules (immatriculation, marque, modele, annee, couleur,
                kilometrage, carburant, statut, date_acquisition)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (immatriculation, marque, modele, annee, couleur, kilometrage,
                  carburant, statut, date_acquisition or None))

            conn.commit()
            conn.close()

            flash(f'Véhicule {immatriculation} ajouté avec succès!', 'success')
            return redirect(url_for('vehicules'))

        except Exception as e:
            flash(f'Erreur lors de l\'ajout du véhicule: {e}', 'error')

    return render_template('ajouter_vehicule.html')

@gesparc_app.route('/vehicules/<int:id>')
def voir_vehicule(id):
    """Voir les détails d'un véhicule"""
    try:
        conn = get_db_connection()
        vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (id,)).fetchone()

        if not vehicule:
            flash('Véhicule non trouvé', 'error')
            return redirect(url_for('vehicules'))

        # Récupérer les maintenances du véhicule
        maintenances = conn.execute('''
            SELECT * FROM maintenances
            WHERE vehicule_id = ?
            ORDER BY date_maintenance DESC
        ''', (id,)).fetchall()

        # Récupérer les affectations du véhicule
        affectations = conn.execute('''
            SELECT a.*, c.nom, c.prenom
            FROM affectations a
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.vehicule_id = ?
            ORDER BY a.date_debut DESC
        ''', (id,)).fetchall()

        conn.close()

        return render_template('voir_vehicule.html', vehicule=vehicule,
                             maintenances=maintenances, affectations=affectations)

    except Exception as e:
        flash(f'Erreur lors de la récupération du véhicule: {e}', 'error')
        return redirect(url_for('vehicules'))

@gesparc_app.route('/vehicules/<int:id>/modifier', methods=['GET', 'POST'])
def modifier_vehicule(id):
    """Modifier un véhicule"""
    try:
        conn = get_db_connection()
        vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (id,)).fetchone()

        if not vehicule:
            flash('Véhicule non trouvé', 'error')
            return redirect(url_for('vehicules'))

        if request.method == 'POST':
            # Récupérer les données du formulaire
            immatriculation_brute = request.form['immatriculation'].strip()
            marque = request.form['marque']
            modele = request.form['modele']
            annee = int(request.form['annee'])
            couleur = request.form.get('couleur', '')
            kilometrage = int(request.form.get('kilometrage', 0))

            # Validation de l'immatriculation marocaine
            validation_immat = ImmatriculationMaroc.valider(immatriculation_brute)
            if not validation_immat['valide']:
                flash(f'Immatriculation invalide: {validation_immat["message"]}', 'error')
                return redirect(url_for('modifier_vehicule', id=id))

            # Utiliser l'immatriculation formatée
            immatriculation = validation_immat['immatriculation_formatee']
            carburant = request.form['carburant']
            statut = request.form.get('statut', 'disponible')
            date_acquisition = request.form.get('date_acquisition', '')

            # Vérifier si l'immatriculation existe déjà (sauf pour ce véhicule)
            existing = conn.execute('SELECT id FROM vehicules WHERE immatriculation = ? AND id != ?',
                                  (immatriculation, id)).fetchone()
            if existing:
                flash(f'Un autre véhicule avec l\'immatriculation {immatriculation} existe déjà', 'error')
                conn.close()
                return render_template('modifier_vehicule.html', vehicule=vehicule)

            # Mettre à jour le véhicule
            conn.execute('''
                UPDATE vehicules SET immatriculation=?, marque=?, modele=?, annee=?,
                couleur=?, kilometrage=?, carburant=?, statut=?, date_acquisition=?
                WHERE id=?
            ''', (immatriculation, marque, modele, annee, couleur, kilometrage,
                  carburant, statut, date_acquisition or None, id))

            conn.commit()
            conn.close()

            flash(f'Véhicule {immatriculation} modifié avec succès!', 'success')
            return redirect(url_for('voir_vehicule', id=id))

        conn.close()
        return render_template('modifier_vehicule.html', vehicule=vehicule)

    except Exception as e:
        flash(f'Erreur lors de la modification du véhicule: {e}', 'error')
        return redirect(url_for('vehicules'))

@gesparc_app.route('/vehicules/<int:id>/supprimer')
def supprimer_vehicule(id):
    """Supprimer un véhicule"""
    try:
        conn = get_db_connection()
        vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (id,)).fetchone()

        if not vehicule:
            flash('Véhicule non trouvé', 'error')
            return redirect(url_for('vehicules'))

        # Vérifier s'il y a des affectations actives
        affectations_actives = conn.execute('''
            SELECT COUNT(*) FROM affectations
            WHERE vehicule_id = ? AND statut = 'active'
        ''', (id,)).fetchone()[0]

        if affectations_actives > 0:
            flash('Impossible de supprimer ce véhicule car il a des affectations actives', 'error')
            conn.close()
            return redirect(url_for('vehicules'))

        # Supprimer le véhicule
        conn.execute('DELETE FROM vehicules WHERE id = ?', (id,))
        conn.commit()
        conn.close()

        flash(f'Véhicule {vehicule["immatriculation"]} supprimé avec succès!', 'success')
        return redirect(url_for('vehicules'))

    except Exception as e:
        flash(f'Erreur lors de la suppression du véhicule: {e}', 'error')
        return redirect(url_for('vehicules'))

# Routes pour la gestion des conducteurs
@gesparc_app.route('/conducteurs')
def conducteurs():
    """Liste des conducteurs"""
    try:
        conn = get_db_connection()

        # Récupérer les conducteurs avec leurs véhicules affectés
        conducteurs = conn.execute('''
            SELECT c.*,
                   CASE WHEN a.vehicule_id IS NOT NULL
                        THEN v.immatriculation
                        ELSE NULL
                   END as vehicule_affecte
            FROM conducteurs c
            LEFT JOIN affectations a ON c.id = a.conducteur_id AND a.statut = 'active'
            LEFT JOIN vehicules v ON a.vehicule_id = v.id
            ORDER BY c.nom, c.prenom
        ''').fetchall()

        # Statistiques
        stats = {}
        stats['total'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]

        statuts = conn.execute('''
            SELECT statut, COUNT(*) as count
            FROM conducteurs
            GROUP BY statut
        ''').fetchall()

        for statut in statuts:
            stats[statut['statut']] = statut['count']

        # Nombre de conducteurs avec véhicule affecté
        affectations_actives = conn.execute('''
            SELECT COUNT(DISTINCT conducteur_id)
            FROM affectations
            WHERE statut = 'active'
        ''').fetchone()[0]

        conn.close()

        return render_template('conducteurs.html',
                             conducteurs=conducteurs,
                             stats=stats,
                             affectations_actives=affectations_actives)

    except Exception as e:
        flash(f'Erreur lors de la récupération des conducteurs: {e}', 'error')
        return render_template('conducteurs.html', conducteurs=[], stats={}, affectations_actives=0)

@gesparc_app.route('/conducteurs/ajouter', methods=['GET', 'POST'])
def ajouter_conducteur():
    """Ajouter un nouveau conducteur"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            nom = request.form['nom'].strip()
            prenom = request.form['prenom'].strip()
            numero_permis = request.form['numero_permis'].strip()
            date_permis = request.form.get('date_permis', '')
            telephone = request.form.get('telephone', '').strip()
            email = request.form.get('email', '').strip()
            statut = request.form.get('statut', 'actif')

            # Validation
            if not all([nom, prenom, numero_permis]):
                flash('Veuillez remplir tous les champs obligatoires', 'error')
                return render_template('ajouter_conducteur.html')

            # Vérifier si le numéro de permis existe déjà
            conn = get_db_connection()
            existing = conn.execute('SELECT id FROM conducteurs WHERE numero_permis = ?', (numero_permis,)).fetchone()
            if existing:
                flash(f'Un conducteur avec le numéro de permis {numero_permis} existe déjà', 'error')
                conn.close()
                return render_template('ajouter_conducteur.html')

            # Insérer le conducteur
            conn.execute('''
                INSERT INTO conducteurs (nom, prenom, numero_permis, date_permis,
                telephone, email, statut)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (nom, prenom, numero_permis, date_permis or None,
                  telephone or None, email or None, statut))

            conn.commit()
            conn.close()

            flash(f'Conducteur {prenom} {nom} ajouté avec succès!', 'success')
            return redirect(url_for('conducteurs'))

        except Exception as e:
            flash(f'Erreur lors de l\'ajout du conducteur: {e}', 'error')

    return render_template('ajouter_conducteur.html')

@gesparc_app.route('/conducteurs/<int:id>')
def voir_conducteur(id):
    """Voir les détails d'un conducteur"""
    try:
        conn = get_db_connection()
        conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (id,)).fetchone()

        if not conducteur:
            flash('Conducteur non trouvé', 'error')
            return redirect(url_for('conducteurs'))

        # Récupérer les affectations du conducteur
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            WHERE a.conducteur_id = ?
            ORDER BY a.date_debut DESC
        ''', (id,)).fetchall()

        conn.close()

        return render_template('voir_conducteur.html', conducteur=conducteur, affectations=affectations)

    except Exception as e:
        flash(f'Erreur lors de la récupération du conducteur: {e}', 'error')
        return redirect(url_for('conducteurs'))

@gesparc_app.route('/conducteurs/<int:id>/modifier', methods=['GET', 'POST'])
def modifier_conducteur(id):
    """Modifier un conducteur"""
    try:
        conn = get_db_connection()
        conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (id,)).fetchone()

        if not conducteur:
            flash('Conducteur non trouvé', 'error')
            return redirect(url_for('conducteurs'))

        if request.method == 'POST':
            # Récupérer les données du formulaire
            nom = request.form['nom'].strip()
            prenom = request.form['prenom'].strip()
            numero_permis = request.form['numero_permis'].strip()
            date_permis = request.form.get('date_permis', '')
            telephone = request.form.get('telephone', '').strip()
            email = request.form.get('email', '').strip()
            statut = request.form.get('statut', 'actif')

            # Vérifier si le numéro de permis existe déjà (sauf pour ce conducteur)
            existing = conn.execute('SELECT id FROM conducteurs WHERE numero_permis = ? AND id != ?',
                                  (numero_permis, id)).fetchone()
            if existing:
                flash(f'Un autre conducteur avec le numéro de permis {numero_permis} existe déjà', 'error')
                conn.close()
                return render_template('modifier_conducteur.html', conducteur=conducteur)

            # Mettre à jour le conducteur
            conn.execute('''
                UPDATE conducteurs SET nom=?, prenom=?, numero_permis=?, date_permis=?,
                telephone=?, email=?, statut=? WHERE id=?
            ''', (nom, prenom, numero_permis, date_permis or None,
                  telephone or None, email or None, statut, id))

            conn.commit()
            conn.close()

            flash(f'Conducteur {prenom} {nom} modifié avec succès!', 'success')
            return redirect(url_for('voir_conducteur', id=id))

        conn.close()
        return render_template('modifier_conducteur.html', conducteur=conducteur)

    except Exception as e:
        flash(f'Erreur lors de la modification du conducteur: {e}', 'error')
        return redirect(url_for('conducteurs'))

@gesparc_app.route('/conducteurs/<int:id>/supprimer')
def supprimer_conducteur(id):
    """Supprimer un conducteur"""
    try:
        conn = get_db_connection()
        conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (id,)).fetchone()

        if not conducteur:
            flash('Conducteur non trouvé', 'error')
            return redirect(url_for('conducteurs'))

        # Vérifier s'il y a des affectations actives
        affectations_actives = conn.execute('''
            SELECT COUNT(*) FROM affectations
            WHERE conducteur_id = ? AND statut = 'active'
        ''', (id,)).fetchone()[0]

        if affectations_actives > 0:
            flash('Impossible de supprimer ce conducteur car il a des affectations actives', 'error')
            conn.close()
            return redirect(url_for('conducteurs'))

        # Supprimer le conducteur
        conn.execute('DELETE FROM conducteurs WHERE id = ?', (id,))
        conn.commit()
        conn.close()

        flash(f'Conducteur {conducteur["prenom"]} {conducteur["nom"]} supprimé avec succès!', 'success')
        return redirect(url_for('conducteurs'))

    except Exception as e:
        flash(f'Erreur lors de la suppression du conducteur: {e}', 'error')
        return redirect(url_for('conducteurs'))

# Routes pour les maintenances
@gesparc_app.route('/maintenances')
def maintenances():
    """Liste des maintenances"""
    try:
        conn = get_db_connection()
        maintenances = conn.execute('''
            SELECT m.*, v.immatriculation, v.marque, v.modele
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        conn.close()

        return render_template('maintenances.html', maintenances=maintenances)

    except Exception as e:
        flash(f'Erreur lors de la récupération des maintenances: {e}', 'error')
        return render_template('maintenances.html', maintenances=[])

@gesparc_app.route('/maintenances/ajouter', methods=['GET', 'POST'])
def ajouter_maintenance():
    """Planifier une nouvelle maintenance"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            vehicule_id = int(request.form['vehicule_id'])
            type_maintenance = request.form['type_maintenance']
            description = request.form.get('description', '').strip()
            date_maintenance = request.form['date_maintenance']
            cout_estime = request.form.get('cout_estime', '')
            garage = request.form.get('garage', '').strip()
            priorite = request.form.get('priorite', 'normale')
            statut = request.form.get('statut', 'planifiee')
            
            # Gestion du fichier justificatif
            justificatif_filename = None
            if 'justificatif' in request.files:
                file = request.files['justificatif']
                if file and file.filename:
                    # Vérifier l'extension du fichier
                    allowed_extensions = {'pdf', 'jpg', 'jpeg', 'png', 'gif', 'doc', 'docx'}
                    file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
                    
                    if file_extension in allowed_extensions:
                        # Générer un nom de fichier unique
                        import uuid
                        from datetime import datetime
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        unique_filename = f"{timestamp}_{uuid.uuid4().hex[:8]}.{file_extension}"
                        
                        # Créer le chemin complet
                        import os
                        upload_folder = os.path.join('uploads', 'maintenances')
                        os.makedirs(upload_folder, exist_ok=True)
                        file_path = os.path.join(upload_folder, unique_filename)
                        
                        # Sauvegarder le fichier
                        file.save(file_path)
                        justificatif_filename = unique_filename
                    else:
                        flash('Format de fichier non autorisé. Formats acceptés : PDF, Images, Documents Word', 'error')
                        return redirect(url_for('ajouter_maintenance'))

            # Validation
            if not all([vehicule_id, type_maintenance, date_maintenance]):
                flash('Veuillez remplir tous les champs obligatoires', 'error')
                return redirect(url_for('ajouter_maintenance'))

            conn = get_db_connection()

            # Vérifier que le véhicule existe
            vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (vehicule_id,)).fetchone()
            if not vehicule:
                flash('Véhicule non trouvé', 'error')
                conn.close()
                return redirect(url_for('ajouter_maintenance'))

            # Insérer la maintenance
            conn.execute('''
                INSERT INTO maintenances (vehicule_id, type_maintenance, description,
                date_maintenance, cout, garage, priorite, statut, justificatif)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (vehicule_id, type_maintenance, description or None, date_maintenance,
                  float(cout_estime) if cout_estime else None, garage or None, priorite, statut, justificatif_filename))

            conn.commit()
            conn.close()

            flash(f'Maintenance {type_maintenance} planifiée avec succès pour {vehicule["immatriculation"]}!', 'success')
            return redirect(url_for('maintenances'))

        except Exception as e:
            flash(f'Erreur lors de la planification de la maintenance: {e}', 'error')
            return redirect(url_for('ajouter_maintenance'))

    # GET request - afficher le formulaire
    try:
        conn = get_db_connection()

        # Récupérer tous les véhicules
        vehicules = conn.execute('''
            SELECT * FROM vehicules
            ORDER BY immatriculation
        ''').fetchall()

        conn.close()

        from datetime import date
        date_today = date.today().isoformat()

        return render_template('planifier_maintenance.html',
                             vehicules=vehicules,
                             date_today=date_today)

    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {e}', 'error')
        return redirect(url_for('maintenances'))

@gesparc_app.route('/uploads/maintenances/<filename>')
def uploaded_file(filename):
    """Servir les fichiers justificatifs téléchargés"""
    try:
        upload_folder = os.path.join('uploads', 'maintenances')
        return send_from_directory(upload_folder, filename)
    except Exception as e:
        flash(f'Fichier non trouvé: {e}', 'error')
        return redirect(url_for('maintenances'))

@gesparc_app.route('/maintenances/<int:id>/demarrer', methods=['POST'])
def demarrer_maintenance(id):
    """Démarrer une maintenance (planifiée -> en cours)"""
    try:
        conn = get_db_connection()

        # Vérifier que la maintenance existe et est planifiée
        maintenance = conn.execute('SELECT * FROM maintenances WHERE id = ?', (id,)).fetchone()
        if not maintenance:
            flash('Maintenance non trouvée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        if maintenance['statut'] != 'planifiee':
            flash('Cette maintenance ne peut pas être démarrée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        # Mettre à jour le statut
        conn.execute('''
            UPDATE maintenances
            SET statut = 'en_cours'
            WHERE id = ?
        ''', (id,))

        conn.commit()
        conn.close()

        flash(f'Maintenance {maintenance["type_maintenance"]} démarrée avec succès!', 'success')

    except Exception as e:
        flash(f'Erreur lors du démarrage de la maintenance: {e}', 'error')

    return redirect(url_for('maintenances'))

@gesparc_app.route('/maintenances/<int:id>/terminer', methods=['POST'])
def terminer_maintenance(id):
    """Terminer une maintenance (en cours -> terminée)"""
    try:
        conn = get_db_connection()

        # Vérifier que la maintenance existe et est en cours
        maintenance = conn.execute('SELECT * FROM maintenances WHERE id = ?', (id,)).fetchone()
        if not maintenance:
            flash('Maintenance non trouvée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        if maintenance['statut'] != 'en_cours':
            flash('Cette maintenance ne peut pas être terminée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        # Mettre à jour le statut et la date de réalisation
        from datetime import date
        conn.execute('''
            UPDATE maintenances
            SET statut = 'terminee', date_realisation = ?
            WHERE id = ?
        ''', (date.today().isoformat(), id))

        conn.commit()
        conn.close()

        flash(f'Maintenance {maintenance["type_maintenance"]} terminée avec succès!', 'success')

    except Exception as e:
        flash(f'Erreur lors de la finalisation de la maintenance: {e}', 'error')

    return redirect(url_for('maintenances'))

@gesparc_app.route('/maintenances/<int:id>/voir')
def voir_maintenance(id):
    """Voir les détails d'une maintenance"""
    try:
        conn = get_db_connection()

        # Récupérer la maintenance avec les détails du véhicule
        maintenance = conn.execute('''
            SELECT m.*, v.immatriculation, v.marque, v.modele, v.annee, v.kilometrage
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            WHERE m.id = ?
        ''', (id,)).fetchone()

        if not maintenance:
            flash('Maintenance non trouvée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        conn.close()

        return render_template('voir_maintenance.html', maintenance=maintenance)

    except Exception as e:
        flash(f'Erreur lors du chargement des détails: {e}', 'error')
        return redirect(url_for('maintenances'))

@gesparc_app.route('/maintenances/<int:id>/modifier', methods=['GET', 'POST'])
def modifier_maintenance(id):
    """Modifier une maintenance"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            type_maintenance = request.form['type_maintenance']
            description = request.form.get('description', '').strip()
            date_maintenance = request.form['date_maintenance']
            cout = request.form.get('cout', '')
            garage = request.form.get('garage', '').strip()
            priorite = request.form.get('priorite', 'normale')
            statut = request.form.get('statut', 'planifiee')

            conn = get_db_connection()

            # Mettre à jour la maintenance
            conn.execute('''
                UPDATE maintenances
                SET type_maintenance = ?, description = ?, date_maintenance = ?,
                    cout = ?, garage = ?, priorite = ?, statut = ?
                WHERE id = ?
            ''', (type_maintenance, description or None, date_maintenance,
                  float(cout) if cout else None, garage or None, priorite, statut, id))

            conn.commit()
            conn.close()

            flash(f'Maintenance {type_maintenance} modifiée avec succès!', 'success')
            return redirect(url_for('maintenances'))

        except Exception as e:
            flash(f'Erreur lors de la modification: {e}', 'error')

    # GET request - afficher le formulaire de modification
    try:
        conn = get_db_connection()

        # Récupérer la maintenance
        maintenance = conn.execute('''
            SELECT m.*, v.immatriculation, v.marque, v.modele
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            WHERE m.id = ?
        ''', (id,)).fetchone()

        if not maintenance:
            flash('Maintenance non trouvée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        # Récupérer tous les véhicules pour le formulaire
        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()

        conn.close()

        return render_template('modifier_maintenance.html',
                             maintenance=maintenance,
                             vehicules=vehicules)

    except Exception as e:
        flash(f'Erreur lors du chargement: {e}', 'error')
        return redirect(url_for('maintenances'))

@gesparc_app.route('/api/valider-immatriculation', methods=['POST'])
def valider_immatriculation_api():
    """API pour valider une immatriculation en temps réel"""
    try:
        data = request.get_json()
        immatriculation = data.get('immatriculation', '').strip()

        if not immatriculation:
            return jsonify({
                'valide': False,
                'message': 'Immatriculation vide'
            })

        # Valider avec le module marocain
        validation = ImmatriculationMaroc.valider(immatriculation)

        # Vérifier l'unicité en base de données
        if validation['valide']:
            conn = get_db_connection()
            existing = conn.execute(
                'SELECT id FROM vehicules WHERE immatriculation = ?',
                (validation['immatriculation_formatee'],)
            ).fetchone()
            conn.close()

            if existing:
                validation['valide'] = False
                validation['message'] = 'Cette immatriculation existe déjà'

        return jsonify(validation)

    except Exception as e:
        return jsonify({
            'valide': False,
            'message': f'Erreur de validation: {str(e)}'
        })

@gesparc_app.route('/guide-immatriculation')
def guide_immatriculation():
    """Guide des formats d'immatriculation marocaine"""
    return render_template('guide_immatriculation.html')

# Routes pour les affectations
@gesparc_app.route('/affectations')
def affectations():
    """Liste des affectations"""
    try:
        conn = get_db_connection()
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele,
                   c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC, a.id DESC
        ''').fetchall()
        conn.close()

        return render_template('affectations.html', affectations=affectations)

    except Exception as e:
        flash(f'Erreur lors de la récupération des affectations: {e}', 'error')
        return render_template('affectations.html', affectations=[])

@gesparc_app.route('/affectations/ajouter', methods=['GET', 'POST'])
def ajouter_affectation():
    """Ajouter une nouvelle affectation"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            vehicule_id = int(request.form['vehicule_id'])
            conducteur_id = int(request.form['conducteur_id'])
            date_debut = request.form['date_debut']
            date_fin = request.form.get('date_fin', '')
            mission = request.form.get('mission', '').strip()
            destination = request.form.get('destination', '').strip()
            commentaire = request.form.get('commentaire', '').strip()
            kilometrage_actuel = request.form.get('kilometrage_actuel', '')
            budget_carburant = request.form.get('budget_carburant', '')

            # Validation
            if not all([vehicule_id, conducteur_id, date_debut, mission]):
                flash('Veuillez remplir tous les champs obligatoires (véhicule, conducteur, date de début et mission)', 'error')
                return redirect(url_for('ajouter_affectation'))

            conn = get_db_connection()

            # Vérifier que le véhicule existe et est disponible
            vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (vehicule_id,)).fetchone()
            if not vehicule:
                flash('Véhicule non trouvé', 'error')
                conn.close()
                return redirect(url_for('ajouter_affectation'))

            # Vérifier que le conducteur existe et est actif
            conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (conducteur_id,)).fetchone()
            if not conducteur:
                flash('Conducteur non trouvé', 'error')
                conn.close()
                return redirect(url_for('ajouter_affectation'))

            if conducteur['statut'] != 'actif':
                flash('Le conducteur doit être actif pour recevoir une affectation', 'error')
                conn.close()
                return redirect(url_for('ajouter_affectation'))

            # Vérifier qu'il n'y a pas d'affectation active pour ce véhicule
            affectation_active = conn.execute('''
                SELECT id FROM affectations
                WHERE vehicule_id = ? AND statut = 'active'
            ''', (vehicule_id,)).fetchone()

            if affectation_active:
                flash('Ce véhicule a déjà une affectation active', 'error')
                conn.close()
                return redirect(url_for('ajouter_affectation'))

            # Créer l'affectation
            conn.execute('''
                INSERT INTO affectations (vehicule_id, conducteur_id, date_debut,
                date_fin, statut, mission, destination, commentaire, kilometrage_debut, budget_carburant)
                VALUES (?, ?, ?, ?, 'active', ?, ?, ?, ?, ?)
            ''', (vehicule_id, conducteur_id, date_debut,
                  date_fin or None, mission, destination or None, commentaire or None,
                  int(kilometrage_actuel) if kilometrage_actuel else None,
                  float(budget_carburant) if budget_carburant else None))

            # Si un budget carburant est alloué, le soustraire du budget carburant global
            if budget_carburant and float(budget_carburant) > 0:
                montant_budget = float(budget_carburant)
                
                # Vérifier le solde actuel du budget carburant
                solde_carburant = conn.execute('''
                    SELECT COALESCE(SUM(CASE WHEN type IN ('recette', 'reapprovisionnement') THEN montant ELSE 0 END), 0) -
                           COALESCE(SUM(CASE WHEN type = 'depense' THEN montant ELSE 0 END), 0)
                    FROM budget_operations 
                    WHERE type_budget = 'carburant' AND statut = 'valide'
                ''').fetchone()[0]
                
                if solde_carburant >= montant_budget:
                    # Enregistrer la dépense dans le budget carburant
                    from datetime import datetime
                    conn.execute('''
                        INSERT INTO budget_operations (type, montant, date_operation, commentaire, categorie, reference, type_budget)
                        VALUES ('depense', ?, ?, ?, 'Carburant', ?, 'carburant')
                    ''', (montant_budget, 
                          datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                          f'Allocation carburant pour affectation - {mission}',
                          f'AFF-{vehicule_id}-{conducteur_id}'))
                    
                    flash_message = f'Affectation créée avec succès pour {conducteur["prenom"]} {conducteur["nom"]}! Budget carburant de {montant_budget:,.2f} MAD alloué et déduit du budget global.'
                else:
                    # Budget insuffisant - créer quand même l'affectation mais alerter
                    flash_message = f'Affectation créée pour {conducteur["prenom"]} {conducteur["nom"]}. ⚠️ ATTENTION: Budget carburant insuffisant (Solde: {solde_carburant:,.2f} MAD, Demandé: {montant_budget:,.2f} MAD)'
                    flash(f'Budget carburant insuffisant! Solde disponible: {solde_carburant:,.2f} MAD', 'warning')
            else:
                flash_message = f'Affectation créée avec succès pour {conducteur["prenom"]} {conducteur["nom"]}!'

            # Mettre à jour le statut du véhicule
            conn.execute('''
                UPDATE vehicules SET statut = 'affecte' WHERE id = ?
            ''', (vehicule_id,))

            conn.commit()
            conn.close()

            flash(flash_message, 'success')
            return redirect(url_for('affectations'))

        except Exception as e:
            flash(f'Erreur lors de la création de l\'affectation: {e}', 'error')

    # GET request - afficher le formulaire
    try:
        conn = get_db_connection()

        # Récupérer les véhicules disponibles
        vehicules = conn.execute('''
            SELECT * FROM vehicules
            WHERE statut = 'disponible'
            ORDER BY immatriculation
        ''').fetchall()

        # Récupérer les conducteurs actifs
        conducteurs = conn.execute('''
            SELECT * FROM conducteurs
            WHERE statut = 'actif'
            ORDER BY nom, prenom
        ''').fetchall()

        # Récupérer le solde actuel du budget carburant
        solde_budget_carburant = conn.execute('''
            SELECT COALESCE(SUM(CASE WHEN type IN ('recette', 'reapprovisionnement') THEN montant ELSE 0 END), 0) -
                   COALESCE(SUM(CASE WHEN type = 'depense' THEN montant ELSE 0 END), 0)
            FROM budget_operations 
            WHERE type_budget = 'carburant' AND statut = 'valide'
        ''').fetchone()[0]

        # Récupérer la configuration du budget carburant
        config_budget_carburant = conn.execute('''
            SELECT * FROM budget_config WHERE type_budget = 'carburant'
        ''').fetchone()

        conn.close()

        from datetime import datetime, date
        # Date et heure actuelles pour le début
        datetime_debut_default = datetime.now().strftime('%Y-%m-%dT%H:%M')
        # Date d'aujourd'hui avec heure 23:59 pour la fin
        date_today = date.today()
        datetime_fin_default = date_today.strftime('%Y-%m-%dT23:59')

        return render_template('ajouter_affectation.html',
                             vehicules=vehicules,
                             conducteurs=conducteurs,
                             datetime_debut_default=datetime_debut_default,
                             datetime_fin_default=datetime_fin_default,
                             solde_budget_carburant=solde_budget_carburant,
                             config_budget_carburant=config_budget_carburant)

    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {e}', 'error')
        return redirect(url_for('affectations'))

@gesparc_app.route('/affectations/<int:id>/ordre-mission')
def ordre_mission(id):
    """Générer l'ordre de mission pour impression"""
    try:
        conn = get_db_connection()

        # Récupérer l'affectation avec tous les détails nécessaires
        affectation = conn.execute('''
            SELECT a.*,
                   v.immatriculation, v.marque, v.modele, v.annee, v.couleur,
                   v.carburant, v.kilometrage, v.statut as statut_vehicule,
                   c.nom, c.prenom, c.telephone, c.email, c.numero_permis,
                   c.statut as statut_conducteur
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.id = ?
        ''', (id,)).fetchone()

        if not affectation:
            flash('Affectation non trouvée', 'error')
            conn.close()
            return redirect(url_for('affectations'))

        # Calculer des informations supplémentaires
        duree_affectation = None
        if affectation['date_debut'] and affectation['date_fin']:
            from datetime import datetime
            try:
                if 'T' in affectation['date_debut']:
                    debut = datetime.strptime(affectation['date_debut'], '%Y-%m-%dT%H:%M')
                else:
                    debut = datetime.strptime(affectation['date_debut'], '%Y-%m-%d')

                if 'T' in affectation['date_fin']:
                    fin = datetime.strptime(affectation['date_fin'], '%Y-%m-%dT%H:%M')
                else:
                    fin = datetime.strptime(affectation['date_fin'], '%Y-%m-%d')

                duree_affectation = fin - debut
            except ValueError:
                duree_affectation = None

        # Date et heure de génération
        from datetime import datetime
        date_generation = datetime.now()

        conn.close()

        return render_template('ordre_mission.html',
                             affectation=affectation,
                             duree_affectation=duree_affectation,
                             date_generation=date_generation)

    except Exception as e:
        flash(f'Erreur lors de la génération de l\'ordre de mission: {e}', 'error')
        return redirect(url_for('affectations'))

@gesparc_app.route('/affectations/<int:id>/detail')
def detail_affectation(id):
    """Voir les détails d'une affectation"""
    try:
        conn = get_db_connection()

        # Récupérer l'affectation avec tous les détails
        affectation = conn.execute('''
            SELECT a.*,
                   v.immatriculation, v.marque, v.modele, v.annee, v.couleur,
                   v.carburant, v.kilometrage, v.statut as statut_vehicule,
                   c.nom, c.prenom, c.telephone, c.email, c.numero_permis,
                   c.statut as statut_conducteur
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.id = ?
        ''', (id,)).fetchone()

        if not affectation:
            flash('Affectation non trouvée', 'error')
            conn.close()
            return redirect(url_for('affectations'))

        # Récupérer l'historique des maintenances du véhicule pendant l'affectation
        maintenances = []
        if affectation['date_debut']:
            if affectation['date_fin']:
                maintenances = conn.execute('''
                    SELECT m.*,
                           CASE
                               WHEN m.date_maintenance BETWEEN ? AND ? THEN 'pendant'
                               WHEN m.date_maintenance < ? THEN 'avant'
                               ELSE 'apres'
                           END as periode
                    FROM maintenances m
                    WHERE m.vehicule_id = ?
                    ORDER BY m.date_maintenance DESC
                ''', (affectation['date_debut'], affectation['date_fin'], affectation['date_debut'], affectation['vehicule_id'])).fetchall()
            else:
                maintenances = conn.execute('''
                    SELECT m.*,
                           CASE
                               WHEN m.date_maintenance BETWEEN ? AND date("now") THEN 'pendant'
                               WHEN m.date_maintenance < ? THEN 'avant'
                               ELSE 'apres'
                           END as periode
                    FROM maintenances m
                    WHERE m.vehicule_id = ?
                    ORDER BY m.date_maintenance DESC
                ''', (affectation['date_debut'], affectation['date_debut'], affectation['vehicule_id'])).fetchall()

        # Calculer des statistiques
        duree_affectation = None
        if affectation['date_debut']:
            from datetime import datetime, date
            try:
                # Fonction pour parser une date avec plusieurs formats possibles
                def parse_date_flexible(date_str):
                    if not date_str:
                        return None
                    
                    formats_possibles = [
                        '%Y-%m-%dT%H:%M:%S',  # Format avec secondes
                        '%Y-%m-%dT%H:%M',     # Format datetime-local
                        '%Y-%m-%d %H:%M:%S',  # Format avec espace et secondes
                        '%Y-%m-%d %H:%M',     # Format avec espace
                        '%Y-%m-%d',           # Format date seulement
                    ]
                    
                    for fmt in formats_possibles:
                        try:
                            return datetime.strptime(date_str, fmt).date()
                        except ValueError:
                            continue
                    
                    # Si aucun format ne fonctionne, lever une erreur
                    raise ValueError(f"Format de date non reconnu: {date_str}")
                
                debut = parse_date_flexible(affectation['date_debut'])
                fin = parse_date_flexible(affectation['date_fin']) if affectation['date_fin'] else date.today()
                
                if debut and fin:
                    duree_affectation = (fin - debut).days
                else:
                    duree_affectation = None
                    
            except ValueError as e:
                print(f"Erreur de parsing de date: {e}")
                print(f"Date début: '{affectation['date_debut']}'")
                print(f"Date fin: '{affectation['date_fin']}'")
                duree_affectation = None

        # Calculer les coûts de maintenance pendant l'affectation
        cout_maintenance_periode = 0
        nb_maintenances_periode = 0
        for maintenance in maintenances:
            if maintenance['periode'] == 'pendant' and maintenance['cout']:
                cout_maintenance_periode += maintenance['cout']
                nb_maintenances_periode += 1

        # Calculer le kilométrage parcouru
        km_parcourus = None
        if affectation['kilometrage_debut'] and affectation['kilometrage_fin']:
            km_parcourus = affectation['kilometrage_fin'] - affectation['kilometrage_debut']
        elif affectation['kilometrage_debut'] and affectation['statut'] == 'active':
            km_parcourus = affectation['kilometrage'] - affectation['kilometrage_debut']

        conn.close()

        return render_template('detail_affectation.html',
                             affectation=affectation,
                             maintenances=maintenances,
                             duree_affectation=duree_affectation,
                             cout_maintenance_periode=cout_maintenance_periode,
                             nb_maintenances_periode=nb_maintenances_periode,
                             km_parcourus=km_parcourus)

    except Exception as e:
        flash(f'Erreur lors du chargement des détails: {e}', 'error')
        return redirect(url_for('affectations'))

@gesparc_app.route('/affectations/<int:id>/modifier', methods=['GET', 'POST'])
def modifier_affectation(id):
    """Modifier une affectation"""
    try:
        conn = get_db_connection()
        
        # Récupérer l'affectation
        affectation = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele, v.statut as statut_vehicule,
                   c.nom, c.prenom, c.telephone, c.email
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.id = ?
        ''', (id,)).fetchone()
        
        if not affectation:
            flash('Affectation non trouvée', 'error')
            conn.close()
            return redirect(url_for('affectations'))
        
        # Vérifier que l'affectation n'est pas terminée
        if affectation['statut'] == 'terminee':
            flash('Impossible de modifier une affectation terminée', 'error')
            conn.close()
            return redirect(url_for('detail_affectation', id=id))
        
        if request.method == 'POST':
            # Récupérer les données du formulaire
            vehicule_id = int(request.form['vehicule_id'])
            conducteur_id = int(request.form['conducteur_id'])
            date_debut = request.form['date_debut']
            date_fin = request.form.get('date_fin', '') or None
            mission = request.form['mission'].strip()
            destination = request.form.get('destination', '').strip()
            kilometrage_actuel = request.form.get('kilometrage_actuel', '') or None
            budget_carburant = request.form.get('budget_carburant', '') or None
            commentaire = request.form.get('commentaire', '').strip()
            
            # Validation
            if not all([vehicule_id, conducteur_id, date_debut, mission]):
                flash('Veuillez remplir tous les champs obligatoires', 'error')
                return redirect(url_for('modifier_affectation', id=id))
            
            # Validation des dates
            if date_fin:
                from datetime import datetime
                try:
                    debut = datetime.fromisoformat(date_debut.replace('T', ' '))
                    fin = datetime.fromisoformat(date_fin.replace('T', ' '))
                    if fin <= debut:
                        flash('La date de fin doit être postérieure à la date de début', 'error')
                        return redirect(url_for('modifier_affectation', id=id))
                except ValueError:
                    flash('Format de date invalide', 'error')
                    return redirect(url_for('modifier_affectation', id=id))
            
            # Vérifier que le véhicule existe
            vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (vehicule_id,)).fetchone()
            if not vehicule:
                flash('Véhicule non trouvé', 'error')
                return redirect(url_for('modifier_affectation', id=id))
            
            # Vérifier que le conducteur existe
            conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (conducteur_id,)).fetchone()
            if not conducteur:
                flash('Conducteur non trouvé', 'error')
                return redirect(url_for('modifier_affectation', id=id))
            
            # Vérifier les conflits d'affectation (sauf pour l'affectation actuelle)
            conflit = conn.execute('''
                SELECT id FROM affectations 
                WHERE vehicule_id = ? AND statut = 'active' AND id != ?
            ''', (vehicule_id, id)).fetchone()
            
            if conflit:
                flash(f'Le véhicule {vehicule["immatriculation"]} est déjà affecté à un autre conducteur', 'error')
                return redirect(url_for('modifier_affectation', id=id))
            
            # Déterminer le nouveau statut
            nouveau_statut = 'terminee' if date_fin else 'active'
            
            # Mettre à jour l'affectation
            conn.execute('''
                UPDATE affectations 
                SET vehicule_id = ?, conducteur_id = ?, date_debut = ?, date_fin = ?,
                    mission = ?, destination = ?, kilometrage = ?, budget_carburant = ?,
                    commentaire = ?, statut = ?
                WHERE id = ?
            ''', (vehicule_id, conducteur_id, date_debut, date_fin, mission, destination,
                  int(kilometrage_actuel) if kilometrage_actuel else None,
                  float(budget_carburant) if budget_carburant else None,
                  commentaire, nouveau_statut, id))
            
            # Mettre à jour le statut du véhicule
            if nouveau_statut == 'active':
                conn.execute('UPDATE vehicules SET statut = ? WHERE id = ?', ('affecte', vehicule_id))
            elif nouveau_statut == 'terminee':
                # Vérifier s'il y a d'autres affectations actives pour ce véhicule
                autres_affectations = conn.execute('''
                    SELECT COUNT(*) FROM affectations 
                    WHERE vehicule_id = ? AND statut = 'active' AND id != ?
                ''', (vehicule_id, id)).fetchone()[0]
                
                if autres_affectations == 0:
                    conn.execute('UPDATE vehicules SET statut = ? WHERE id = ?', ('disponible', vehicule_id))
            
            conn.commit()
            conn.close()
            
            flash(f'Affectation modifiée avec succès!', 'success')
            return redirect(url_for('detail_affectation', id=id))
        
        # GET request - afficher le formulaire
        # Récupérer les véhicules (inclure le véhicule actuel même s'il n'est pas disponible)
        vehicules = conn.execute('''
            SELECT * FROM vehicules 
            WHERE statut = 'disponible' OR id = ?
            ORDER BY immatriculation
        ''', (affectation['vehicule_id'],)).fetchall()
        
        # Récupérer tous les conducteurs
        conducteurs = conn.execute('SELECT * FROM conducteurs ORDER BY nom, prenom').fetchall()
        
        conn.close()
        
        return render_template('edit_affectation.html',
                             affectation=affectation,
                             vehicules=vehicules,
                             conducteurs=conducteurs)
    
    except Exception as e:
        flash(f'Erreur lors de la modification: {e}', 'error')
        return redirect(url_for('affectations'))

def get_datetime_fin_default(affectation):
    """Calculer la date/heure de fin par défaut pour une affectation"""
    from datetime import datetime
    
    # Utiliser la date_fin existante si elle existe, sinon la date/heure actuelle
    if affectation and affectation['date_fin']:
        try:
            # Si la date_fin est au format ISO (avec T), on la garde telle quelle
            if 'T' in affectation['date_fin']:
                return affectation['date_fin'][:16]  # Garder YYYY-MM-DDTHH:MM
            else:
                # Si c'est juste une date, ajouter l'heure actuelle
                return f"{affectation['date_fin']}T{datetime.now().strftime('%H:%M')}"
        except:
            # En cas d'erreur, utiliser la date/heure actuelle
            pass
    
    # Pas de date_fin existante ou erreur, utiliser la date/heure actuelle
    return datetime.now().strftime('%Y-%m-%dT%H:%M')

@gesparc_app.route('/affectations/<int:id>/terminer', methods=['GET', 'POST'])
def terminer_affectation(id):
    """Terminer une affectation"""
    try:
        conn = get_db_connection()

        # Récupérer l'affectation avec les détails du véhicule
        affectation = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele, v.kilometrage, c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.id = ?
        ''', (id,)).fetchone()

        if not affectation:
            flash('Affectation non trouvée', 'error')
            conn.close()
            return redirect(url_for('affectations'))

        if affectation['statut'] != 'active':
            flash('Cette affectation n\'est pas active', 'error')
            conn.close()
            return redirect(url_for('affectations'))

        if request.method == 'POST':
            # Traitement du formulaire de fin d'affectation
            date_fin = request.form.get('date_fin', '').strip()
            kilometrage_final = request.form.get('kilometrage_final', '')
            motif_fin = request.form.get('motif_fin', '')
            etat_vehicule = request.form.get('etat_vehicule', 'bon')
            commentaire_fin = request.form.get('commentaire_fin', '')
            
            # Validation de la date de fin (obligatoire)
            if not date_fin:
                flash('La date de fin d\'affectation est obligatoire', 'error')
                conn.close()
                datetime_fin_default = get_datetime_fin_default(affectation)
                return render_template('terminer_affectation.html',
                                     affectation=affectation,
                                     datetime_fin_default=datetime_fin_default,
                                     error_date=True)

            # Validation du kilométrage final
            if kilometrage_final:
                try:
                    km_final = int(kilometrage_final)
                    km_actuel = affectation['kilometrage'] or 0

                    if km_final < km_actuel:
                        flash(f'Le kilométrage final ({km_final:,} km) doit être supérieur au kilométrage actuel ({km_actuel:,} km)', 'error')
                        conn.close()
                        datetime_fin_default = get_datetime_fin_default(affectation)
                        return render_template('terminer_affectation.html',
                                             affectation=affectation,
                                             datetime_fin_default=datetime_fin_default,
                                             error_kilometrage=True)
                except ValueError:
                    flash('Le kilométrage final doit être un nombre valide', 'error')
                    conn.close()
                    datetime_fin_default = get_datetime_fin_default(affectation)
                    return render_template('terminer_affectation.html',
                                         affectation=affectation,
                                         datetime_fin_default=datetime_fin_default,
                                         error_kilometrage=True)

            # Mettre à jour l'affectation avec tous les nouveaux champs
            conn.execute('''
                UPDATE affectations
                SET statut = 'terminee', date_fin = ?, motif_fin = ?, commentaire_fin = ?,
                    kilometrage_fin = ?, etat_vehicule_fin = ?
                WHERE id = ?
            ''', (date_fin, motif_fin, commentaire_fin,
                  int(kilometrage_final) if kilometrage_final else None,
                  etat_vehicule, id))

            # Mettre à jour le kilométrage du véhicule si fourni
            if kilometrage_final:
                conn.execute('''
                    UPDATE vehicules SET kilometrage = ? WHERE id = ?
                ''', (int(kilometrage_final), affectation['vehicule_id']))

            # Déterminer le nouveau statut du véhicule selon son état
            nouveau_statut = 'disponible'
            if etat_vehicule == 'maintenance_requise':
                nouveau_statut = 'en_maintenance'
            elif etat_vehicule == 'reparation_requise':
                nouveau_statut = 'en_maintenance'

            # Remettre le véhicule avec le bon statut
            conn.execute('''
                UPDATE vehicules SET statut = ? WHERE id = ?
            ''', (nouveau_statut, affectation['vehicule_id']))

            conn.commit()
            conn.close()

            flash(f'Affectation terminée pour {affectation["prenom"]} {affectation["nom"]} ({affectation["immatriculation"]})', 'success')
            return redirect(url_for('affectations'))

        # GET request - afficher le formulaire
        conn.close()
        datetime_fin_default = get_datetime_fin_default(affectation)

        return render_template('terminer_affectation.html',
                             affectation=affectation,
                             datetime_fin_default=datetime_fin_default)

    except Exception as e:
        flash(f'Erreur lors de la fin de l\'affectation: {e}', 'error')
        return redirect(url_for('affectations'))

# Routes pour la gestion du budget
@gesparc_app.route('/budget')
def budget():
    """Page principale de gestion des budgets - Vue d'ensemble"""
    try:
        conn = get_db_connection()
        
        # Récupérer les configurations des 3 types de budget
        configs = conn.execute('SELECT * FROM budget_config ORDER BY type_budget').fetchall()
        
        # Calculer les données pour chaque type de budget
        budgets_data = []
        for config in configs:
            type_budget = config['type_budget']
            
            # Calculer recettes et dépenses
            recettes = conn.execute('''
                SELECT COALESCE(SUM(montant), 0) FROM budget_operations 
                WHERE type IN ('recette', 'reapprovisionnement') AND statut = 'valide' AND type_budget = ?
            ''', (type_budget,)).fetchone()[0]
            
            depenses = conn.execute('''
                SELECT COALESCE(SUM(montant), 0) FROM budget_operations 
                WHERE type = 'depense' AND statut = 'valide' AND type_budget = ?
            ''', (type_budget,)).fetchone()[0]
            
            solde_actuel = recettes - depenses
            
            # Dernières opérations pour ce budget
            operations = conn.execute('''
                SELECT * FROM budget_operations 
                WHERE type_budget = ?
                ORDER BY date_operation DESC, id DESC 
                LIMIT 5
            ''', (type_budget,)).fetchall()
            
            budgets_data.append({
                'config': config,
                'recettes': recettes,
                'depenses': depenses,
                'solde_actuel': solde_actuel,
                'operations': operations,
                'alerte': solde_actuel < config['seuil_alerte']
            })
        
        # Statistiques globales
        total_recettes = sum(b['recettes'] for b in budgets_data)
        total_depenses = sum(b['depenses'] for b in budgets_data)
        total_solde = total_recettes - total_depenses
        
        conn.close()
        
        return render_template('budget_overview.html',
                             budgets_data=budgets_data,
                             total_recettes=total_recettes,
                             total_depenses=total_depenses,
                             total_solde=total_solde)
                             
    except Exception as e:
        flash(f'Erreur lors du chargement des budgets: {e}', 'error')
        return redirect(url_for('index'))

@gesparc_app.route('/budget/<type_budget>')
def budget_detail(type_budget):
    """Page détaillée d'un type de budget spécifique"""
    if type_budget not in ['carburant', 'maintenance', 'transport_terrestre']:
        flash('Type de budget invalide', 'error')
        return redirect(url_for('budget'))
    
    try:
        conn = get_db_connection()
        
        # Récupérer la configuration du budget
        config = conn.execute('SELECT * FROM budget_config WHERE type_budget = ?', (type_budget,)).fetchone()
        
        if not config:
            flash('Configuration de budget non trouvée', 'error')
            conn.close()
            return redirect(url_for('budget'))
        
        # Calculer le solde actuel
        recettes = conn.execute('''
            SELECT COALESCE(SUM(montant), 0) FROM budget_operations 
            WHERE type IN ('recette', 'reapprovisionnement') AND statut = 'valide' AND type_budget = ?
        ''', (type_budget,)).fetchone()[0]
        
        depenses = conn.execute('''
            SELECT COALESCE(SUM(montant), 0) FROM budget_operations 
            WHERE type = 'depense' AND statut = 'valide' AND type_budget = ?
        ''', (type_budget,)).fetchone()[0]
        
        solde_actuel = recettes - depenses
        
        # Récupérer les opérations pour ce budget avec informations sur les affectations
        operations = conn.execute('''
            SELECT bo.*, 
                   a.id as affectation_id,
                   a.statut as affectation_statut,
                   v.immatriculation,
                   c.prenom || ' ' || c.nom as conducteur_nom
            FROM budget_operations bo
            LEFT JOIN affectations a ON bo.reference = 'AFF-' || a.vehicule_id || '-' || a.conducteur_id
            LEFT JOIN vehicules v ON a.vehicule_id = v.id
            LEFT JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE bo.type_budget = ?
            ORDER BY bo.date_operation DESC, bo.id DESC 
            LIMIT 50
        ''', (type_budget,)).fetchall()
        
        # Statistiques par catégorie pour ce budget
        stats_categories = conn.execute('''
            SELECT categorie, type, SUM(montant) as total
            FROM budget_operations 
            WHERE statut = 'valide' AND type_budget = ?
            GROUP BY categorie, type
            ORDER BY total DESC
        ''', (type_budget,)).fetchall()
        
        conn.close()
        
        return render_template('budget_detail.html',
                             config=config,
                             type_budget=type_budget,
                             solde_actuel=solde_actuel,
                             recettes=recettes,
                             depenses=depenses,
                             operations=operations,
                             stats_categories=stats_categories)
                             
    except Exception as e:
        flash(f'Erreur lors du chargement du budget: {e}', 'error')
        return redirect(url_for('budget'))

@gesparc_app.route('/budget/<type_budget>/ajouter', methods=['GET', 'POST'])
def ajouter_operation_budget(type_budget):
    """Ajouter une nouvelle opération budgétaire pour un type de budget spécifique"""
    if type_budget not in ['carburant', 'maintenance', 'transport_terrestre']:
        flash('Type de budget invalide', 'error')
        return redirect(url_for('budget'))
    
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            type_operation = request.form['type']
            montant = float(request.form['montant'])
            date_operation = request.form['date_operation']
            commentaire = request.form.get('commentaire', '')
            categorie = request.form.get('categorie', '')
            reference = request.form.get('reference', '')
            
            # Validation
            if montant <= 0:
                flash('Le montant doit être positif', 'error')
                return redirect(url_for('ajouter_operation_budget', type_budget=type_budget))
            
            conn = get_db_connection()
            
            # Insérer la nouvelle opération
            conn.execute('''
                INSERT INTO budget_operations (type, montant, date_operation, commentaire, categorie, reference, type_budget)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (type_operation, montant, date_operation, commentaire, categorie, reference, type_budget))
            
            conn.commit()
            conn.close()
            
            flash(f'Opération {type_operation} de {montant:,.2f} MAD ajoutée avec succès!', 'success')
            return redirect(url_for('budget_detail', type_budget=type_budget))
            
        except ValueError:
            flash('Montant invalide', 'error')
        except Exception as e:
            flash(f'Erreur lors de l\'ajout: {e}', 'error')
    
    # GET request - afficher le formulaire
    try:
        conn = get_db_connection()
        config = conn.execute('SELECT * FROM budget_config WHERE type_budget = ?', (type_budget,)).fetchone()
        conn.close()
        
        if not config:
            flash('Configuration de budget non trouvée', 'error')
            return redirect(url_for('budget'))
        
        from datetime import datetime
        datetime_default = datetime.now().strftime('%Y-%m-%dT%H:%M')
        
        return render_template('ajouter_operation_budget.html',
                             datetime_default=datetime_default,
                             type_budget=type_budget,
                             config=config)
    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {e}', 'error')
        return redirect(url_for('budget'))

@gesparc_app.route('/budget/<int:id>/modifier', methods=['GET', 'POST'])
def modifier_operation_budget(id):
    """Modifier une opération budgétaire"""
    try:
        conn = get_db_connection()
        
        # Récupérer l'opération pour connaître son type de budget
        operation = conn.execute('SELECT * FROM budget_operations WHERE id = ?', (id,)).fetchone()
        
        if not operation:
            flash('Opération non trouvée', 'error')
            conn.close()
            return redirect(url_for('budget'))
        
        type_budget = operation['type_budget']
        
        if request.method == 'POST':
            # Récupérer les données du formulaire
            type_operation = request.form['type']
            montant = float(request.form['montant'])
            date_operation = request.form['date_operation']
            commentaire = request.form.get('commentaire', '')
            categorie = request.form.get('categorie', '')
            reference = request.form.get('reference', '')
            statut = request.form.get('statut', 'valide')
            
            # Validation
            if montant <= 0:
                flash('Le montant doit être positif', 'error')
                return redirect(url_for('modifier_operation_budget', id=id))
            
            # Mettre à jour l'opération
            conn.execute('''
                UPDATE budget_operations 
                SET type = ?, montant = ?, date_operation = ?, commentaire = ?, 
                    categorie = ?, reference = ?, statut = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (type_operation, montant, date_operation, commentaire, categorie, reference, statut, id))
            
            conn.commit()
            conn.close()
            
            flash(f'Opération modifiée avec succès!', 'success')
            return redirect(url_for('budget_detail', type_budget=type_budget))
        
        # GET request - afficher le formulaire
        conn.close()
        
        return render_template('modifier_operation_budget.html', operation=operation)
        
    except Exception as e:
        flash(f'Erreur lors de la modification: {e}', 'error')
        return redirect(url_for('budget'))

@gesparc_app.route('/budget/<int:id>/supprimer', methods=['POST'])
def supprimer_operation_budget(id):
    """Supprimer une opération budgétaire"""
    try:
        conn = get_db_connection()
        
        # Vérifier que l'opération existe et récupérer son type de budget
        operation = conn.execute('SELECT * FROM budget_operations WHERE id = ?', (id,)).fetchone()
        
        if not operation:
            flash('Opération non trouvée', 'error')
            conn.close()
            return redirect(url_for('budget'))
        
        type_budget = operation['type_budget']
        
        # Vérifier si l'opération est liée à une affectation active
        if operation['reference'] and operation['reference'].startswith('AFF-'):
            # Extraire les IDs du véhicule et conducteur de la référence
            try:
                parts = operation['reference'].split('-')
                if len(parts) >= 3:
                    vehicule_id = parts[1]
                    conducteur_id = parts[2]
                    
                    # Vérifier si l'affectation est encore active
                    affectation_active = conn.execute('''
                        SELECT id, statut FROM affectations 
                        WHERE vehicule_id = ? AND conducteur_id = ? AND statut = 'active'
                    ''', (vehicule_id, conducteur_id)).fetchone()
                    
                    if affectation_active:
                        flash('Impossible de supprimer cette opération : elle est liée à une affectation active. Terminez d\'abord l\'affectation.', 'error')
                        conn.close()
                        return redirect(url_for('budget_detail', type_budget=type_budget))
            except:
                pass  # Si erreur de parsing, continuer la suppression
        
        # Supprimer l'opération
        conn.execute('DELETE FROM budget_operations WHERE id = ?', (id,))
        conn.commit()
        conn.close()
        
        flash(f'Opération supprimée avec succès!', 'success')
        return redirect(url_for('budget_detail', type_budget=type_budget))
        
    except Exception as e:
        flash(f'Erreur lors de la suppression: {e}', 'error')
        return redirect(url_for('budget'))

@gesparc_app.route('/budget/<type_budget>/reapprovisionner', methods=['GET', 'POST'])
def reapprovisionner_budget(type_budget):
    """Réapprovisionner un budget spécifique"""
    if type_budget not in ['carburant', 'maintenance', 'transport_terrestre']:
        flash('Type de budget invalide', 'error')
        return redirect(url_for('budget'))
    
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            montant = float(request.form['montant'])
            date_operation = request.form['date_operation']
            commentaire = request.form.get('commentaire', 'Réapprovisionnement budget')
            reference = request.form.get('reference', '')
            
            # Validation
            if montant <= 0:
                flash('Le montant doit être positif', 'error')
                return redirect(url_for('reapprovisionner_budget', type_budget=type_budget))
            
            conn = get_db_connection()
            
            # Déterminer la catégorie selon le type de budget
            categories = {
                'carburant': 'Carburant',
                'maintenance': 'Maintenance', 
                'transport_terrestre': 'Transport'
            }
            
            # Ajouter l'opération de réapprovisionnement
            conn.execute('''
                INSERT INTO budget_operations (type, montant, date_operation, commentaire, categorie, reference, type_budget)
                VALUES ('reapprovisionnement', ?, ?, ?, ?, ?, ?)
            ''', (montant, date_operation, commentaire, categories[type_budget], reference, type_budget))
            
            conn.commit()
            conn.close()
            
            flash(f'Budget {type_budget} réapprovisionné de {montant:,.2f} MAD avec succès!', 'success')
            return redirect(url_for('budget_detail', type_budget=type_budget))
            
        except ValueError:
            flash('Montant invalide', 'error')
        except Exception as e:
            flash(f'Erreur lors du réapprovisionnement: {e}', 'error')
    
    # GET request - afficher le formulaire
    try:
        conn = get_db_connection()
        config = conn.execute('SELECT * FROM budget_config WHERE type_budget = ?', (type_budget,)).fetchone()
        conn.close()
        
        if not config:
            flash('Configuration de budget non trouvée', 'error')
            return redirect(url_for('budget'))
        
        from datetime import datetime
        datetime_default = datetime.now().strftime('%Y-%m-%dT%H:%M')
        
        return render_template('reapprovisionner_budget.html',
                             datetime_default=datetime_default,
                             type_budget=type_budget,
                             config=config)
    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {e}', 'error')
        return redirect(url_for('budget'))

@gesparc_app.route('/budget/<type_budget>/config', methods=['GET', 'POST'])
def config_budget(type_budget):
    """Configuration d'un budget spécifique"""
    if type_budget not in ['carburant', 'maintenance', 'transport_terrestre']:
        flash('Type de budget invalide', 'error')
        return redirect(url_for('budget'))
    
    try:
        conn = get_db_connection()
        
        if request.method == 'POST':
            # Récupérer les données du formulaire
            budget_initial = float(request.form['budget_initial'])
            seuil_alerte = float(request.form['seuil_alerte'])
            periode_debut = request.form.get('periode_debut', '')
            periode_fin = request.form.get('periode_fin', '')
            
            # Mettre à jour la configuration
            conn.execute('''
                UPDATE budget_config 
                SET budget_initial = ?, seuil_alerte = ?, periode_debut = ?, periode_fin = ?, 
                    updated_at = CURRENT_TIMESTAMP
                WHERE type_budget = ?
            ''', (budget_initial, seuil_alerte, periode_debut, periode_fin, type_budget))
            
            conn.commit()
            conn.close()
            
            flash(f'Configuration du budget {type_budget} mise à jour avec succès!', 'success')
            return redirect(url_for('budget_detail', type_budget=type_budget))
        
        # GET request - afficher le formulaire
        config = conn.execute('SELECT * FROM budget_config WHERE type_budget = ?', (type_budget,)).fetchone()
        
        if not config:
            flash('Configuration de budget non trouvée', 'error')
            conn.close()
            return redirect(url_for('budget'))
        
        conn.close()
        
        return render_template('config_budget.html', config=config, type_budget=type_budget)
        
    except Exception as e:
        flash(f'Erreur lors de la configuration: {e}', 'error')
        return redirect(url_for('budget'))

@gesparc_app.route('/budget/config-globale', methods=['GET', 'POST'])
def config_budget_globale():
    """Configuration globale de tous les budgets"""
    try:
        conn = get_db_connection()
        
        if request.method == 'POST':
            # Mettre à jour les 3 configurations
            for type_budget in ['carburant', 'maintenance', 'transport_terrestre']:
                budget_initial = float(request.form[f'budget_initial_{type_budget}'])
                seuil_alerte = float(request.form[f'seuil_alerte_{type_budget}'])
                periode_debut = request.form.get(f'periode_debut_{type_budget}', '')
                periode_fin = request.form.get(f'periode_fin_{type_budget}', '')
                
                conn.execute('''
                    UPDATE budget_config 
                    SET budget_initial = ?, seuil_alerte = ?, periode_debut = ?, periode_fin = ?, 
                        updated_at = CURRENT_TIMESTAMP
                    WHERE type_budget = ?
                ''', (budget_initial, seuil_alerte, periode_debut, periode_fin, type_budget))
            
            conn.commit()
            conn.close()
            
            flash('Configuration globale des budgets mise à jour avec succès!', 'success')
            return redirect(url_for('budget'))
        
        # GET request - afficher le formulaire
        configs = conn.execute('SELECT * FROM budget_config ORDER BY type_budget').fetchall()
        conn.close()
        
        return render_template('config_budget_globale.html', configs=configs)
        
    except Exception as e:
        flash(f'Erreur lors de la configuration: {e}', 'error')
        return redirect(url_for('budget'))

# Routes pour les rapports
@gesparc_app.route('/rapports')
def rapports():
    """Page des rapports et statistiques"""
    print("🔍 DEBUG: Début de la route rapports")
    try:
        print("🔍 DEBUG: Connexion à la base de données")
        conn = get_db_connection()

        # Statistiques générales
        print("🔍 DEBUG: Calcul des statistiques générales")
        stats = {}
        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]
        stats['total_conducteurs'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]
        stats['total_maintenances'] = conn.execute('SELECT COUNT(*) FROM maintenances').fetchone()[0]
        stats['total_affectations'] = conn.execute('SELECT COUNT(*) FROM affectations').fetchone()[0]
        print(f"🔍 DEBUG: Stats générales OK: {stats}")

        # Statistiques détaillées
        stats['vehicules_disponibles'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'").fetchone()[0]
        stats['vehicules_en_maintenance'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'en_maintenance'").fetchone()[0]
        stats['vehicules_affectes'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'affecte'").fetchone()[0]
        stats['maintenances_planifiees'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'planifiee'").fetchone()[0]
        stats['maintenances_en_cours'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'en_cours'").fetchone()[0]
        stats['maintenances_terminees'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'terminee'").fetchone()[0]
        stats['affectations_actives'] = conn.execute("SELECT COUNT(*) FROM affectations WHERE statut = 'active'").fetchone()[0]
        stats['conducteurs_actifs'] = conn.execute("SELECT COUNT(*) FROM conducteurs WHERE statut = 'actif'").fetchone()[0]

        # Coûts de maintenance et analyses financières
        cout_total_result = conn.execute('SELECT SUM(cout) FROM maintenances WHERE cout IS NOT NULL').fetchone()
        stats['cout_total_maintenances'] = cout_total_result[0] if cout_total_result[0] else 0

        cout_mois_result = conn.execute('''
            SELECT SUM(cout) FROM maintenances
            WHERE cout IS NOT NULL
            AND date_maintenance >= date('now', 'start of month')
        ''').fetchone()
        stats['cout_mois_maintenances'] = cout_mois_result[0] if cout_mois_result[0] else 0

        # Coût moyen par véhicule et par maintenance
        stats['cout_moyen_vehicule'] = (stats['cout_total_maintenances'] / stats['total_vehicules']) if stats['total_vehicules'] > 0 else 0
        stats['cout_moyen_maintenance'] = (stats['cout_total_maintenances'] / stats['total_maintenances']) if stats['total_maintenances'] > 0 else 0

        # Analyses de performance
        stats['taux_disponibilite'] = (stats['vehicules_disponibles'] / stats['total_vehicules'] * 100) if stats['total_vehicules'] > 0 else 0
        stats['taux_utilisation'] = (stats['vehicules_affectes'] / stats['total_vehicules'] * 100) if stats['total_vehicules'] > 0 else 0
        stats['taux_maintenance'] = (stats['vehicules_en_maintenance'] / stats['total_vehicules'] * 100) if stats['total_vehicules'] > 0 else 0

        # Kilométrage moyen
        km_moyen_result = conn.execute('SELECT AVG(kilometrage) FROM vehicules WHERE kilometrage > 0').fetchone()
        stats['kilometrage_moyen'] = km_moyen_result[0] if km_moyen_result[0] else 0

        # Âge moyen du parc
        age_moyen_result = conn.execute('SELECT AVG(2025 - annee) FROM vehicules').fetchone()
        stats['age_moyen_parc'] = age_moyen_result[0] if age_moyen_result[0] else 0

        # Répartition par statut des véhicules
        vehicules_par_statut = conn.execute('''
            SELECT statut, COUNT(*) as count
            FROM vehicules
            GROUP BY statut
            ORDER BY count DESC
        ''').fetchall()

        # Répartition par carburant
        vehicules_par_carburant = conn.execute('''
            SELECT carburant, COUNT(*) as count
            FROM vehicules
            WHERE carburant IS NOT NULL AND carburant != ''
            GROUP BY carburant
            ORDER BY count DESC
        ''').fetchall()

        # Répartition par marque
        vehicules_par_marque = conn.execute('''
            SELECT marque, COUNT(*) as count
            FROM vehicules
            WHERE marque IS NOT NULL AND marque != ''
            GROUP BY marque
            ORDER BY count DESC
            LIMIT 10
        ''').fetchall()

        # Maintenances par mois (12 derniers mois)
        maintenances_par_mois = conn.execute('''
            SELECT strftime('%Y-%m', date_maintenance) as mois,
                   COUNT(*) as count,
                   SUM(CASE WHEN cout IS NOT NULL THEN cout ELSE 0 END) as cout_total
            FROM maintenances
            WHERE date_maintenance >= date('now', '-12 months')
            GROUP BY strftime('%Y-%m', date_maintenance)
            ORDER BY mois
        ''').fetchall()

        # Maintenances par type
        maintenances_par_type = conn.execute('''
            SELECT type_maintenance, COUNT(*) as count,
                   AVG(CASE WHEN cout IS NOT NULL THEN cout ELSE 0 END) as cout_moyen
            FROM maintenances
            GROUP BY type_maintenance
            ORDER BY count DESC
        ''').fetchall()

        # Top 5 véhicules avec le plus de maintenances
        vehicules_maintenances = conn.execute('''
            SELECT v.immatriculation, v.marque, v.modele, COUNT(m.id) as nb_maintenances,
                   SUM(CASE WHEN m.cout IS NOT NULL THEN m.cout ELSE 0 END) as cout_total
            FROM vehicules v
            LEFT JOIN maintenances m ON v.id = m.vehicule_id
            GROUP BY v.id
            ORDER BY nb_maintenances DESC, cout_total DESC
            LIMIT 5
        ''').fetchall()

        # Conducteurs avec véhicules affectés
        conducteurs_actifs = conn.execute('''
            SELECT c.nom, c.prenom, v.immatriculation, v.marque, v.modele, a.date_debut
            FROM conducteurs c
            JOIN affectations a ON c.id = a.conducteur_id
            JOIN vehicules v ON a.vehicule_id = v.id
            WHERE a.statut = 'active'
            ORDER BY c.nom, c.prenom
        ''').fetchall()

        # Analyses prédictives et alertes (version simplifiée)
        try:
            alertes = []
            predictions = []

            # Alertes simples
            if stats['vehicules_en_maintenance'] > 0:
                alertes.append({
                    'type': 'maintenance_en_cours',
                    'niveau': 'info',
                    'message': f'{stats["vehicules_en_maintenance"]} véhicule(s) en maintenance',
                    'vehicule': 'Multiple'
                })

            if stats['taux_disponibilite'] < 70:
                alertes.append({
                    'type': 'disponibilite_faible',
                    'niveau': 'warning',
                    'message': f'Taux de disponibilité faible: {stats["taux_disponibilite"]:.1f}%',
                    'vehicule': 'Parc'
                })

            stats['alertes'] = alertes
            stats['predictions'] = predictions
            stats['nb_alertes'] = len(alertes)
            stats['nb_predictions'] = len(predictions)
        except Exception as e:
            print(f"Erreur alertes: {e}")
            stats['alertes'] = []
            stats['predictions'] = []
            stats['nb_alertes'] = 0
            stats['nb_predictions'] = 0

        conn.close()

        print("🔍 DEBUG: Rendu du template debug")
        return render_template('rapports_debug.html',
                             stats=stats,
                             vehicules_par_statut=vehicules_par_statut,
                             vehicules_par_carburant=vehicules_par_carburant,
                             vehicules_par_marque=vehicules_par_marque,
                             maintenances_par_mois=maintenances_par_mois,
                             maintenances_par_type=maintenances_par_type,
                             vehicules_maintenances=vehicules_maintenances,
                             conducteurs_actifs=conducteurs_actifs)

    except Exception as e:
        print(f"❌ DEBUG: Erreur dans la route rapports: {e}")
        import traceback
        traceback.print_exc()
        flash(f'Erreur lors de la génération des rapports: {e}', 'error')
        return render_template('rapports_debug.html',
                             stats={'erreur': str(e)}, vehicules_par_statut=[], vehicules_par_carburant=[],
                             vehicules_par_marque=[], maintenances_par_mois=[], maintenances_par_type=[],
                             vehicules_maintenances=[], conducteurs_actifs=[])

@gesparc_app.route('/api/dashboard-data')
def api_dashboard_data():
    """API pour récupérer les données du dashboard en temps réel"""
    try:
        conn = get_db_connection()

        # Données de base
        data = {
            'timestamp': datetime.now().isoformat(),
            'stats': {
                'total_vehicules': conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0],
                'vehicules_disponibles': conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'").fetchone()[0],
                'vehicules_en_maintenance': conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'en_maintenance'").fetchone()[0],
                'maintenances_planifiees': conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'planifiee'").fetchone()[0],
                'cout_mois': conn.execute('''
                    SELECT COALESCE(SUM(cout), 0) FROM maintenances
                    WHERE cout IS NOT NULL AND date_maintenance >= date('now', 'start of month')
                ''').fetchone()[0]
            },
            'alertes_recentes': []
        }

        # Alertes récentes (dernières 24h)
        alertes = conn.execute('''
            SELECT v.immatriculation, m.type_maintenance, m.date_maintenance, m.priorite
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            WHERE m.date_creation >= date('now', '-1 day')
            AND m.statut = 'planifiee'
            ORDER BY m.date_creation DESC
            LIMIT 5
        ''').fetchall()

        for alerte in alertes:
            data['alertes_recentes'].append({
                'vehicule': alerte[0],
                'type': alerte[1],
                'date': alerte[2],
                'priorite': alerte[3]
            })

        conn.close()
        return jsonify(data)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@gesparc_app.route('/api/dashboard-filters')
def api_dashboard_filters():
    """API pour appliquer des filtres au dashboard"""
    try:
        period = request.args.get('period', '30')
        vehicle_type = request.args.get('vehicle_type', 'all')
        status = request.args.get('status', 'all')

        conn = get_db_connection()

        # Construction de la requête avec filtres
        where_conditions = []
        params = []

        if vehicle_type != 'all':
            where_conditions.append('v.carburant = ?')
            params.append(vehicle_type)

        if status != 'all':
            where_conditions.append('v.statut = ?')
            params.append(status)

        where_clause = ' AND '.join(where_conditions)
        if where_clause:
            where_clause = 'WHERE ' + where_clause

        # Données filtrées
        query = f'''
            SELECT COUNT(*) FROM vehicules v {where_clause}
        '''

        total_filtered = conn.execute(query, params).fetchone()[0]

        conn.close()

        return jsonify({
            'total_filtered': total_filtered,
            'filters_applied': {
                'period': period,
                'vehicle_type': vehicle_type,
                'status': status
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@gesparc_app.route('/rapports/export/<format>')
def export_rapports(format):
    """Exporter les rapports en différents formats"""
    try:
        if format == 'csv':
            return export_rapports_csv()
        elif format == 'excel':
            return export_rapports_excel()
        else:
            flash('Format d\'export non supporté', 'error')
            return redirect(url_for('rapports'))

    except Exception as e:
        flash(f'Erreur lors de l\'export: {e}', 'error')
        return redirect(url_for('rapports'))

def export_rapports_csv():
    """Exporter les rapports en CSV"""
    import csv
    import io
    from flask import make_response

    try:
        conn = get_db_connection()

        output = io.StringIO()
        writer = csv.writer(output)

        # Export des véhicules
        output.write("=== VEHICULES ===\n")
        writer.writerow(['Immatriculation', 'Marque', 'Modèle', 'Année', 'Statut', 'Carburant', 'Kilométrage'])
        vehicules = conn.execute('SELECT immatriculation, marque, modele, annee, statut, carburant, kilometrage FROM vehicules ORDER BY immatriculation').fetchall()
        for row in vehicules:
            writer.writerow(row)

        output.write("\n=== MAINTENANCES ===\n")
        writer.writerow(['Véhicule', 'Type', 'Date', 'Coût (MAD)', 'Garage', 'Statut', 'Priorité'])
        maintenances = conn.execute('''
            SELECT v.immatriculation, m.type_maintenance, m.date_maintenance,
                   m.cout, m.garage, m.statut, m.priorite
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        for row in maintenances:
            writer.writerow(row)

        conn.close()

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=rapports_gesparc_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

        return response

    except Exception as e:
        flash(f'Erreur export CSV: {e}', 'error')
        return redirect(url_for('rapports'))

def export_rapports_excel():
    """Exporter les rapports en Excel"""
    try:
        # Tentative d'import d'openpyxl
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            flash('Module openpyxl non installé. Export Excel non disponible.', 'error')
            return redirect(url_for('rapports'))

        import io
        from flask import make_response

        conn = get_db_connection()

        wb = openpyxl.Workbook()

        # Feuille Véhicules
        ws_vehicules = wb.active
        ws_vehicules.title = "Véhicules"
        headers = ['Immatriculation', 'Marque', 'Modèle', 'Année', 'Statut', 'Carburant', 'Kilométrage']
        ws_vehicules.append(headers)

        # Style pour les en-têtes
        header_font = Font(bold=True)
        header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        for col in range(1, len(headers) + 1):
            cell = ws_vehicules.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill

        vehicules = conn.execute('SELECT immatriculation, marque, modele, annee, statut, carburant, kilometrage FROM vehicules ORDER BY immatriculation').fetchall()
        for row in vehicules:
            ws_vehicules.append(row)

        # Feuille Maintenances
        ws_maintenances = wb.create_sheet("Maintenances")
        headers = ['Véhicule', 'Type', 'Date', 'Coût (MAD)', 'Garage', 'Statut', 'Priorité']
        ws_maintenances.append(headers)

        for col in range(1, len(headers) + 1):
            cell = ws_maintenances.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill

        maintenances = conn.execute('''
            SELECT v.immatriculation, m.type_maintenance, m.date_maintenance,
                   m.cout, m.garage, m.statut, m.priorite
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        for row in maintenances:
            ws_maintenances.append(row)

        conn.close()

        # Sauvegarder en mémoire
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        response = make_response(output.read())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename=rapports_gesparc_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

        return response

    except Exception as e:
        flash(f'Erreur export Excel: {e}', 'error')
        return redirect(url_for('rapports'))

# Routes d'export
@gesparc_app.route('/export/vehicules/<format>')
def export_vehicules(format):
    """Exporter la liste des véhicules"""
    try:
        conn = get_db_connection()
        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()
        conn.close()

        headers = ['Immatriculation', 'Marque', 'Modèle', 'Année', 'Couleur',
                  'Kilométrage', 'Carburant', 'Statut', 'Date acquisition']

        filename = get_export_filename('vehicules', format)

        if format == 'csv':
            return export_to_csv(vehicules, headers, filename)
        elif format == 'xlsx':
            return export_to_excel_xlsx(vehicules, headers, filename, 'Véhicules')
        elif format == 'xls':
            return export_to_excel_xls(vehicules, headers, filename, 'Véhicules')
        else:
            flash('Format d\'export non supporté', 'error')
            return redirect(url_for('vehicules'))

    except Exception as e:
        flash(f'Erreur lors de l\'export: {e}', 'error')
        return redirect(url_for('vehicules'))

@gesparc_app.route('/export/conducteurs/<format>')
def export_conducteurs(format):
    """Exporter la liste des conducteurs"""
    try:
        conn = get_db_connection()
        conducteurs = conn.execute('SELECT * FROM conducteurs ORDER BY nom, prenom').fetchall()
        conn.close()

        headers = ['Nom', 'Prénom', 'Numéro permis', 'Date permis',
                  'Téléphone', 'Email', 'Statut']

        filename = get_export_filename('conducteurs', format)

        if format == 'csv':
            return export_to_csv(conducteurs, headers, filename)
        elif format == 'xlsx':
            return export_to_excel_xlsx(conducteurs, headers, filename, 'Conducteurs')
        elif format == 'xls':
            return export_to_excel_xls(conducteurs, headers, filename, 'Conducteurs')
        else:
            flash('Format d\'export non supporté', 'error')
            return redirect(url_for('conducteurs'))

    except Exception as e:
        flash(f'Erreur lors de l\'export: {e}', 'error')
        return redirect(url_for('conducteurs'))

@gesparc_app.route('/export/maintenances/<format>')
def export_maintenances(format):
    """Exporter la liste des maintenances"""
    try:
        conn = get_db_connection()
        maintenances = conn.execute('''
            SELECT m.type_maintenance, m.description, m.date_maintenance,
                   m.cout, m.garage, m.statut, v.immatriculation, v.marque, v.modele
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        conn.close()

        headers = ['Véhicule', 'Marque', 'Modèle', 'Type maintenance', 'Description',
                  'Date maintenance', 'Coût (MAD)', 'Garage', 'Statut']

        filename = get_export_filename('maintenances', format)

        if format == 'csv':
            return export_to_csv(maintenances, headers, filename)
        elif format == 'xlsx':
            return export_to_excel_xlsx(maintenances, headers, filename, 'Maintenances')
        elif format == 'xls':
            return export_to_excel_xls(maintenances, headers, filename, 'Maintenances')
        else:
            flash('Format d\'export non supporté', 'error')
            return redirect(url_for('maintenances'))

    except Exception as e:
        flash(f'Erreur lors de l\'export: {e}', 'error')
        return redirect(url_for('maintenances'))

@gesparc_app.route('/export/affectations/<format>')
def export_affectations(format):
    """Exporter la liste des affectations"""
    try:
        conn = get_db_connection()
        affectations = conn.execute('''
            SELECT v.immatriculation, v.marque, v.modele, c.nom, c.prenom,
                   a.date_debut, a.date_fin, a.statut, a.commentaire
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC
        ''').fetchall()
        conn.close()

        headers = ['Véhicule', 'Marque', 'Modèle', 'Nom conducteur', 'Prénom conducteur',
                  'Date début', 'Date fin', 'Statut', 'Commentaire']

        filename = get_export_filename('affectations', format)

        if format == 'csv':
            return export_to_csv(affectations, headers, filename)
        elif format == 'xlsx':
            return export_to_excel_xlsx(affectations, headers, filename, 'Affectations')
        elif format == 'xls':
            return export_to_excel_xls(affectations, headers, filename, 'Affectations')
        else:
            flash('Format d\'export non supporté', 'error')
            return redirect(url_for('affectations'))

    except Exception as e:
        flash(f'Erreur lors de l\'export: {e}', 'error')
        return redirect(url_for('affectations'))

@gesparc_app.route('/export/complet/<format>')
def export_complet(format):
    """Exporter toutes les données dans un fichier multi-feuilles (Excel uniquement)"""
    try:
        if format not in ['xlsx', 'xls']:
            flash('Export complet disponible uniquement en format Excel', 'error')
            return redirect(url_for('rapports'))

        conn = get_db_connection()

        # Récupérer toutes les données
        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()
        conducteurs = conn.execute('SELECT * FROM conducteurs ORDER BY nom, prenom').fetchall()
        maintenances = conn.execute('''
            SELECT m.*, v.immatriculation, v.marque, v.modele
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele, c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC
        ''').fetchall()

        conn.close()

        filename = get_export_filename('gesparc_complet', format)

        # Pour l'export complet, on utilise une fonction spécialisée
        return export_complet_excel(vehicules, conducteurs, maintenances, affectations, filename, format)

    except Exception as e:
        flash(f'Erreur lors de l\'export complet: {e}', 'error')
        return redirect(url_for('rapports'))

def export_complet_excel(vehicules, conducteurs, maintenances, affectations, filename, format):
    """Exporte toutes les données dans un fichier Excel multi-feuilles"""
    if format == 'xlsx':
        import openpyxl
        from openpyxl.styles import Font, PatternFill

        wb = openpyxl.Workbook()

        # Supprimer la feuille par défaut
        wb.remove(wb.active)

        # Feuille Véhicules
        ws_vehicules = wb.create_sheet("Véhicules")
        headers_vehicules = ['Immatriculation', 'Marque', 'Modèle', 'Année', 'Couleur',
                           'Kilométrage', 'Carburant', 'Statut', 'Date acquisition']
        add_data_to_sheet(ws_vehicules, vehicules, headers_vehicules)

        # Feuille Conducteurs
        ws_conducteurs = wb.create_sheet("Conducteurs")
        headers_conducteurs = ['Nom', 'Prénom', 'Numéro permis', 'Date permis',
                             'Téléphone', 'Email', 'Statut']
        add_data_to_sheet(ws_conducteurs, conducteurs, headers_conducteurs)

        # Feuille Maintenances
        ws_maintenances = wb.create_sheet("Maintenances")
        headers_maintenances = ['Véhicule', 'Type maintenance', 'Description',
                              'Date maintenance', 'Coût (MAD)', 'Garage', 'Statut']
        add_data_to_sheet(ws_maintenances, maintenances, headers_maintenances)

        # Feuille Affectations
        ws_affectations = wb.create_sheet("Affectations")
        headers_affectations = ['Véhicule', 'Conducteur', 'Date début', 'Date fin', 'Statut', 'Commentaire']
        add_data_to_sheet(ws_affectations, affectations, headers_affectations)

        # Sauvegarder
        import io
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        from flask import Response
        return Response(
            output.getvalue(),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={'Content-Disposition': f'attachment; filename={filename}.xlsx'}
        )

    else:  # format == 'xls'
        # Pour XLS, on fait un export simple de la première feuille seulement
        headers = ['Immatriculation', 'Marque', 'Modèle', 'Année', 'Carburant', 'Statut']
        return export_to_excel_xls(vehicules, headers, filename, 'Véhicules')

def add_data_to_sheet(worksheet, data, headers):
    """Ajoute des données à une feuille Excel avec formatage"""
    from openpyxl.styles import Font, PatternFill, Alignment

    # Styles pour les en-têtes
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")

    # Écrire les en-têtes
    for col, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment

    # Écrire les données
    for row_idx, row in enumerate(data, 2):
        for col_idx, header in enumerate(headers, 1):
            if isinstance(row, dict):
                key = header.lower().replace(' ', '_').replace('é', 'e').replace('è', 'e')
                value = row.get(key, '')
            else:
                value = row[col_idx - 1] if col_idx - 1 < len(row) else ''
            worksheet.cell(row=row_idx, column=col_idx, value=value)

    # Ajuster la largeur des colonnes
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        worksheet.column_dimensions[column_letter].width = adjusted_width

# Routes Analytics Matplotlib
@gesparc_app.route('/analytics/matplotlib')
def analytics_matplotlib():
    """Page des analytics avec graphiques Matplotlib"""
    try:
        analytics = GesparcAnalytics()

        # Générer tous les graphiques
        charts = analytics.create_comprehensive_report()

        return render_template('analytics_matplotlib.html', charts=charts)

    except Exception as e:
        flash(f'Erreur lors de la génération des analytics: {e}', 'error')
        return render_template('analytics_matplotlib.html', charts={})

@gesparc_app.route('/api/analytics/chart/<chart_type>')
def api_analytics_chart(chart_type):
    """API pour générer un graphique spécifique"""
    try:
        analytics = GesparcAnalytics()

        if chart_type == 'evolution':
            period = request.args.get('period', 12, type=int)
            chart_data = analytics.create_maintenance_evolution_chart(period)
        elif chart_type == 'dashboard':
            chart_data = analytics.create_vehicle_analysis_dashboard()
        elif chart_type == 'costs':
            chart_data = analytics.create_maintenance_cost_analysis()
        elif chart_type == 'heatmap':
            chart_data = analytics.create_performance_heatmap()
        elif chart_type == 'predictive':
            chart_data = analytics.create_predictive_analysis()
        else:
            return jsonify({'error': 'Type de graphique non supporté'}), 400

        return jsonify({
            'success': True,
            'chart_data': chart_data,
            'chart_type': chart_type
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@gesparc_app.route('/analytics/download/<chart_type>')
def download_analytics_chart(chart_type):
    """Télécharger un graphique en PNG"""
    try:
        analytics = GesparcAnalytics()

        if chart_type == 'evolution':
            chart_data = analytics.create_maintenance_evolution_chart()
        elif chart_type == 'dashboard':
            chart_data = analytics.create_vehicle_analysis_dashboard()
        elif chart_type == 'costs':
            chart_data = analytics.create_maintenance_cost_analysis()
        elif chart_type == 'heatmap':
            chart_data = analytics.create_performance_heatmap()
        elif chart_type == 'predictive':
            chart_data = analytics.create_predictive_analysis()
        elif chart_type == 'advanced_fleet':
            chart_data = analytics.create_advanced_fleet_analytics()
        elif chart_type == 'financial':
            chart_data = analytics.create_financial_dashboard()
        elif chart_type == 'correlation':
            chart_data = analytics.create_correlation_analysis()
        elif chart_type == 'operational':
            chart_data = analytics.create_operational_efficiency_dashboard()
        else:
            flash('Type de graphique non supporté', 'error')
            return redirect(url_for('analytics_matplotlib'))

        # Décoder le base64 et créer la réponse
        import base64
        from flask import make_response
        img_data = base64.b64decode(chart_data)

        response = make_response(img_data)
        response.headers['Content-Type'] = 'image/png'
        response.headers['Content-Disposition'] = f'attachment; filename=gesparc_analytics_{chart_type}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'

        return response

    except Exception as e:
        flash(f'Erreur lors du téléchargement: {e}', 'error')
        return redirect(url_for('analytics_matplotlib'))

@gesparc_app.route('/test-prefix')
def test_prefix():
    """Page de test pour vérifier la configuration du préfixe"""
    return render_template('test_prefix.html')

@gesparc_app.route('/api/prefix-info')
def api_prefix_info():
    """API pour obtenir les informations de configuration du préfixe"""
    return jsonify({
        'use_prefix': USE_PREFIX,
        'prefix': GESPARC_PREFIX if USE_PREFIX else None,
        'application_root': gesparc_app.config.get('APPLICATION_ROOT'),
        'script_name': os.environ.get('SCRIPT_NAME'),
        'request_url': request.url,
        'request_path': request.path,
        'request_script_root': request.script_root,
        'urls': {
            'index': url_for('index'),
            'vehicules': url_for('vehicules'),
            'analytics': url_for('analytics_matplotlib'),
            'rapports': url_for('rapports'),
            'test_prefix': url_for('test_prefix')
        },
        'urls_with_prefix': {
            'index': url_for_prefix('index'),
            'vehicules': url_for_prefix('vehicules'),
            'analytics': url_for_prefix('analytics_matplotlib'),
            'rapports': url_for_prefix('rapports'),
            'test_prefix': url_for_prefix('test_prefix')
        } if USE_PREFIX else None
    })



if __name__ == '__main__':
    print("=" * 50)
    print("🚗 GesParc Auto - Gestion de Parc Automobile")
    print("=" * 50)
    print("Démarrage de l'application...")
    print("Application disponible sur: http://localhost:8080")
    if USE_PREFIX:
        print("Via Apache: http://localhost/gesparc")
    print("Appuyez sur Ctrl+C pour arrêter l'application")
    print("=" * 50)

    gesparc_app.run(debug=True, host='127.0.0.1', port=8080)
