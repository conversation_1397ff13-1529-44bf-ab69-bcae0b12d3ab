#!/usr/bin/env python3
"""
Test simple des heures par défaut
"""

from datetime import date

def test_heures_defaut_simple():
    """Test simple des heures par défaut"""
    print("🧪 Test Simple des Heures par Défaut")
    print("=" * 40)
    
    # Simuler la logique du backend
    date_today = date.today()
    datetime_debut_default = date_today.strftime('%Y-%m-%dT00:00')
    datetime_fin_default = date_today.strftime('%Y-%m-%dT23:59')
    
    print(f"📅 Date d'aujourd'hui: {date_today}")
    print(f"🕐 Début par défaut: {datetime_debut_default}")
    print(f"🕚 Fin par défaut: {datetime_fin_default}")
    
    # Vérifications
    success = True
    
    if datetime_debut_default.endswith('T00:00'):
        print("✅ Heure de début correcte (00:00)")
    else:
        print("❌ Erreur heure de début")
        success = False
    
    if datetime_fin_default.endswith('T23:59'):
        print("✅ Heure de fin correcte (23:59)")
    else:
        print("❌ Erreur heure de fin")
        success = False
    
    # Test de format
    if 'T' in datetime_debut_default and 'T' in datetime_fin_default:
        print("✅ Format datetime-local correct")
    else:
        print("❌ Format incorrect")
        success = False
    
    return success

def test_fichiers_modifies():
    """Test que les fichiers ont été modifiés"""
    print(f"\n🔍 Vérification des Fichiers Modifiés:")
    print("-" * 40)
    
    success = True
    
    # Vérifier gesparc_app.py
    try:
        with open('gesparc_app.py', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        if 'datetime_debut_default' in contenu:
            print("✅ Variable datetime_debut_default trouvée")
        else:
            print("❌ Variable datetime_debut_default manquante")
            success = False
        
        if 'datetime_fin_default' in contenu:
            print("✅ Variable datetime_fin_default trouvée")
        else:
            print("❌ Variable datetime_fin_default manquante")
            success = False
        
        if 'T00:00' in contenu:
            print("✅ Heure 00:00 trouvée")
        else:
            print("❌ Heure 00:00 manquante")
            success = False
        
        if 'T23:59' in contenu:
            print("✅ Heure 23:59 trouvée")
        else:
            print("❌ Heure 23:59 manquante")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lecture gesparc_app.py: {e}")
        success = False
    
    # Vérifier ajouter_affectation.html
    try:
        with open('templates/ajouter_affectation.html', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        if 'datetime_debut_default' in contenu:
            print("✅ Template utilise datetime_debut_default")
        else:
            print("❌ Template n'utilise pas datetime_debut_default")
            success = False
        
        if 'datetime_fin_default' in contenu:
            print("✅ Template utilise datetime_fin_default")
        else:
            print("❌ Template n'utilise pas datetime_fin_default")
            success = False
        
        if 'validateDateOrder' in contenu:
            print("✅ Validation JavaScript présente")
        else:
            print("❌ Validation JavaScript manquante")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lecture template: {e}")
        success = False
    
    return success

if __name__ == '__main__':
    print("🚀 Test simple des heures par défaut...")
    
    # Tests
    test1 = test_heures_defaut_simple()
    test2 = test_fichiers_modifies()
    
    # Résultat final
    print(f"\n" + "="*40)
    if test1 and test2:
        print("🎉 HEURES PAR DÉFAUT CONFIGURÉES!")
        print("✅ Début: 00:00")
        print("✅ Fin: 23:59")
        print("✅ Fichiers modifiés correctement")
    else:
        print("⚠️  PROBLÈME DÉTECTÉ")
        print("🔧 Vérifiez les modifications")
