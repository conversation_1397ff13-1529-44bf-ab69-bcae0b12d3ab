@echo off
echo ================================================
echo    GesParc Auto - Demarrage de l'application
echo ================================================
echo.

REM Changer vers le repertoire de l'application
cd /d "c:\Apache24\htdocs\gesparc"

REM Verifier si Python est disponible
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo Veuillez installer Python et reessayer
    pause
    exit /b 1
)

REM Verifier si Flask est installe
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo Installation de Flask...
    pip install Flask
)

REM Verifier si la base de donnees existe
if not exist "parc_automobile.db" (
    echo Initialisation de la base de donnees...
    python init_db.py
)

echo Demarrage de l'application Flask...
echo.
echo Application disponible sur:
echo - Direct: http://localhost:5001
echo - Via Apache: http://localhost/gesparc
echo.
echo Appuyez sur Ctrl+C pour arreter l'application
echo ================================================

REM Lancer l'application
python gesparc_app.py
