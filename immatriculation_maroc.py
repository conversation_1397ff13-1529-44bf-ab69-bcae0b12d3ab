#!/usr/bin/env python3
"""
Module de validation et formatage des immatriculations marocaines
"""

import re

class ImmatriculationMaroc:
    """Classe pour gérer les immatriculations marocaines"""

    # Patterns pour les différents formats (basés sur la structure officielle)
    PATTERNS = {
        # Format standard actuel : NNNNN ل NN (chiffres + lettre + code territorial)
        # Accepte de 1 à 5 chiffres pour le numéro séquentiel
        'standard_actuel': r'^(\d{1,5})\s*([أبتثجحخدذرزسشصضطظعغفقكلمنهوي])\s*(\d{1,2})$',  # Lettre arabe
        'standard_actuel_latin': r'^(\d{1,5})\s*([A-Z])\s*(\d{1,2})$',  # Lettre latine équivalente

        # Format avec tirets (souvent utilisé pour la saisie)
        # Accepte de 1 à 5 chiffres pour le numéro séquentiel
        'standard_tirets': r'^(\d{1,5})-([A-Z])-(\d{1,2})$',  # 123-A-1 à 12345-A-89

        # Formats courts spécifiques (historiques et valides)
        'format_court_3': r'^(\d{3})\s*([A-Z])\s*(\d{1,2})$',    # 123 A 1
        'format_court_3_tirets': r'^(\d{3})-([A-Z])-(\d{1,2})$', # 123-A-1
        'format_court_2': r'^(\d{2})\s*([A-Z])\s*(\d{1,2})$',    # 12 A 1
        'format_court_2_tirets': r'^(\d{2})-([A-Z])-(\d{1,2})$', # 12-A-1
        'format_court_1': r'^(\d{1})\s*([A-Z])\s*(\d{1,2})$',    # 1 A 1
        'format_court_1_tirets': r'^(\d{1})-([A-Z])-(\d{1,2})$', # 1-A-1

        # Plaques spéciales
        'diplomatique': r'^(CD|CC|CMD)\s*(\d{3,4})$',        # CD 123 ou CD 1234
        'temporaire_w': r'^(W)\s*(\d{3,5})$',                # W 123 à W 12345
        'temporaire_ww': r'^(WW)\s*(\d{3,5})$',              # WW 123 à WW 12345
        'administration': r'^(ADM|ADMIN)\s*(\d{3,4})$',      # ADM 123 ou ADM 1234

        # Formats avec tirets pour les spéciaux
        'diplomatique_tirets': r'^(CD|CC|CMD)-(\d{3,4})$',   # CD-123 ou CD-1234
        'temporaire_w_tirets': r'^(W)-(\d{3,5})$',           # W-123 à W-12345
        'temporaire_ww_tirets': r'^(WW)-(\d{3,5})$',         # WW-123 à WW-12345
        'administration_tirets': r'^(ADM|ADMIN)-(\d{3,4})$', # ADM-123 ou ADM-1234
    }
    
    # Correspondance lettres arabes <-> latines (basée sur la documentation officielle)
    LETTRES_ARABE_LATIN = {
        'أ': 'A', 'ب': 'B', 'ت': 'T', 'ث': 'TH', 'ج': 'J', 'ح': 'H',
        'خ': 'KH', 'د': 'D', 'ذ': 'DH', 'ر': 'R', 'ز': 'Z', 'س': 'S',
        'ش': 'SH', 'ص': 'S', 'ض': 'D', 'ط': 'T', 'ظ': 'Z', 'ع': 'A',
        'غ': 'GH', 'ف': 'F', 'ق': 'Q', 'ك': 'K', 'ل': 'L', 'م': 'M',
        'ن': 'N', 'ه': 'H', 'و': 'W', 'ي': 'Y'
    }

    # Lettres latines autorisées (équivalents des lettres arabes)
    LETTRES_LATINES_AUTORISEES = list(LETTRES_ARABE_LATIN.values())

    # Codes territoriaux officiels (basés sur la documentation officielle)
    CODES_TERRITORIAUX = {
        '1': 'Rabat', '2': 'Salé-Médina', '3': 'Sala Al-Jadida', '4': 'Skhirat-Temara',
        '5': 'Khémisset', '6': 'Casablanca Anfa', '7': 'Casablanca Hay Mohammadi-Aïn Sebaâ',
        '8': 'Casablanca Hay Hassani', '9': 'Casablanca Benmsik', '10': 'Casablanca Moulay Rachid',
        '11': 'Casablanca-Al Fida Derb Sultan', '12': 'Casablanca Mechouar',
        '13': 'Casablanca Sidi Bernoussi-Zenata', '14': 'Mohammedia', '15': 'Fès jdid – dar dbibagh',
        '16': 'Fès Medina', '17': 'Zouagha – Moulay Yacoub', '18': 'Sefrou', '19': 'Boulmane',
        '20': 'Meknès Menzah', '21': 'Meknès Ismailia', '22': 'El Hajeb', '23': 'Ifrane',
        '24': 'Khénifra', '25': 'Errachidia', '26': 'Marrakech-Menara', '27': 'Marrakech-Medina',
        '28': 'Marrakech-Sidi Youssef Ben-Ali', '29': 'El-Haouz', '30': 'Chichaoua',
        '31': 'Kelâat Es-Sraghna', '32': 'Essaouira', '33': 'Agadir Ida-Outanane',
        '34': 'Agadir – Inezgane – Ait Melloul', '35': 'Chtouka Aït Baha', '36': 'Taroudant',
        '37': 'Tiznit', '38': 'Ouarzazate', '39': 'Zagora', '40': 'Tangier – Asilah',
        '41': 'Tanger Fahs-Bni Makada', '42': 'Larache', '43': 'Chefchaouen', '44': 'Tétouan',
        '45': 'Al-Hoceima', '46': 'Taza', '47': 'Taounate', '48': 'Oujda', '49': 'Berkane',
        '50': 'Nador', '51': 'Taourirt', '52': 'Jerada', '53': 'Figuig', '54': 'Asfi',
        '55': 'El Jadida', '56': 'Settat', '57': 'Khouribga', '58': 'Benslimane',
        '59': 'Kénitra', '60': 'Sidi Kacem', '61': 'Béni Mellal', '62': 'Azilal',
        '63': 'Smara', '64': 'Guelmim', '65': 'Tan-Tan', '66': 'Tata', '67': 'Assa-Zag',
        '68': 'Laâyoune', '69': 'Boujdour', '70': 'Oued Ed-Dahab', '71': 'Aousserd',
        '72': 'Casablanca Ain-Chock', '73': 'Casablanca Nouacer', '74': 'Casablanca Mediouna',
        '75': 'M\'diq – Fnideq', '76': 'Driouch', '77': 'Guercif', '78': 'Ouazzane',
        '79': 'Sidi Slimane', '80': 'Midelt', '81': 'Berrechid', '82': 'Sidi Bennour',
        '83': 'Ben Guerir', '84': 'Fquih Ben Salah', '85': 'Youssoufia', '86': 'Tinghir',
        '87': 'Sidi Ifni', '88': 'Tarfaya', '89': 'Lagouira'
    }
    
    @classmethod
    def valider(cls, immatriculation):
        """
        Valide une immatriculation marocaine
        
        Args:
            immatriculation (str): L'immatriculation à valider
            
        Returns:
            dict: {
                'valide': bool,
                'format': str,
                'message': str,
                'region': str (optionnel)
            }
        """
        if not immatriculation:
            return {
                'valide': False,
                'format': None,
                'message': 'Immatriculation vide'
            }
        
        # Nettoyer et normaliser
        immat_clean = immatriculation.strip().upper()
        # Remplacer les espaces par des tirets pour la normalisation
        immat_normalized = immat_clean.replace(' ', '-')

        # Tester chaque format
        for format_name, pattern in cls.PATTERNS.items():
            # Tester d'abord avec la version normalisée
            match = re.match(pattern, immat_normalized)
            if not match:
                # Tester avec la version originale (espaces)
                match = re.match(pattern, immat_clean)

            if match:
                # Déterminer le format de sortie selon le type
                if 'tirets' in format_name:
                    immat_formatee = immat_normalized
                else:
                    # Format avec espaces pour les formats standards
                    if format_name.startswith('standard'):
                        groups = match.groups()
                        immat_formatee = f"{groups[0]} {groups[1]} {groups[2]}"
                    else:
                        immat_formatee = immat_clean

                result = {
                    'valide': True,
                    'format': format_name,
                    'message': f'Format {format_name.replace("_", " ")} valide',
                    'immatriculation_formatee': immat_formatee
                }

                # Ajouter info territoriale si applicable
                if format_name.startswith('standard'):
                    code_territorial = match.group(3)
                    if code_territorial in cls.CODES_TERRITORIAUX:
                        result['region'] = cls.CODES_TERRITORIAUX[code_territorial]
                        result['code_territorial'] = code_territorial

                return result
        
        return {
            'valide': False,
            'format': None,
            'message': 'Format d\'immatriculation non reconnu'
        }
    
    @classmethod
    def formater(cls, immatriculation):
        """
        Formate une immatriculation selon les standards marocains
        
        Args:
            immatriculation (str): L'immatriculation à formater
            
        Returns:
            str: L'immatriculation formatée ou None si invalide
        """
        validation = cls.valider(immatriculation)
        if validation['valide']:
            return validation['immatriculation_formatee']
        return None
    
    @classmethod
    def generer_exemples(cls):
        """
        Génère des exemples d'immatriculations valides selon la structure officielle

        Returns:
            list: Liste d'exemples avec descriptions
        """
        return [
            {
                'immatriculation': '12345 A 1',
                'format': 'Standard actuel (5 chiffres)',
                'region': 'Rabat',
                'description': 'Format standard actuel (5 chiffres + lettre + code territorial)'
            },
            {
                'immatriculation': '1234 B 6',
                'format': 'Standard actuel (4 chiffres)',
                'region': 'Casablanca Anfa',
                'description': 'Format standard avec 4 chiffres'
            },
            {
                'immatriculation': '123 A 1',
                'format': 'Format court (3 chiffres)',
                'region': 'Rabat',
                'description': 'Format court avec 3 chiffres (valide)'
            },
            {
                'immatriculation': '12 C 16',
                'format': 'Format court (2 chiffres)',
                'region': 'Fès Medina',
                'description': 'Format court avec 2 chiffres (valide)'
            },
            {
                'immatriculation': '1 A 1',
                'format': 'Format très court (1 chiffre)',
                'region': 'Rabat',
                'description': 'Format très court avec 1 chiffre (valide)'
            },
            {
                'immatriculation': '67890 ل 34',
                'format': 'Standard avec lettre arabe',
                'region': 'Agadir – Inezgane – Ait Melloul',
                'description': 'Format avec lettre arabe authentique'
            },
            {
                'immatriculation': '123-A-1',
                'format': 'Format court avec tirets',
                'region': 'Rabat',
                'description': 'Format court avec tirets (saisie courante)'
            },
            {
                'immatriculation': 'CD 123',
                'format': 'Diplomatique court',
                'region': 'Corps Diplomatique',
                'description': 'Véhicule diplomatique format court'
            },
            {
                'immatriculation': 'WW 123',
                'format': 'Temporaire court',
                'region': 'Immatriculation temporaire',
                'description': 'Véhicule temporaire format court'
            },
            {
                'immatriculation': 'W 567',
                'format': 'Temporaire garage court',
                'region': 'Immatriculation temporaire',
                'description': 'Véhicule garage format court'
            }
        ]
    
    @classmethod
    def obtenir_info_region(cls, immatriculation):
        """
        Obtient les informations régionales d'une immatriculation
        
        Args:
            immatriculation (str): L'immatriculation
            
        Returns:
            dict: Informations sur la région
        """
        validation = cls.valider(immatriculation)
        if validation['valide'] and 'region' in validation:
            return {
                'region': validation['region'],
                'lettre': immatriculation.split('-')[1] if '-' in immatriculation else None
            }
        return None

def tester_immatriculations():
    """Fonction de test pour les immatriculations"""
    print("🇲🇦 Test des Immatriculations Marocaines")
    print("=" * 50)
    
    # Tests de validation avec les vrais formats marocains (y compris formats courts)
    tests = [
        '12345 A 1',     # Standard actuel (5 chiffres)
        '1234 B 6',      # Standard actuel (4 chiffres)
        '123 A 1',       # Format court (3 chiffres) - VALIDE
        '12 C 16',       # Format court (2 chiffres) - VALIDE
        '1 A 1',         # Format très court (1 chiffre) - VALIDE
        '67890 ل 34',    # Standard avec lettre arabe
        '123-A-1',       # Format court avec tirets - VALIDE
        '12-B-6',        # Format très court avec tirets - VALIDE
        '1-A-1',         # Format minimal avec tirets - VALIDE
        'CD 123',        # Diplomatique court - VALIDE
        'CD 1234',       # Diplomatique standard
        'WW 123',        # Temporaire court - VALIDE
        'WW 12345',      # Temporaire standard
        'W 567',         # Temporaire garage court - VALIDE
        '99999 Z 89',    # Maximum valide
        '123-A-45',      # Invalide (code territorial inexistant)
        'ABCD-E-FG',     # Invalide (lettres dans les chiffres)
        '12345A1',       # Sans espaces ni tirets
        '12345  A  1',   # Espaces multiples
        '',              # Vide - invalide
        'A-123-1',       # Ordre incorrect - invalide
    ]
    
    for test in tests:
        result = ImmatriculationMaroc.valider(test)
        status = "✅" if result['valide'] else "❌"
        print(f"{status} {test:<12} → {result['message']}")
        if result['valide'] and 'region' in result:
            print(f"   Région: {result['region']}")
    
    print("\n📋 Exemples d'immatriculations valides:")
    exemples = ImmatriculationMaroc.generer_exemples()
    for ex in exemples:
        print(f"  {ex['immatriculation']:<12} - {ex['description']} ({ex['region']})")

if __name__ == "__main__":
    tester_immatriculations()
