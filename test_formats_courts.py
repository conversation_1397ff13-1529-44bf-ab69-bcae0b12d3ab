#!/usr/bin/env python3
"""
Test des formats courts d'immatriculation marocaine
"""

import requests
import sys

def test_formats_courts():
    """Teste les formats courts d'immatriculation"""
    print("🔧 Test des Formats Courts d'Immatriculation")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    
    # Formats courts à tester
    formats_tests = [
        # Formats courts valides
        ('123 A 1', 'Format court 3 chiffres', True),
        ('12 B 6', 'Format court 2 chiffres', True),
        ('1 A 1', 'Format très court 1 chiffre', True),
        ('123-A-1', 'Format court avec tirets', True),
        ('12-B-6', 'Format court tirets 2 chiffres', True),
        ('1-A-1', 'Format minimal avec tirets', True),
        
        # Formats spéciaux courts
        ('CD 123', 'Diplomatique court', True),
        ('WW 123', 'Temporaire court', True),
        ('W 567', 'Garage court', True),
        
        # Formats longs (toujours valides)
        ('12345 A 1', 'Format standard long', True),
        ('1234 B 6', 'Format standard moyen', True),
        
        # Formats invalides
        ('ABCD-E-FG', 'Lettres dans chiffres', False),
        ('A-123-1', 'Ordre incorrect', False),
        ('123456-A-1', 'Trop de chiffres', False),
        ('', 'Vide', False),
    ]
    
    try:
        print("1. Test de l'API de validation...")
        
        resultats = []
        for immat, description, attendu in formats_tests:
            try:
                response = requests.post(
                    f"{base_url}/api/valider-immatriculation",
                    json={'immatriculation': immat},
                    timeout=5
                )
                
                if response.status_code == 200:
                    data = response.json()
                    valide = data.get('valide', False)
                    
                    if valide == attendu:
                        status = "✅"
                        resultats.append(True)
                    else:
                        status = "❌"
                        resultats.append(False)
                    
                    print(f"   {status} {immat:<12} → {description}")
                    if valide and 'region' in data:
                        print(f"      📍 Région: {data['region']}")
                    if not valide:
                        print(f"      ⚠️ Erreur: {data.get('message', 'Inconnue')}")
                        
                else:
                    print(f"   ❌ {immat:<12} → Erreur HTTP {response.status_code}")
                    resultats.append(False)
                    
            except Exception as e:
                print(f"   ❌ {immat:<12} → Erreur: {e}")
                resultats.append(False)
        
        # Statistiques
        print(f"\n📊 Résultats:")
        total = len(resultats)
        succes = sum(resultats)
        echecs = total - succes
        
        print(f"   Total tests: {total}")
        print(f"   Succès: {succes}")
        print(f"   Échecs: {echecs}")
        print(f"   Taux de réussite: {(succes/total*100):.1f}%")
        
        # Test de la page guide
        print("\n2. Test de la page guide...")
        response = requests.get(f"{base_url}/guide-immatriculation", timeout=10)
        if response.status_code == 200:
            print("   ✅ Page guide accessible")
            if "Format court" in response.text:
                print("   ✅ Documentation des formats courts présente")
            else:
                print("   ⚠️ Documentation des formats courts manquante")
        else:
            print(f"   ❌ Page guide inaccessible: {response.status_code}")
        
        # Test de la page d'ajout
        print("\n3. Test de la page d'ajout de véhicule...")
        response = requests.get(f"{base_url}/vehicules/ajouter", timeout=10)
        if response.status_code == 200:
            print("   ✅ Page d'ajout accessible")
            if "123-A-1" in response.text:
                print("   ✅ Exemples de formats courts présents")
            else:
                print("   ⚠️ Exemples de formats courts manquants")
        else:
            print(f"   ❌ Page d'ajout inaccessible: {response.status_code}")
        
        print("\n" + "=" * 60)
        print("🏁 Tests des formats courts terminés")
        
        return echecs == 0
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
        print("💡 Vérifiez que l'application Flask est démarrée")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def test_exemples_specifiques():
    """Teste des exemples spécifiques de formats courts"""
    print("\n🎯 Test d'Exemples Spécifiques")
    print("=" * 40)
    
    from immatriculation_maroc import ImmatriculationMaroc
    
    exemples = [
        '1 A 1',      # Minimum absolu
        '12 B 6',     # 2 chiffres
        '123 C 16',   # 3 chiffres
        '1234 D 26',  # 4 chiffres
        '12345 E 34', # 5 chiffres (maximum)
        '1-A-1',      # Minimum avec tirets
        '123-A-1',    # Court avec tirets
        'CD 1',       # Diplomatique minimal
        'W 12',       # Temporaire minimal
    ]
    
    print("Test avec le module local:")
    for exemple in exemples:
        result = ImmatriculationMaroc.valider(exemple)
        status = "✅" if result['valide'] else "❌"
        print(f"   {status} {exemple:<10} → {result['message']}")
        if result['valide'] and 'region' in result:
            print(f"      📍 {result['region']}")

if __name__ == "__main__":
    print("🧪 Tests des Formats Courts d'Immatriculation Marocaine")
    print("=" * 70)
    
    success1 = test_formats_courts()
    test_exemples_specifiques()
    
    if success1:
        print("\n🎉 Tous les tests des formats courts sont passés !")
        sys.exit(0)
    else:
        print("\n❌ Certains tests ont échoué")
        sys.exit(1)
