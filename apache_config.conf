# Configuration Apache pour GesParc Auto
# À ajouter dans httpd.conf ou dans un fichier de configuration séparé

# Activer les modules nécessaires (décommentez si nécessaire)
# LoadModule rewrite_module modules/mod_rewrite.so
# LoadModule proxy_module modules/mod_proxy.so
# LoadModule proxy_http_module modules/mod_proxy_http.so

# Configuration pour le dossier gesparc
<Directory "c:/Apache24/htdocs/gesparc">
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
    
    # Activer la réécriture d'URL
    RewriteEngine On
    
    # Si le fichier ou dossier existe, le servir directement
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^.*$ - [L]
    
    # Sinon, rediriger vers index.php
    RewriteRule ^.*$ index.php [L]
</Directory>

# Configuration alternative avec proxy (si mod_proxy est disponible)
# <Location "/gesparc">
#     ProxyPass "http://127.0.0.1:5001/"
#     ProxyPassReverse "http://127.0.0.1:5001/"
#     ProxyPreserveHost On
# </Location>

# Servir les fichiers statiques directement
Alias /gesparc/static "c:/Apache24/htdocs/gesparc/static"
<Directory "c:/Apache24/htdocs/gesparc/static">
    Options -Indexes
    AllowOverride None
    Require all granted
    
    # Cache pour les fichiers statiques
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/ico "access plus 1 month"
        ExpiresByType image/icon "access plus 1 month"
        ExpiresByType text/ico "access plus 1 month"
        ExpiresByType application/ico "access plus 1 month"
    </IfModule>
</Directory>
