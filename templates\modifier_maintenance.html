{% extends "base.html" %}

{% block title %}Modifier Maintenance - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit"></i> Modifier la Maintenance</h1>
            <a href="{{ url_for('maintenances') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour aux maintenances
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools"></i> Modification de la maintenance
                    <small class="text-muted">- {{ maintenance.immatriculation }} ({{ maintenance.marque }} {{ maintenance.modele }})</small>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Type de maintenance -->
                        <div class="col-md-6 mb-3">
                            <label for="type_maintenance" class="form-label">
                                <i class="fas fa-wrench"></i> Type de maintenance *
                            </label>
                            <select class="form-select" id="type_maintenance" name="type_maintenance" required>
                                <option value="">Sélectionner le type</option>
                                <option value="Vidange" {{ 'selected' if maintenance.type_maintenance == 'Vidange' }}>Vidange</option>
                                <option value="Révision" {{ 'selected' if maintenance.type_maintenance == 'Révision' }}>Révision</option>
                                <option value="Contrôle technique" {{ 'selected' if maintenance.type_maintenance == 'Contrôle technique' }}>Contrôle technique</option>
                                <option value="Changement pneus" {{ 'selected' if maintenance.type_maintenance == 'Changement pneus' }}>Changement pneus</option>
                                <option value="Freinage" {{ 'selected' if maintenance.type_maintenance == 'Freinage' }}>Freinage</option>
                                <option value="Climatisation" {{ 'selected' if maintenance.type_maintenance == 'Climatisation' }}>Climatisation</option>
                                <option value="Batterie" {{ 'selected' if maintenance.type_maintenance == 'Batterie' }}>Batterie</option>
                                <option value="Carrosserie" {{ 'selected' if maintenance.type_maintenance == 'Carrosserie' }}>Carrosserie</option>
                                <option value="Électricité" {{ 'selected' if maintenance.type_maintenance == 'Électricité' }}>Électricité</option>
                                <option value="Transmission" {{ 'selected' if maintenance.type_maintenance == 'Transmission' }}>Transmission</option>
                                <option value="Suspension" {{ 'selected' if maintenance.type_maintenance == 'Suspension' }}>Suspension</option>
                                <option value="Autre" {{ 'selected' if maintenance.type_maintenance == 'Autre' }}>Autre</option>
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner le type de maintenance
                            </div>
                        </div>

                        <!-- Date de maintenance -->
                        <div class="col-md-6 mb-3">
                            <label for="date_maintenance" class="form-label">
                                <i class="fas fa-calendar"></i> Date prévue *
                            </label>
                            <input type="date" class="form-control" id="date_maintenance" 
                                   name="date_maintenance" required
                                   value="{{ maintenance.date_maintenance }}">
                            <div class="invalid-feedback">
                                Veuillez saisir la date de maintenance
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Priorité -->
                        <div class="col-md-6 mb-3">
                            <label for="priorite" class="form-label">
                                <i class="fas fa-exclamation-circle"></i> Priorité
                            </label>
                            <select class="form-select" id="priorite" name="priorite">
                                <option value="faible" {{ 'selected' if maintenance.priorite == 'faible' }}>🟢 Faible</option>
                                <option value="normale" {{ 'selected' if maintenance.priorite == 'normale' or not maintenance.priorite }}>🟡 Normale</option>
                                <option value="elevee" {{ 'selected' if maintenance.priorite == 'elevee' }}>🟠 Élevée</option>
                                <option value="urgente" {{ 'selected' if maintenance.priorite == 'urgente' }}>🔴 Urgente</option>
                            </select>
                        </div>

                        <!-- Statut -->
                        <div class="col-md-6 mb-3">
                            <label for="statut" class="form-label">
                                <i class="fas fa-flag"></i> Statut
                            </label>
                            <select class="form-select" id="statut" name="statut">
                                <option value="planifiee" {{ 'selected' if maintenance.statut == 'planifiee' }}>📅 Planifiée</option>
                                <option value="en_cours" {{ 'selected' if maintenance.statut == 'en_cours' }}>⚙️ En cours</option>
                                <option value="terminee" {{ 'selected' if maintenance.statut == 'terminee' }}>✅ Terminée</option>
                                <option value="reportee" {{ 'selected' if maintenance.statut == 'reportee' }}>⏸️ Reportée</option>
                                <option value="annulee" {{ 'selected' if maintenance.statut == 'annulee' }}>❌ Annulée</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Coût -->
                        <div class="col-md-6 mb-3">
                            <label for="cout" class="form-label">
                                <i class="fas fa-coins"></i> Coût (MAD)
                            </label>
                            <input type="number" class="form-control" id="cout" 
                                   name="cout" min="0" step="0.01"
                                   value="{{ maintenance.cout if maintenance.cout else '' }}"
                                   placeholder="0.00">
                            <small class="form-text text-muted">
                                Coût de la maintenance
                            </small>
                        </div>

                        <!-- Garage -->
                        <div class="col-md-6 mb-3">
                            <label for="garage" class="form-label">
                                <i class="fas fa-store"></i> Garage / Prestataire
                            </label>
                            <input type="text" class="form-control" id="garage" 
                                   name="garage" 
                                   value="{{ maintenance.garage if maintenance.garage else '' }}"
                                   placeholder="Nom du garage ou prestataire">
                        </div>
                    </div>

                    <div class="row">
                        <!-- Description -->
                        <div class="col-12 mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-comment"></i> Description / Notes
                            </label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="4" placeholder="Détails de la maintenance, pièces à changer, observations...">{{ maintenance.description if maintenance.description else '' }}</textarea>
                        </div>
                    </div>

                    <!-- Informations véhicule (lecture seule) -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-car"></i> Véhicule concerné</h6>
                                <strong>{{ maintenance.immatriculation }}</strong> - {{ maintenance.marque }} {{ maintenance.modele }}
                                <a href="{{ url_for('voir_vehicule', id=maintenance.vehicule_id) }}" 
                                   class="btn btn-sm btn-outline-primary ms-2">
                                    <i class="fas fa-eye"></i> Voir le véhicule
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Boutons -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a href="{{ url_for('voir_maintenance', id=maintenance.id) }}" class="btn btn-outline-info">
                                        <i class="fas fa-eye"></i> Voir détails
                                    </a>
                                    <a href="{{ url_for('maintenances') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Annuler
                                    </a>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Enregistrer les modifications
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
{% endblock %}
