{% extends "base.html" %}

{% block title %}Debug Rapports - GesParc Auto{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">🔍 Debug Rapports</h1>
            
            <!-- Test basique -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Test Basique</h5>
                </div>
                <div class="card-body">
                    <p>Si vous voyez ce message, le template fonctionne.</p>
                    <p>Total véhicules: {{ stats.total_vehicules or "Non défini" }}</p>
                    <p>Total conducteurs: {{ stats.total_conducteurs or "Non défini" }}</p>
                </div>
            </div>

            <!-- Test des stats -->
            {% if stats %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Statistiques Disponibles</h5>
                </div>
                <div class="card-body">
                    <ul>
                        {% for key, value in stats.items() %}
                        <li><strong>{{ key }}:</strong> {{ value }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- Test des véhicules par statut -->
            {% if vehicules_par_statut %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Véhicules par Statut</h5>
                </div>
                <div class="card-body">
                    <ul>
                        {% for item in vehicules_par_statut %}
                        <li>{{ item.statut }}: {{ item.count }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- Test des alertes -->
            {% if stats.alertes %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Alertes</h5>
                </div>
                <div class="card-body">
                    {% for alerte in stats.alertes %}
                    <div class="alert alert-info">
                        <strong>{{ alerte.type }}:</strong> {{ alerte.message }}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Test simple sans formatage complexe -->
            <div class="card">
                <div class="card-header">
                    <h5>Test Variables</h5>
                </div>
                <div class="card-body">
                    <p>Coût total: {{ stats.cout_total_maintenances or 0 }}</p>
                    <p>Taux disponibilité: {{ stats.taux_disponibilite or 0 }}</p>
                    <p>Nombre d'alertes: {{ stats.nb_alertes or 0 }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
