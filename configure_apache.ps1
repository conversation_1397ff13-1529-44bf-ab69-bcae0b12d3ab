# Configuration Apache pour GesParc Auto
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "   Configuration Apache pour GesParc Auto" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# Variables
$ApacheDir = "c:\Apache24"
$GesparcDir = "c:\Apache24\htdocs\gesparc"
$HttpdConf = "$ApacheDir\conf\httpd.conf"

# Vérifier si Apache existe
if (-not (Test-Path $ApacheDir)) {
    Write-Host "❌ ERREUR: Apache non trouvé dans $ApacheDir" -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

Write-Host "1. Sauvegarde de la configuration actuelle..." -ForegroundColor Yellow
try {
    Copy-Item $HttpdConf "$HttpdConf.backup" -Force
    Write-Host "   ✅ Sauvegarde créée" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️ Impossible de créer la sauvegarde" -ForegroundColor Yellow
}

Write-Host "2. Vérification des modules nécessaires..." -ForegroundColor Yellow

# Fonction pour vérifier et ajouter un module
function Add-ApacheModule {
    param($ModuleName, $ModuleLine)
    
    $content = Get-Content $HttpdConf
    if ($content -notcontains $ModuleLine) {
        Write-Host "   - Activation de $ModuleName" -ForegroundColor Cyan
        Add-Content $HttpdConf $ModuleLine
        return $true
    } else {
        Write-Host "   ✅ $ModuleName déjà activé" -ForegroundColor Green
        return $false
    }
}

# Vérifier les modules
$modulesAdded = $false
$modulesAdded = (Add-ApacheModule "mod_rewrite" "LoadModule rewrite_module modules/mod_rewrite.so") -or $modulesAdded
$modulesAdded = (Add-ApacheModule "mod_proxy" "LoadModule proxy_module modules/mod_proxy.so") -or $modulesAdded
$modulesAdded = (Add-ApacheModule "mod_proxy_http" "LoadModule proxy_http_module modules/mod_proxy_http.so") -or $modulesAdded

Write-Host "3. Ajout de la configuration GesParc..." -ForegroundColor Yellow

# Vérifier si la configuration GesParc est déjà présente
$content = Get-Content $HttpdConf
if ($content -notcontains "# GesParc Auto Configuration") {
    Add-Content $HttpdConf ""
    Add-Content $HttpdConf "# GesParc Auto Configuration"
    Add-Content $HttpdConf "Include `"$GesparcDir\gesparc-apache.conf`""
    Write-Host "   ✅ Configuration GesParc ajoutée" -ForegroundColor Green
} else {
    Write-Host "   ✅ Configuration GesParc déjà présente" -ForegroundColor Green
}

Write-Host "4. Vérification de la configuration..." -ForegroundColor Yellow

# Tester la configuration Apache
$testResult = & "$ApacheDir\bin\httpd.exe" -t 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ Configuration Apache valide" -ForegroundColor Green
} else {
    Write-Host "   ❌ Configuration Apache invalide:" -ForegroundColor Red
    Write-Host $testResult -ForegroundColor Red
    
    Write-Host "Restauration de la sauvegarde..." -ForegroundColor Yellow
    Copy-Item "$HttpdConf.backup" $HttpdConf -Force
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

Write-Host "5. Redémarrage d'Apache..." -ForegroundColor Yellow

# Redémarrer Apache
try {
    Stop-Service "Apache2.4" -ErrorAction SilentlyContinue
    Start-Sleep 2
    Start-Service "Apache2.4"
    Write-Host "   ✅ Apache redémarré avec succès" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️ Impossible de redémarrer Apache automatiquement" -ForegroundColor Yellow
    Write-Host "   Veuillez redémarrer Apache manuellement" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "Configuration terminée avec succès !" -ForegroundColor Green
Write-Host ""
Write-Host "URLs d'accès :" -ForegroundColor Cyan
Write-Host "  - Via Apache: http://localhost/gesparc" -ForegroundColor White
Write-Host "  - Direct Flask: http://localhost:5001" -ForegroundColor White
Write-Host ""
Write-Host "Pour démarrer l'application Flask :" -ForegroundColor Cyan
Write-Host "  cd $GesparcDir" -ForegroundColor White
Write-Host "  python gesparc_app.py" -ForegroundColor White
Write-Host "================================================" -ForegroundColor Cyan

Read-Host "Appuyez sur Entrée pour continuer"
