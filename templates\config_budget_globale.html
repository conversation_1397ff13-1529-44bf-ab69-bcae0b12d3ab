{% extends "base.html" %}

{% block title %}Configuration Globale des Budgets - GesParc Auto{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-cogs text-secondary"></i> Configuration Globale des Budgets
                </h1>
                <a href="{{ url_for('budget') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour aux Budgets
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h"></i> Paramètres des 3 Types de Budget
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Information :</strong> Cette page permet de configurer simultanément 
                        les paramètres des trois types de budget : Carburant, Maintenance et Transport Terrestre.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            {% for config in configs %}
                            <div class="col-md-4 mb-4">
                                <div class="card {% if config.type_budget == 'carburant' %}border-primary{% elif config.type_budget == 'maintenance' %}border-warning{% else %}border-info{% endif %}">
                                    <div class="card-header {% if config.type_budget == 'carburant' %}bg-primary text-white{% elif config.type_budget == 'maintenance' %}bg-warning text-dark{% else %}bg-info text-white{% endif %}">
                                        <h6 class="card-title mb-0">
                                            {% if config.type_budget == 'carburant' %}
                                                <i class="fas fa-gas-pump"></i> Budget Carburant
                                            {% elif config.type_budget == 'maintenance' %}
                                                <i class="fas fa-tools"></i> Budget Maintenance
                                            {% else %}
                                                <i class="fas fa-truck"></i> Budget Transport Terrestre
                                            {% endif %}
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- Budget initial -->
                                        <div class="mb-3">
                                            <label for="budget_initial_{{ config.type_budget }}" class="form-label">
                                                <i class="fas fa-piggy-bank"></i> Budget Initial (MAD) *
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" 
                                                       id="budget_initial_{{ config.type_budget }}" 
                                                       name="budget_initial_{{ config.type_budget }}" 
                                                       min="0" step="0.01" required
                                                       value="{{ config.budget_initial }}">
                                                <span class="input-group-text">MAD</span>
                                            </div>
                                            <div class="invalid-feedback">
                                                Veuillez saisir le budget initial
                                            </div>
                                        </div>

                                        <!-- Seuil d'alerte -->
                                        <div class="mb-3">
                                            <label for="seuil_alerte_{{ config.type_budget }}" class="form-label">
                                                <i class="fas fa-exclamation-triangle"></i> Seuil d'Alerte (MAD) *
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" 
                                                       id="seuil_alerte_{{ config.type_budget }}" 
                                                       name="seuil_alerte_{{ config.type_budget }}" 
                                                       min="0" step="0.01" required
                                                       value="{{ config.seuil_alerte }}">
                                                <span class="input-group-text">MAD</span>
                                            </div>
                                            <div class="invalid-feedback">
                                                Veuillez saisir le seuil d'alerte
                                            </div>
                                        </div>

                                        <!-- Période de début -->
                                        <div class="mb-3">
                                            <label for="periode_debut_{{ config.type_budget }}" class="form-label">
                                                <i class="fas fa-calendar-plus"></i> Début de Période
                                            </label>
                                            <input type="date" class="form-control" 
                                                   id="periode_debut_{{ config.type_budget }}"
                                                   name="periode_debut_{{ config.type_budget }}"
                                                   value="{{ config.periode_debut or '' }}">
                                        </div>

                                        <!-- Période de fin -->
                                        <div class="mb-3">
                                            <label for="periode_fin_{{ config.type_budget }}" class="form-label">
                                                <i class="fas fa-calendar-minus"></i> Fin de Période
                                            </label>
                                            <input type="date" class="form-control" 
                                                   id="periode_fin_{{ config.type_budget }}"
                                                   name="periode_fin_{{ config.type_budget }}"
                                                   value="{{ config.periode_fin or '' }}">
                                        </div>

                                        <!-- Aperçu actuel -->
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Configuration Actuelle :</small>
                                                <br>
                                                <small>
                                                    <strong>Initial :</strong> {{ '{:,.0f}'.format(config.budget_initial).replace(',', ' ') }} MAD<br>
                                                    <strong>Seuil :</strong> {{ '{:,.0f}'.format(config.seuil_alerte).replace(',', ' ') }} MAD<br>
                                                    <strong>Modifié :</strong> {{ config.updated_at[:10] }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Suggestions de configuration -->
                        <div class="card border-info mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-lightbulb"></i> Suggestions de Configuration
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6><i class="fas fa-gas-pump text-primary"></i> Budget Carburant</h6>
                                        <ul class="list-unstyled small">
                                            <li><i class="fas fa-check text-success"></i> Budget recommandé : 25 000 - 35 000 MAD</li>
                                            <li><i class="fas fa-check text-success"></i> Seuil d'alerte : 10-15% du budget</li>
                                            <li><i class="fas fa-info-circle text-info"></i> Surveiller la consommation mensuelle</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6><i class="fas fa-tools text-warning"></i> Budget Maintenance</h6>
                                        <ul class="list-unstyled small">
                                            <li><i class="fas fa-check text-success"></i> Budget recommandé : 20 000 - 30 000 MAD</li>
                                            <li><i class="fas fa-check text-success"></i> Seuil d'alerte : 10-15% du budget</li>
                                            <li><i class="fas fa-info-circle text-info"></i> Prévoir les révisions périodiques</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6><i class="fas fa-truck text-info"></i> Budget Transport</h6>
                                        <ul class="list-unstyled small">
                                            <li><i class="fas fa-check text-success"></i> Budget recommandé : 30 000 - 50 000 MAD</li>
                                            <li><i class="fas fa-check text-success"></i> Seuil d'alerte : 10-15% du budget</li>
                                            <li><i class="fas fa-info-circle text-info"></i> Inclure péages et frais de route</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions rapides -->
                        <div class="card border-success mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-magic"></i> Actions Rapides
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-outline-primary btn-sm mb-2" onclick="appliquerSeuils(10)">
                                            <i class="fas fa-percentage"></i> Seuils à 10% des budgets
                                        </button>
                                        <button type="button" class="btn btn-outline-primary btn-sm mb-2" onclick="appliquerSeuils(15)">
                                            <i class="fas fa-percentage"></i> Seuils à 15% des budgets
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-outline-secondary btn-sm mb-2" onclick="synchroniserPeriodes()">
                                            <i class="fas fa-sync"></i> Synchroniser les périodes
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm mb-2" onclick="resetDefaults()">
                                            <i class="fas fa-undo"></i> Valeurs par défaut
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('budget') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> Enregistrer Toutes les Configurations
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function appliquerSeuils(pourcentage) {
    const types = ['carburant', 'maintenance', 'transport_terrestre'];
    
    types.forEach(type => {
        const budgetInput = document.getElementById(`budget_initial_${type}`);
        const seuilInput = document.getElementById(`seuil_alerte_${type}`);
        
        if (budgetInput.value) {
            const budget = parseFloat(budgetInput.value);
            const seuil = budget * (pourcentage / 100);
            seuilInput.value = seuil.toFixed(2);
            
            // Effet visuel
            seuilInput.style.backgroundColor = '#e3f2fd';
            setTimeout(() => {
                seuilInput.style.backgroundColor = '';
            }, 500);
        }
    });
}

function synchroniserPeriodes() {
    const debutCarburant = document.getElementById('periode_debut_carburant').value;
    const finCarburant = document.getElementById('periode_fin_carburant').value;
    
    if (debutCarburant && finCarburant) {
        document.getElementById('periode_debut_maintenance').value = debutCarburant;
        document.getElementById('periode_fin_maintenance').value = finCarburant;
        document.getElementById('periode_debut_transport_terrestre').value = debutCarburant;
        document.getElementById('periode_fin_transport_terrestre').value = finCarburant;
        
        // Notification
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show mt-2';
        alert.innerHTML = '<i class="fas fa-check"></i> Périodes synchronisées avec le budget carburant !';
        document.querySelector('.card-body').insertBefore(alert, document.querySelector('form'));
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    } else {
        alert('Veuillez d\'abord définir les périodes du budget carburant');
    }
}

function resetDefaults() {
    if (confirm('Êtes-vous sûr de vouloir restaurer les valeurs par défaut ?')) {
        // Valeurs par défaut
        const defaults = {
            carburant: { budget: 30000, seuil: 3000 },
            maintenance: { budget: 25000, seuil: 2500 },
            transport_terrestre: { budget: 40000, seuil: 4000 }
        };
        
        Object.keys(defaults).forEach(type => {
            document.getElementById(`budget_initial_${type}`).value = defaults[type].budget;
            document.getElementById(`seuil_alerte_${type}`).value = defaults[type].seuil;
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Validation en temps réel
    const types = ['carburant', 'maintenance', 'transport_terrestre'];
    
    types.forEach(type => {
        const budgetInput = document.getElementById(`budget_initial_${type}`);
        const seuilInput = document.getElementById(`seuil_alerte_${type}`);
        
        function validateSeuil() {
            const budget = parseFloat(budgetInput.value) || 0;
            const seuil = parseFloat(seuilInput.value) || 0;
            
            if (seuil > budget) {
                seuilInput.setCustomValidity('Le seuil ne peut pas être supérieur au budget');
                seuilInput.classList.add('is-invalid');
            } else {
                seuilInput.setCustomValidity('');
                seuilInput.classList.remove('is-invalid');
            }
        }
        
        budgetInput.addEventListener('input', validateSeuil);
        seuilInput.addEventListener('input', validateSeuil);
    });
});
</script>
{% endblock %}