{% extends "base.html" %}

{% block title %}Modifier Opération Budget - GesParc Auto{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-edit text-primary"></i> Modifier l'Opération Budget
                </h1>
                <a href="{{ url_for('budget') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour au Budget
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-wallet"></i> Modification de l'Opération #{{ operation.id }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <!-- Type d'opération -->
                            <div class="col-md-6 mb-3">
                                <label for="type" class="form-label">
                                    <i class="fas fa-tag"></i> Type d'opération *
                                </label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">Sélectionnez le type</option>
                                    <option value="recette" {% if operation.type == 'recette' %}selected{% endif %}>
                                        Recette
                                    </option>
                                    <option value="depense" {% if operation.type == 'depense' %}selected{% endif %}>
                                        Dépense
                                    </option>
                                    <option value="reapprovisionnement" {% if operation.type == 'reapprovisionnement' %}selected{% endif %}>
                                        Réapprovisionnement
                                    </option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner le type d'opération
                                </div>
                            </div>

                            <!-- Montant -->
                            <div class="col-md-6 mb-3">
                                <label for="montant" class="form-label">
                                    <i class="fas fa-coins"></i> Montant (MAD) *
                                </label>
                                <input type="number" class="form-control" id="montant" 
                                       name="montant" min="0.01" step="0.01" required
                                       value="{{ operation.montant }}"
                                       placeholder="0.00">
                                <div class="invalid-feedback">
                                    Veuillez saisir un montant valide
                                </div>
                                <small class="form-text text-muted">
                                    Montant en dirhams marocains
                                </small>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Date et heure -->
                            <div class="col-md-6 mb-3">
                                <label for="date_operation" class="form-label">
                                    <i class="fas fa-calendar"></i> Date et heure *
                                </label>
                                <input type="datetime-local" class="form-control" id="date_operation"
                                       name="date_operation" required
                                       value="{{ operation.date_operation[:16] }}">
                                <div class="invalid-feedback">
                                    Veuillez saisir la date et heure
                                </div>
                            </div>

                            <!-- Statut -->
                            <div class="col-md-6 mb-3">
                                <label for="statut" class="form-label">
                                    <i class="fas fa-flag"></i> Statut *
                                </label>
                                <select class="form-select" id="statut" name="statut" required>
                                    <option value="valide" {% if operation.statut == 'valide' %}selected{% endif %}>
                                        Valide
                                    </option>
                                    <option value="en_attente" {% if operation.statut == 'en_attente' %}selected{% endif %}>
                                        En attente
                                    </option>
                                    <option value="annule" {% if operation.statut == 'annule' %}selected{% endif %}>
                                        Annulé
                                    </option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner le statut
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Catégorie -->
                            <div class="col-md-6 mb-3">
                                <label for="categorie" class="form-label">
                                    <i class="fas fa-folder"></i> Catégorie
                                </label>
                                <select class="form-select" id="categorie" name="categorie">
                                    <option value="">Sélectionnez une catégorie</option>
                                    <option value="Maintenance" {% if operation.categorie == 'Maintenance' %}selected{% endif %}>Maintenance</option>
                                    <option value="Carburant" {% if operation.categorie == 'Carburant' %}selected{% endif %}>Carburant</option>
                                    <option value="Assurance" {% if operation.categorie == 'Assurance' %}selected{% endif %}>Assurance</option>
                                    <option value="Réparation" {% if operation.categorie == 'Réparation' %}selected{% endif %}>Réparation</option>
                                    <option value="Pièces détachées" {% if operation.categorie == 'Pièces détachées' %}selected{% endif %}>Pièces détachées</option>
                                    <option value="Formation" {% if operation.categorie == 'Formation' %}selected{% endif %}>Formation</option>
                                    <option value="Équipement" {% if operation.categorie == 'Équipement' %}selected{% endif %}>Équipement</option>
                                    <option value="Réapprovisionnement" {% if operation.categorie == 'Réapprovisionnement' %}selected{% endif %}>Réapprovisionnement</option>
                                    <option value="Initial" {% if operation.categorie == 'Initial' %}selected{% endif %}>Initial</option>
                                    <option value="Mensuel" {% if operation.categorie == 'Mensuel' %}selected{% endif %}>Mensuel</option>
                                    <option value="Autre" {% if operation.categorie == 'Autre' %}selected{% endif %}>Autre</option>
                                </select>
                                <small class="form-text text-muted">
                                    Catégorie pour le classement
                                </small>
                            </div>

                            <!-- Référence -->
                            <div class="col-md-6 mb-3">
                                <label for="reference" class="form-label">
                                    <i class="fas fa-hashtag"></i> Référence
                                </label>
                                <input type="text" class="form-control" id="reference" 
                                       name="reference" maxlength="50"
                                       value="{{ operation.reference or '' }}"
                                       placeholder="REF-001">
                                <small class="form-text text-muted">
                                    Numéro de référence ou de facture
                                </small>
                            </div>
                        </div>

                        <!-- Commentaire -->
                        <div class="mb-3">
                            <label for="commentaire" class="form-label">
                                <i class="fas fa-comment"></i> Commentaire
                            </label>
                            <textarea class="form-control" id="commentaire" name="commentaire" 
                                      rows="3" maxlength="500"
                                      placeholder="Description de l'opération...">{{ operation.commentaire or '' }}</textarea>
                            <small class="form-text text-muted">
                                Description détaillée de l'opération (optionnel)
                            </small>
                        </div>

                        <!-- Informations de suivi -->
                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle"></i> Informations de suivi
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">Créé le :</small><br>
                                        <strong>{{ operation.created_at[:16].replace('T', ' ') }}</strong>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">Modifié le :</small><br>
                                        <strong>{{ operation.updated_at[:16].replace('T', ' ') }}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('budget') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer les Modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mise à jour de l'icône selon le type sélectionné
    const typeSelect = document.getElementById('type');
    const montantInput = document.getElementById('montant');
    const statutSelect = document.getElementById('statut');
    
    function updateTypeStyle() {
        const selectedType = typeSelect.value;
        const montantLabel = document.querySelector('label[for="montant"]');
        
        if (selectedType === 'recette' || selectedType === 'reapprovisionnement') {
            montantInput.style.borderColor = '#28a745';
            montantLabel.innerHTML = '<i class="fas fa-coins text-success"></i> Montant (MAD) *';
        } else if (selectedType === 'depense') {
            montantInput.style.borderColor = '#dc3545';
            montantLabel.innerHTML = '<i class="fas fa-coins text-danger"></i> Montant (MAD) *';
        } else {
            montantInput.style.borderColor = '';
            montantLabel.innerHTML = '<i class="fas fa-coins"></i> Montant (MAD) *';
        }
    }
    
    function updateStatutStyle() {
        const selectedStatut = statutSelect.value;
        
        if (selectedStatut === 'valide') {
            statutSelect.style.borderColor = '#28a745';
        } else if (selectedStatut === 'annule') {
            statutSelect.style.borderColor = '#dc3545';
        } else {
            statutSelect.style.borderColor = '#ffc107';
        }
    }
    
    // Initialiser les styles
    updateTypeStyle();
    updateStatutStyle();
    
    // Écouter les changements
    typeSelect.addEventListener('change', updateTypeStyle);
    statutSelect.addEventListener('change', updateStatutStyle);
});
</script>
{% endblock %}