#!/usr/bin/env python3
"""
Démonstration des nouvelles fonctionnalités d'analytics avancées
Génère des exemples de graphiques et sauvegarde les images
"""

import os
import base64
from matplotlib_analytics import GesparcAnalytics
from datetime import datetime

def save_chart_as_png(chart_data, filename):
    """Sauvegarde un graphique base64 en fichier PNG"""
    try:
        img_data = base64.b64decode(chart_data)
        with open(filename, 'wb') as f:
            f.write(img_data)
        return True
    except Exception as e:
        print(f"Erreur sauvegarde {filename}: {e}")
        return False

def demo_analytics_avancees():
    """Démonstration complète des analytics avancées"""
    print("🎨 Démonstration des Analytics Avancées GesParc Auto")
    print("=" * 60)
    
    # Créer le dossier de sortie
    output_dir = "demo_analytics_output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 Dossier créé: {output_dir}")
    
    # Initialiser le générateur
    analytics = GesparcAnalytics()
    print("✅ Générateur d'analytics initialisé")
    
    # Générer tous les graphiques
    print("\n🔄 Génération de tous les graphiques...")
    
    charts_info = [
        ("evolution", "Évolution des Maintenances", "evolution_maintenances.png"),
        ("dashboard", "Dashboard Véhicules", "dashboard_vehicules.png"),
        ("costs", "Analyse des Coûts", "analyse_couts.png"),
        ("heatmap", "Heatmap de Performance", "heatmap_performance.png"),
        ("predictive", "Analyse Prédictive", "analyse_predictive.png"),
        ("advanced_fleet", "Analytics Avancés Flotte", "analytics_avances_flotte.png"),
        ("financial", "Dashboard Financier", "dashboard_financier.png"),
        ("correlation", "Analyse de Corrélation", "analyse_correlation.png"),
        ("operational", "Efficacité Opérationnelle", "efficacite_operationnelle.png")
    ]
    
    generated_charts = []
    
    for method_suffix, description, filename in charts_info:
        print(f"\n  📊 Génération: {description}")
        try:
            # Appeler la méthode correspondante
            if method_suffix == "evolution":
                chart_data = analytics.create_maintenance_evolution_chart()
            elif method_suffix == "dashboard":
                chart_data = analytics.create_vehicle_analysis_dashboard()
            elif method_suffix == "costs":
                chart_data = analytics.create_maintenance_cost_analysis()
            elif method_suffix == "heatmap":
                chart_data = analytics.create_performance_heatmap()
            elif method_suffix == "predictive":
                chart_data = analytics.create_predictive_analysis()
            elif method_suffix == "advanced_fleet":
                chart_data = analytics.create_advanced_fleet_analytics()
            elif method_suffix == "financial":
                chart_data = analytics.create_financial_dashboard()
            elif method_suffix == "correlation":
                chart_data = analytics.create_correlation_analysis()
            elif method_suffix == "operational":
                chart_data = analytics.create_operational_efficiency_dashboard()
            
            # Sauvegarder le graphique
            filepath = os.path.join(output_dir, filename)
            if save_chart_as_png(chart_data, filepath):
                print(f"    ✅ Sauvegardé: {filepath}")
                generated_charts.append((description, filepath, len(chart_data)))
            else:
                print(f"    ❌ Erreur sauvegarde: {filename}")
                
        except Exception as e:
            print(f"    ❌ Erreur génération {description}: {e}")
    
    # Générer le rapport complet
    print(f"\n📋 Génération du rapport complet...")
    try:
        comprehensive_report = analytics.create_comprehensive_report()
        
        # Sauvegarder chaque graphique du rapport
        for chart_name, chart_data in comprehensive_report.items():
            if chart_data and len(chart_data) > 100:
                filename = f"rapport_{chart_name}.png"
                filepath = os.path.join(output_dir, filename)
                if save_chart_as_png(chart_data, filepath):
                    print(f"  ✅ Rapport {chart_name}: {filepath}")
        
        print("  ✅ Rapport complet généré")
        
    except Exception as e:
        print(f"  ❌ Erreur rapport complet: {e}")
    
    # Résumé de la démonstration
    print(f"\n📈 Résumé de la Démonstration:")
    print(f"  🎯 Graphiques générés: {len(generated_charts)}")
    print(f"  📁 Dossier de sortie: {output_dir}")
    print(f"  📅 Date génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if generated_charts:
        print(f"\n📊 Détails des graphiques générés:")
        total_size = 0
        for description, filepath, size in generated_charts:
            size_mb = size / (1024 * 1024)
            total_size += size
            print(f"    📈 {description}")
            print(f"       📄 Fichier: {os.path.basename(filepath)}")
            print(f"       📏 Taille: {size_mb:.2f} MB")
        
        total_size_mb = total_size / (1024 * 1024)
        print(f"\n  📦 Taille totale: {total_size_mb:.2f} MB")
    
    # Instructions d'utilisation
    print(f"\n🚀 Instructions d'utilisation:")
    print(f"  1. Ouvrez les fichiers PNG dans {output_dir}")
    print(f"  2. Consultez les analytics dans l'application web")
    print(f"  3. Utilisez l'API REST pour intégrations")
    print(f"  4. Personnalisez les analyses selon vos besoins")
    
    # Informations techniques
    print(f"\n🔧 Informations techniques:")
    print(f"  📦 Backend: Python + Matplotlib + Seaborn")
    print(f"  🎨 Styles: Palette GesParc personnalisée")
    print(f"  📊 Formats: PNG haute résolution (300 DPI)")
    print(f"  🔗 API: Routes REST pour chaque graphique")
    print(f"  💾 Base64: Intégration web directe")
    
    print(f"\n🎉 Démonstration terminée avec succès!")
    print(f"✅ Les analytics avancées sont prêtes pour la production!")

if __name__ == '__main__':
    demo_analytics_avancees()
