{% extends "base.html" %}

{% block title %}Terminer Affectation - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-stop"></i> Terminer l'Affectation</h1>
            <a href="{{ url_for('affectations') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Confirmation de fin d'affectation
                </h5>
            </div>
            <div class="card-body">
                <!-- Informations de l'affectation -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Détails de l'affectation</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Véhicule:</strong><br>
                            <span class="text-primary">{{ affectation.immatriculation }}</span><br>
                            <small class="text-muted">{{ affectation.marque }} {{ affectation.modele }}</small>
                        </div>
                        <div class="col-md-4">
                            <strong>Conducteur:</strong><br>
                            <span class="text-primary">{{ affectation.prenom }} {{ affectation.nom }}</span><br>
                            <small class="text-muted">Depuis le {{ affectation.date_debut | format_datetime }}</small>
                        </div>
                        <div class="col-md-4">
                            <strong>Kilométrage actuel:</strong><br>
                            <span class="text-success fs-5">
                                <i class="fas fa-tachometer-alt"></i>
                                {{ '{:,}'.format(affectation.kilometrage or 0).replace(',', ' ') }} km
                            </span><br>
                            <small class="text-muted">Kilométrage de référence</small>
                        </div>
                    </div>
                    {% if affectation.commentaire %}
                    <hr>
                    <strong>Commentaire initial:</strong><br>
                    <em>{{ affectation.commentaire }}</em>
                    {% endif %}
                </div>

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Date de fin -->
                        <div class="col-md-6 mb-3">
                            <label for="date_fin" class="form-label">
                                <i class="fas fa-calendar-minus"></i> Date et heure de fin *
                            </label>
                            <input type="datetime-local" class="form-control" id="date_fin" 
                                   name="date_fin" required
                                   value="{{ datetime_fin_default }}"
                                   min="{{ affectation.date_debut }}"
                                   max="{{ datetime_fin_default }}">
                            <div class="invalid-feedback">
                                <i class="fas fa-exclamation-circle"></i>
                                Veuillez saisir la date et heure de fin d'affectation
                            </div>
                            <small class="form-text text-muted">
                                Date effective de fin d'affectation
                            </small>
                        </div>

                        <!-- Kilométrage final -->
                        <div class="col-md-6 mb-3">
                            <label for="kilometrage_final" class="form-label">
                                <i class="fas fa-tachometer-alt"></i> Kilométrage final (km) *
                            </label>
                            <input type="number" class="form-control" id="kilometrage_final"
                                   name="kilometrage_final" min="{{ affectation.kilometrage or 0 }}"
                                   required
                                   placeholder="Doit être > {{ '{:,}'.format(affectation.kilometrage or 0).replace(',', ' ') }} km">
                            <div class="invalid-feedback">
                                Le kilométrage final doit être supérieur au kilométrage actuel ({{ '{:,}'.format(affectation.kilometrage or 0).replace(',', ' ') }} km)
                            </div>
                            <small class="form-text text-muted">
                                <strong>Kilométrage actuel:</strong> {{ '{:,}'.format(affectation.kilometrage or 0).replace(',', ' ') }} km<br>
                                Le kilométrage final doit être supérieur au kilométrage actuel
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Motif de fin -->
                        <div class="col-md-6 mb-3">
                            <label for="motif_fin" class="form-label">
                                <i class="fas fa-clipboard-list"></i> Motif de fin
                            </label>
                            <select class="form-select" id="motif_fin" name="motif_fin">
                                <option value="fin_mission" selected>Fin de mission</option>
                                <option value="changement_conducteur">Changement de conducteur</option>
                                <option value="maintenance_vehicule">Maintenance du véhicule</option>
                                <option value="demande_conducteur">Demande du conducteur</option>
                                <option value="reorganisation">Réorganisation du parc</option>
                                <option value="autre">Autre</option>
                            </select>
                            <small class="form-text text-muted">
                                Raison de la fin d'affectation
                            </small>
                        </div>

                        <!-- État du véhicule -->
                        <div class="col-md-6 mb-3">
                            <label for="etat_vehicule" class="form-label">
                                <i class="fas fa-car"></i> État du véhicule
                            </label>
                            <select class="form-select" id="etat_vehicule" name="etat_vehicule">
                                <option value="bon">Bon état</option>
                                <option value="moyen">État moyen</option>
                                <option value="maintenance_requise">Maintenance requise</option>
                                <option value="reparation_requise">Réparation requise</option>
                            </select>
                            <small class="form-text text-muted">
                                État du véhicule à la restitution
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Commentaires de fin -->
                        <div class="col-12 mb-3">
                            <label for="commentaire_fin" class="form-label">
                                <i class="fas fa-comment"></i> Commentaires de fin
                            </label>
                            <textarea class="form-control" id="commentaire_fin" name="commentaire_fin" 
                                      rows="3" placeholder="Observations, incidents, recommandations..."></textarea>
                            <small class="form-text text-muted">
                                Observations particulières sur cette affectation
                            </small>
                        </div>
                    </div>

                    <!-- Résumé des changements -->
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Conséquences de cette action</h6>
                        <ul class="mb-0">
                            <li>L'affectation sera marquée comme <strong>terminée</strong></li>
                            <li>Le véhicule <strong>{{ affectation.immatriculation }}</strong> redeviendra <strong>disponible</strong></li>
                            <li>Le conducteur <strong>{{ affectation.prenom }} {{ affectation.nom }}</strong> n'aura plus de véhicule affecté</li>
                            <li>Cette action est <strong>irréversible</strong></li>
                        </ul>
                    </div>

                    <!-- Boutons -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('affectations') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-stop"></i> Terminer l'affectation
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    const kilometrageFinal = document.getElementById('kilometrage_final');
    const kilometrageActuel = {{ affectation.kilometrage or 0 }};

    // Validation en temps réel du kilométrage
    kilometrageFinal.addEventListener('input', function() {
        const valeur = parseInt(this.value) || 0;

        if (valeur > 0 && valeur < kilometrageActuel) {
            this.setCustomValidity(`Le kilométrage final (${valeur.toLocaleString()} km) doit être supérieur au kilométrage actuel (${kilometrageActuel.toLocaleString()} km)`);
            this.classList.add('is-invalid');
            this.classList.remove('is-valid');
        } else if (valeur >= kilometrageActuel) {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid', 'is-valid');
        }
    });

    form.addEventListener('submit', function(event) {
        // Laisser la validation HTML5 native faire son travail d'abord
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
            form.classList.add('was-validated');
            
            // Vérifier spécifiquement le champ date
            const dateFin = document.getElementById('date_fin');
            if (!dateFin.value) {
                dateFin.focus();
                showDateError();
            }
            return false;
        }
        
        // Validation supplémentaire du kilométrage
        const kmFinal = parseInt(kilometrageFinal.value) || 0;

        if (kmFinal > 0 && kmFinal < kilometrageActuel) {
            event.preventDefault();
            event.stopPropagation();

            // Afficher une alerte personnalisée
            showKilometrageError(kmFinal, kilometrageActuel);
            return false;
        }

        // Si tout est valide, marquer le formulaire comme validé
        form.classList.add('was-validated');
    });
    
    // Calcul automatique du coût carburant
    const carburantConsomme = document.getElementById('carburant_consomme');
    const coutCarburant = document.getElementById('cout_carburant');
    
    // Prix moyen du carburant au Maroc (à ajuster selon les prix actuels)
    const prixLitreCarburant = 14.5; // MAD par litre (exemple)
    
    carburantConsomme.addEventListener('input', function() {
        const litres = parseFloat(this.value) || 0;
        const cout = litres * prixLitreCarburant;
        coutCarburant.value = cout.toFixed(2);
    });
    
    // Validation des dates
    const dateFin = document.getElementById('date_fin');
    const today = new Date().toISOString().slice(0, 16); // Format datetime-local
    dateFin.max = today;
    
    // Validation simple en temps réel de la date de fin
    dateFin.addEventListener('input', function() {
        if (this.value) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else {
            this.classList.remove('is-valid');
        }
    });
    
    // Affichage conditionnel pour "autre" motif
    const motifSelect = document.getElementById('motif_fin');
    motifSelect.addEventListener('change', function() {
        const commentaireField = document.getElementById('commentaire_fin');
        if (this.value === 'autre') {
            commentaireField.required = true;
            commentaireField.placeholder = 'Veuillez préciser le motif de fin d\'affectation...';
        } else {
            commentaireField.required = false;
            commentaireField.placeholder = 'Observations, incidents, recommandations...';
        }
    });
});

// Fonction pour afficher l'erreur de date manquante
function showDateError() {
    // Créer une modal d'erreur pour la date
    const modalHtml = `
        <div class="modal fade" id="dateErrorModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-warning">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="fas fa-calendar-times"></i> Date de fin requise
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning d-flex align-items-center">
                            <i class="fas fa-calendar-alt fa-2x me-3"></i>
                            <div>
                                <h6 class="mb-1">Date de fin manquante</h6>
                                <p class="mb-0">
                                    Veuillez saisir la <strong>date et heure de fin</strong> d'affectation avant de continuer.
                                </p>
                            </div>
                        </div>
                        <div class="bg-light p-3 rounded">
                            <h6><i class="fas fa-info-circle text-info"></i> Information</h6>
                            <p class="mb-0 small">
                                La date de fin est obligatoire pour terminer une affectation. 
                                Elle doit être postérieure à la date de début et ne peut pas être dans le futur.
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                            <i class="fas fa-check"></i> Compris
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Supprimer l'ancienne modal si elle existe
    const existingModal = document.getElementById('dateErrorModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Ajouter la nouvelle modal au DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Afficher la modal
    const modal = new bootstrap.Modal(document.getElementById('dateErrorModal'));
    modal.show();
    
    // Nettoyer après fermeture
    document.getElementById('dateErrorModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Fonction pour afficher l'erreur de kilométrage
function showKilometrageError(kmFinal, kmActuel) {
    // Créer une modal d'erreur personnalisée
    const modalHtml = `
        <div class="modal fade" id="kilometrageErrorModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-danger">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-exclamation-triangle"></i> Erreur de Kilométrage
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger d-flex align-items-center">
                            <i class="fas fa-tachometer-alt fa-2x me-3"></i>
                            <div>
                                <h6 class="mb-1">Kilométrage invalide</h6>
                                <p class="mb-0">
                                    Le kilométrage final <strong>${kmFinal.toLocaleString()} km</strong>
                                    doit être supérieur au kilométrage actuel <strong>${kmActuel.toLocaleString()} km</strong>.
                                </p>
                            </div>
                        </div>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-muted">Kilométrage Actuel</h6>
                                        <h4 class="text-primary">${kmActuel.toLocaleString()} km</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-muted">Kilométrage Saisi</h6>
                                        <h4 class="text-danger">${kmFinal.toLocaleString()} km</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                            <i class="fas fa-edit"></i> Corriger le kilométrage
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Ajouter la modal au DOM si elle n'existe pas
    if (!document.getElementById('kilometrageErrorModal')) {
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // Afficher la modal
    const modal = new bootstrap.Modal(document.getElementById('kilometrageErrorModal'));
    modal.show();

    // Focus sur le champ kilométrage quand la modal se ferme
    document.getElementById('kilometrageErrorModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('kilometrage_final').focus();
        document.getElementById('kilometrage_final').select();
    });
}
</script>
{% endblock %}
