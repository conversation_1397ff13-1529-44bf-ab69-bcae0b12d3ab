<?php
/**
 * Proxy PHP pour GesParc Auto
 * Redirige les requêtes de localhost/gesparc vers l'application Flask
 */

// Configuration
$FLASK_HOST = '127.0.0.1';
$FLASK_PORT = 5001;
$FLASK_URL = "http://{$FLASK_HOST}:{$FLASK_PORT}";

// Fonction pour vérifier si Flask est accessible
function isFlaskRunning($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 3);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode === 200;
}

// Fonction pour faire le proxy de la requête
function proxyRequest($flaskUrl, $path = '') {
    $url = $flaskUrl . $path;
    
    // Préparer les headers
    $headers = [];
    foreach (getallheaders() as $name => $value) {
        if (strtolower($name) !== 'host') {
            $headers[] = "$name: $value";
        }
    }
    
    // Initialiser cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    // Méthode HTTP
    $method = $_SERVER['REQUEST_METHOD'];
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, file_get_contents('php://input'));
    } elseif ($method !== 'GET') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        if (in_array($method, ['PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, file_get_contents('php://input'));
        }
    }
    
    // Exécuter la requête
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    curl_close($ch);
    
    if ($response === false) {
        return false;
    }
    
    // Séparer headers et body
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    // Envoyer les headers (en filtrant certains)
    $headerLines = explode("\r\n", $headers);
    foreach ($headerLines as $header) {
        if (strpos($header, ':') !== false) {
            $headerParts = explode(':', $header, 2);
            $headerName = strtolower(trim($headerParts[0]));
            
            // Filtrer certains headers
            if (!in_array($headerName, ['transfer-encoding', 'connection', 'server'])) {
                header($header);
            }
        }
    }
    
    // Définir le code de statut
    http_response_code($httpCode);
    
    // Modifier les URLs dans le contenu HTML pour qu'elles pointent vers /gesparc
    if (strpos($_SERVER['HTTP_ACCEPT'] ?? '', 'text/html') !== false) {
        $body = str_replace('href="/', 'href="/gesparc/', $body);
        $body = str_replace('src="/', 'src="/gesparc/', $body);
        $body = str_replace('action="/', 'action="/gesparc/', $body);
        $body = str_replace("url_for('", "url_for('", $body); // Flask url_for reste inchangé
    }
    
    echo $body;
    return true;
}

// Obtenir le chemin demandé
$requestPath = $_SERVER['REQUEST_URI'];
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = dirname($scriptName);

// Enlever le préfixe /gesparc du chemin
if (strpos($requestPath, '/gesparc') === 0) {
    $flaskPath = substr($requestPath, 7); // Enlever '/gesparc'
} else {
    $flaskPath = $requestPath;
}

// Si c'est la racine, rediriger vers la page d'accueil Flask
if ($flaskPath === '' || $flaskPath === '/') {
    $flaskPath = '/';
}

// Vérifier si Flask est accessible
if (!isFlaskRunning($FLASK_URL)) {
    // Flask n'est pas accessible, afficher la page d'erreur
    ?>
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>GesParc Auto - Service Non Disponible</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .error-container {
                min-height: 100vh;
                display: flex;
                align-items: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }
            .error-card {
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                border-radius: 15px;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-6">
                        <div class="card error-card">
                            <div class="card-header bg-warning text-dark text-center">
                                <h2 class="mb-0">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Service Non Disponible
                                </h2>
                            </div>
                            <div class="card-body p-4">
                                <div class="text-center mb-4">
                                    <i class="fas fa-car fa-4x text-primary mb-3"></i>
                                    <h4>GesParc Auto</h4>
                                    <p class="text-muted">L'application n'est pas démarrée</p>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <strong>L'application Flask GesParc Auto n'est pas accessible.</strong><br>
                                    Veuillez démarrer le service Flask pour continuer.
                                </div>
                                
                                <h5><i class="fas fa-play-circle"></i> Pour démarrer l'application :</h5>
                                <ol class="mb-4">
                                    <li>Ouvrez un terminal/invite de commandes</li>
                                    <li>Naviguez vers : <code class="bg-light p-1">cd c:\Apache24\htdocs\gesparc</code></li>
                                    <li>Lancez : <code class="bg-light p-1">python gesparc_app.py</code></li>
                                    <li>Rafraîchissez cette page</li>
                                </ol>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <button onclick="location.reload()" class="btn btn-primary">
                                        <i class="fas fa-sync-alt"></i> Vérifier à nouveau
                                    </button>
                                    <a href="http://localhost:5001" class="btn btn-outline-primary" target="_blank">
                                        <i class="fas fa-external-link-alt"></i> Accès direct
                                    </a>
                                </div>
                                
                                <hr>
                                <div class="text-center">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Scripts de démarrage disponibles : 
                                        <code>start_gesparc.bat</code> | <code>start_gesparc.ps1</code>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            // Auto-refresh toutes les 10 secondes
            setTimeout(function() {
                location.reload();
            }, 10000);
        </script>
    </body>
    </html>
    <?php
    exit;
}

// Flask est accessible, faire le proxy
if (!proxyRequest($FLASK_URL, $flaskPath)) {
    http_response_code(502);
    echo "Erreur de proxy vers l'application Flask";
}
?>
