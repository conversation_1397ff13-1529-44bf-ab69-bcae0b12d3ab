#!/usr/bin/env python3
"""
Test de la route rapports pour identifier l'erreur
"""

import sqlite3
from datetime import datetime

def test_route_rapports():
    """Simule exactement ce que fait la route rapports"""
    print("🔍 Test de la Route Rapports")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('parc_automobile.db')
        
        # Statistiques générales
        stats = {}
        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]
        stats['total_conducteurs'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]
        stats['total_maintenances'] = conn.execute('SELECT COUNT(*) FROM maintenances').fetchone()[0]
        stats['total_affectations'] = conn.execute('SELECT COUNT(*) FROM affectations').fetchone()[0]
        print(f"✅ Statistiques générales: {stats}")
        
        # Statistiques détaillées
        stats['vehicules_disponibles'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'").fetchone()[0]
        stats['vehicules_en_maintenance'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'en_maintenance'").fetchone()[0]
        stats['vehicules_affectes'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'affecte'").fetchone()[0]
        stats['maintenances_planifiees'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'planifiee'").fetchone()[0]
        stats['maintenances_en_cours'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'en_cours'").fetchone()[0]
        stats['maintenances_terminees'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'terminee'").fetchone()[0]
        stats['affectations_actives'] = conn.execute("SELECT COUNT(*) FROM affectations WHERE statut = 'active'").fetchone()[0]
        stats['conducteurs_actifs'] = conn.execute("SELECT COUNT(*) FROM conducteurs WHERE statut = 'actif'").fetchone()[0]
        print(f"✅ Statistiques détaillées OK")
        
        # Coûts de maintenance et analyses financières
        cout_total_result = conn.execute('SELECT SUM(cout) FROM maintenances WHERE cout IS NOT NULL').fetchone()
        stats['cout_total_maintenances'] = cout_total_result[0] if cout_total_result[0] else 0
        
        cout_mois_result = conn.execute('''
            SELECT SUM(cout) FROM maintenances 
            WHERE cout IS NOT NULL 
            AND date_maintenance >= date('now', 'start of month')
        ''').fetchone()
        stats['cout_mois_maintenances'] = cout_mois_result[0] if cout_mois_result[0] else 0
        print(f"✅ Coûts OK")
        
        # Coût moyen par véhicule et par maintenance
        stats['cout_moyen_vehicule'] = (stats['cout_total_maintenances'] / stats['total_vehicules']) if stats['total_vehicules'] > 0 else 0
        stats['cout_moyen_maintenance'] = (stats['cout_total_maintenances'] / stats['total_maintenances']) if stats['total_maintenances'] > 0 else 0
        print(f"✅ Coûts moyens OK")
        
        # Analyses de performance
        stats['taux_disponibilite'] = (stats['vehicules_disponibles'] / stats['total_vehicules'] * 100) if stats['total_vehicules'] > 0 else 0
        stats['taux_utilisation'] = (stats['vehicules_affectes'] / stats['total_vehicules'] * 100) if stats['total_vehicules'] > 0 else 0
        stats['taux_maintenance'] = (stats['vehicules_en_maintenance'] / stats['total_vehicules'] * 100) if stats['total_vehicules'] > 0 else 0
        print(f"✅ Taux de performance OK")
        
        # Kilométrage moyen
        km_moyen_result = conn.execute('SELECT AVG(kilometrage) FROM vehicules WHERE kilometrage > 0').fetchone()
        stats['kilometrage_moyen'] = km_moyen_result[0] if km_moyen_result[0] else 0
        print(f"✅ Kilométrage moyen OK")
        
        # Âge moyen du parc
        age_moyen_result = conn.execute('SELECT AVG(2025 - annee) FROM vehicules').fetchone()
        stats['age_moyen_parc'] = age_moyen_result[0] if age_moyen_result[0] else 0
        print(f"✅ Âge moyen OK")
        
        # Répartition par statut des véhicules
        vehicules_par_statut = conn.execute('''
            SELECT statut, COUNT(*) as count
            FROM vehicules
            GROUP BY statut
            ORDER BY count DESC
        ''').fetchall()
        print(f"✅ Véhicules par statut: {len(vehicules_par_statut)} groupes")

        # Répartition par carburant
        vehicules_par_carburant = conn.execute('''
            SELECT carburant, COUNT(*) as count
            FROM vehicules
            WHERE carburant IS NOT NULL AND carburant != ''
            GROUP BY carburant
            ORDER BY count DESC
        ''').fetchall()
        print(f"✅ Véhicules par carburant: {len(vehicules_par_carburant)} groupes")

        # Répartition par marque
        vehicules_par_marque = conn.execute('''
            SELECT marque, COUNT(*) as count
            FROM vehicules
            WHERE marque IS NOT NULL AND marque != ''
            GROUP BY marque
            ORDER BY count DESC
            LIMIT 10
        ''').fetchall()
        print(f"✅ Véhicules par marque: {len(vehicules_par_marque)} marques")

        # Maintenances par mois (12 derniers mois)
        maintenances_par_mois = conn.execute('''
            SELECT strftime('%Y-%m', date_maintenance) as mois, 
                   COUNT(*) as count,
                   SUM(CASE WHEN cout IS NOT NULL THEN cout ELSE 0 END) as cout_total
            FROM maintenances
            WHERE date_maintenance >= date('now', '-12 months')
            GROUP BY strftime('%Y-%m', date_maintenance)
            ORDER BY mois
        ''').fetchall()
        print(f"✅ Maintenances par mois: {len(maintenances_par_mois)} mois")
        
        # Maintenances par type
        maintenances_par_type = conn.execute('''
            SELECT type_maintenance, COUNT(*) as count,
                   AVG(CASE WHEN cout IS NOT NULL THEN cout ELSE 0 END) as cout_moyen
            FROM maintenances
            GROUP BY type_maintenance
            ORDER BY count DESC
        ''').fetchall()
        print(f"✅ Maintenances par type: {len(maintenances_par_type)} types")
        
        # Top 5 véhicules avec le plus de maintenances
        vehicules_maintenances = conn.execute('''
            SELECT v.immatriculation, v.marque, v.modele, COUNT(m.id) as nb_maintenances,
                   SUM(CASE WHEN m.cout IS NOT NULL THEN m.cout ELSE 0 END) as cout_total
            FROM vehicules v
            LEFT JOIN maintenances m ON v.id = m.vehicule_id
            GROUP BY v.id
            ORDER BY nb_maintenances DESC, cout_total DESC
            LIMIT 5
        ''').fetchall()
        print(f"✅ Top véhicules maintenances: {len(vehicules_maintenances)} véhicules")
        
        # Conducteurs avec véhicules affectés
        conducteurs_actifs = conn.execute('''
            SELECT c.nom, c.prenom, v.immatriculation, v.marque, v.modele, a.date_debut
            FROM conducteurs c
            JOIN affectations a ON c.id = a.conducteur_id
            JOIN vehicules v ON a.vehicule_id = v.id
            WHERE a.statut = 'active'
            ORDER BY c.nom, c.prenom
        ''').fetchall()
        print(f"✅ Conducteurs actifs: {len(conducteurs_actifs)} conducteurs")
        
        # Alertes simplifiées
        alertes = []
        predictions = []
        
        if stats['vehicules_en_maintenance'] > 0:
            alertes.append({
                'type': 'maintenance_en_cours',
                'niveau': 'info',
                'message': f'{stats["vehicules_en_maintenance"]} véhicule(s) en maintenance',
                'vehicule': 'Multiple'
            })
        
        if stats['taux_disponibilite'] < 70:
            alertes.append({
                'type': 'disponibilite_faible',
                'niveau': 'warning',
                'message': f'Taux de disponibilité faible: {stats["taux_disponibilite"]:.1f}%',
                'vehicule': 'Parc'
            })
        
        stats['alertes'] = alertes
        stats['predictions'] = predictions
        stats['nb_alertes'] = len(alertes)
        stats['nb_predictions'] = len(predictions)
        print(f"✅ Alertes et prédictions: {len(alertes)} alertes, {len(predictions)} prédictions")
        
        conn.close()
        
        print(f"\n✅ Toutes les requêtes de la route rapports fonctionnent")
        print(f"📊 Données finales:")
        print(f"   - Stats: {len(stats)} éléments")
        print(f"   - Véhicules par statut: {len(vehicules_par_statut)}")
        print(f"   - Véhicules par carburant: {len(vehicules_par_carburant)}")
        print(f"   - Alertes: {len(alertes)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur dans la route rapports: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_route_rapports()
