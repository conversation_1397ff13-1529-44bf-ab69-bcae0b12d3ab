# Configuration Apache pour GesParc Auto
# À inclure dans httpd.conf ou à placer dans conf/extra/

# Activer les modules nécessaires
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule proxy_module modules/mod_proxy.so
LoadModule proxy_http_module modules/mod_proxy_http.so

# Configuration du répertoire gesparc
<Directory "c:/Apache24/htdocs/gesparc">
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
    
    # Variables d'environnement
    SetEnv SCRIPT_NAME /gesparc
    SetEnv APPLICATION_ROOT /gesparc
    
    # Index files
    DirectoryIndex index.html index.php proxy.php
</Directory>

# Méthode 1: Proxy vers Flask (Recommandée)
# Rediriger /gesparc vers l'application Flask
ProxyPreserveHost On
ProxyPass /gesparc/static !
ProxyPass /gesparc http://127.0.0.1:5001/gesparc
ProxyPassReverse /gesparc http://127.0.0.1:5001/

# Servir les fichiers statiques directement par Apache
Alias /gesparc/static "c:/Apache24/htdocs/gesparc/static"
<Directory "c:/Apache24/htdocs/gesparc/static">
    Options -Indexes
    AllowOverride None
    Require all granted
    
    # Cache pour les fichiers statiques
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/ico "access plus 1 month"
    </IfModule>
</Directory>

# Sécurité - Bloquer l'accès aux fichiers sensibles
<LocationMatch "^/gesparc/.*\.(py|db|wsgi|log)$">
    Require all denied
</LocationMatch>

# Headers de sécurité
<IfModule mod_headers.c>
    <Location "/gesparc">
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options SAMEORIGIN
        Header always set X-XSS-Protection "1; mode=block"
    </Location>
</IfModule>
