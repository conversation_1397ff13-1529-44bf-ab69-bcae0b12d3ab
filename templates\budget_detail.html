{% extends "base.html" %}

{% block title %}{{ config.nom_budget }} - GesParc Auto{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    {% if type_budget == 'carburant' %}
                        <i class="fas fa-gas-pump text-primary"></i>
                    {% elif type_budget == 'maintenance' %}
                        <i class="fas fa-tools text-warning"></i>
                    {% else %}
                        <i class="fas fa-truck text-info"></i>
                    {% endif %}
                    {{ config.nom_budget }}
                </h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('budget') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                    <a href="{{ url_for('ajouter_operation_budget', type_budget=type_budget) }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Nouvelle Opération
                    </a>
                    <a href="{{ url_for('reapprovisionner_budget', type_budget=type_budget) }}" class="btn btn-primary">
                        <i class="fas fa-money-bill-wave"></i> Réapprovisionner
                    </a>
                    <a href="{{ url_for('config_budget', type_budget=type_budget) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog"></i> Configuration
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau de bord financier -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Recettes Totales</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(recettes).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-up fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Dépenses Totales</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(depenses).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card {% if solde_actuel >= config.seuil_alerte %}bg-info{% else %}bg-warning{% endif %} text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Solde Actuel</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(solde_actuel).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-balance-scale fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Budget Initial</h6>
                            <h4 class="mb-0">{{ '{:,.2f}'.format(config.budget_initial).replace(',', ' ') }} MAD</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-piggy-bank fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barre de progression et alerte -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Utilisation du Budget</h6>
                        <small class="text-muted">
                            {% set pourcentage = (solde_actuel / config.budget_initial * 100) if config.budget_initial > 0 else 0 %}
                            {{ pourcentage|round(1) }}% du budget initial restant
                        </small>
                    </div>
                    <div class="progress mb-2" style="height: 20px;">
                        <div class="progress-bar {% if pourcentage >= 50 %}bg-success{% elif pourcentage >= 25 %}bg-warning{% else %}bg-danger{% endif %}" 
                             style="width: {{ pourcentage|round(1) }}%">
                            {{ '{:,.0f}'.format(solde_actuel).replace(',', ' ') }} MAD
                        </div>
                    </div>
                    
                    <!-- Alerte si solde faible -->
                    {% if solde_actuel < config.seuil_alerte %}
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Attention !</strong> Le solde actuel ({{ '{:,.2f}'.format(solde_actuel).replace(',', ' ') }} MAD) 
                        est inférieur au seuil d'alerte ({{ '{:,.2f}'.format(config.seuil_alerte).replace(',', ' ') }} MAD).
                        <a href="{{ url_for('reapprovisionner_budget', type_budget=type_budget) }}" class="btn btn-sm btn-primary ms-2">
                            <i class="fas fa-money-bill-wave"></i> Réapprovisionner
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Liste des opérations -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list"></i> Opérations {{ config.nom_budget }}
                        </h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button class="btn btn-outline-info" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#infoProtection" aria-expanded="false">
                                <i class="fas fa-info-circle"></i> Info Protection
                            </button>
                        </div>
                    </div>
                    <div class="collapse mt-2" id="infoProtection">
                        <div class="alert alert-info alert-sm mb-0">
                            <i class="fas fa-shield-alt"></i>
                            <strong>Protection des opérations :</strong>
                            <ul class="mb-0 mt-1">
                                <li>Les opérations liées à des <strong>affectations actives</strong> ne peuvent pas être supprimées ou modifiées</li>
                                <li>Pour modifier/supprimer une opération protégée, terminez d'abord l'affectation correspondante</li>
                                <li>Cette protection garantit la cohérence entre les budgets et les affectations en cours</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if operations %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Montant</th>
                                    <th>Commentaire</th>
                                    <th>Référence</th>
                                    <th>Affectation</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in operations %}
                                <tr>
                                    <td>
                                        <small>{{ operation.date_operation[:16].replace('T', ' ') }}</small>
                                    </td>
                                    <td>
                                        {% if operation.type == 'recette' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-arrow-up"></i> Recette
                                            </span>
                                        {% elif operation.type == 'depense' %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-arrow-down"></i> Dépense
                                            </span>
                                        {% else %}
                                            <span class="badge bg-primary">
                                                <i class="fas fa-money-bill-wave"></i> Réappro.
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ '{:,.2f}'.format(operation.montant).replace(',', ' ') }} MAD</strong>
                                    </td>
                                    <td>
                                        <small>{{ operation.commentaire[:40] }}{% if operation.commentaire|length > 40 %}...{% endif %}</small>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ operation.reference or '-' }}</small>
                                    </td>
                                    <td>
                                        {% if operation.affectation_id %}
                                            <div class="d-flex align-items-center">
                                                {% if operation.affectation_statut == 'active' %}
                                                    <span class="badge bg-success me-1">
                                                        <i class="fas fa-play"></i> Active
                                                    </span>
                                                {% elif operation.affectation_statut == 'terminee' %}
                                                    <span class="badge bg-secondary me-1">
                                                        <i class="fas fa-stop"></i> Terminée
                                                    </span>
                                                {% else %}
                                                    <span class="badge bg-warning me-1">
                                                        <i class="fas fa-pause"></i> {{ operation.affectation_statut|title }}
                                                    </span>
                                                {% endif %}
                                                <div>
                                                    <small class="text-muted d-block">{{ operation.immatriculation }}</small>
                                                    <small class="text-muted">{{ operation.conducteur_nom }}</small>
                                                </div>
                                            </div>
                                        {% else %}
                                            <small class="text-muted">-</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if operation.statut == 'valide' %}
                                            <span class="badge bg-success">Valide</span>
                                        {% elif operation.statut == 'annule' %}
                                            <span class="badge bg-danger">Annulé</span>
                                        {% else %}
                                            <span class="badge bg-warning">En attente</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            {% if operation.affectation_statut != 'active' %}
                                                <a href="{{ url_for('modifier_operation_budget', id=operation.id) }}" 
                                                   class="btn btn-outline-primary" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            {% else %}
                                                <button class="btn btn-outline-secondary" disabled title="Modification interdite - Affectation active">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            {% endif %}
                                            
                                            {% if operation.affectation_statut == 'active' %}
                                                <button class="btn btn-outline-secondary" disabled 
                                                        title="Suppression interdite - Affectation active">
                                                    <i class="fas fa-lock"></i>
                                                </button>
                                            {% else %}
                                                <form method="POST" action="{{ url_for('supprimer_operation_budget', id=operation.id) }}" 
                                                      class="d-inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette opération ?')">
                                                    <button type="submit" class="btn btn-outline-danger" title="Supprimer">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            {% endif %}
                                        </div>
                                        
                                        {% if operation.affectation_statut == 'active' %}
                                        <div class="mt-1">
                                            <small class="text-warning">
                                                <i class="fas fa-info-circle"></i>
                                                Opération protégée
                                            </small>
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Aucune opération enregistrée pour ce budget</p>
                        <a href="{{ url_for('ajouter_operation_budget', type_budget=type_budget) }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Ajouter une opération
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Statistiques et configuration -->
        <div class="col-md-4">
            <!-- Statistiques par catégorie -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Répartition
                    </h5>
                </div>
                <div class="card-body">
                    {% if stats_categories %}
                    {% for stat in stats_categories %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <small class="text-muted">{{ stat.categorie or 'Non catégorisé' }}</small>
                            <br>
                            {% if stat.type == 'recette' %}
                                <span class="badge bg-success">Recette</span>
                            {% elif stat.type == 'depense' %}
                                <span class="badge bg-danger">Dépense</span>
                            {% else %}
                                <span class="badge bg-primary">Réappro.</span>
                            {% endif %}
                        </div>
                        <div class="text-end">
                            <strong>{{ '{:,.2f}'.format(stat.total).replace(',', ' ') }} MAD</strong>
                        </div>
                    </div>
                    <hr>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-chart-pie fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Aucune donnée disponible</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Configuration actuelle -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog"></i> Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">Type de Budget</small>
                        <br>
                        <strong>{{ config.nom_budget }}</strong>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">Budget Initial</small>
                        <br>
                        <strong>{{ '{:,.2f}'.format(config.budget_initial).replace(',', ' ') }} MAD</strong>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">Seuil d'Alerte</small>
                        <br>
                        <strong>{{ '{:,.2f}'.format(config.seuil_alerte).replace(',', ' ') }} MAD</strong>
                    </div>
                    {% if config.periode_debut and config.periode_fin %}
                    <div class="mb-2">
                        <small class="text-muted">Période</small>
                        <br>
                        <strong>{{ config.periode_debut }} - {{ config.periode_fin }}</strong>
                    </div>
                    {% endif %}
                    <div class="mb-2">
                        <small class="text-muted">Statut</small>
                        <br>
                        {% if config.actif %}
                            <span class="badge bg-success">Actif</span>
                        {% else %}
                            <span class="badge bg-secondary">Inactif</span>
                        {% endif %}
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('config_budget', type_budget=type_budget) }}" class="btn btn-sm btn-secondary w-100">
                            <i class="fas fa-cog"></i> Modifier Configuration
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}