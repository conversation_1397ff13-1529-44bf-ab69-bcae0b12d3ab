{% extends "base.html" %}

{% block title %}Rapports et Statistiques - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-chart-bar"></i> Rapports et Statistiques</h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> Imprimer
                </button>
                <a href="{{ url_for('export_rapports', format='csv') }}" class="btn btn-outline-success">
                    <i class="fas fa-file-csv"></i> Export CSV
                </a>
                <a href="{{ url_for('export_rapports', format='excel') }}" class="btn btn-outline-info">
                    <i class="fas fa-file-excel"></i> Export Excel
                </a>
                <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i> Actualiser
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques générales -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ stats.total_vehicules or 0 }}</h3>
                        <p class="mb-0">Véhicules</p>
                        <small>{{ stats.vehicules_disponibles or 0 }} disponibles</small>
                    </div>
                    <i class="fas fa-car fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ stats.total_conducteurs or 0 }}</h3>
                        <p class="mb-0">Conducteurs</p>
                        <small>{{ stats.affectations_actives or 0 }} actifs</small>
                    </div>
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-dark h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ stats.total_maintenances or 0 }}</h3>
                        <p class="mb-0">Maintenances</p>
                        <small>{{ stats.maintenances_planifiees or 0 }} planifiées</small>
                    </div>
                    <i class="fas fa-tools fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ "{:,.0f}"|format(stats.cout_total_maintenances or 0) }}</h3>
                        <p class="mb-0">Coût Total (MAD)</p>
                        <small>{{ "{:,.0f}"|format(stats.cout_mois_maintenances or 0) }} ce mois</small>
                    </div>
                    <i class="fas fa-coins fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graphiques principaux -->
<div class="row mb-4">
    <!-- Répartition par statut -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i> Véhicules par statut
                </h5>
            </div>
            <div class="card-body">
                {% if vehicules_par_statut %}
                <canvas id="statutChart" height="250"></canvas>
                {% else %}
                <p class="text-muted text-center">Aucune donnée disponible</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Répartition par carburant -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-gas-pump"></i> Véhicules par carburant
                </h5>
            </div>
            <div class="card-body">
                {% if vehicules_par_carburant %}
                <canvas id="carburantChart" height="250"></canvas>
                {% else %}
                <p class="text-muted text-center">Aucune donnée disponible</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Répartition par marque -->
    <div class="col-lg-4 col-md-12 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-industry"></i> Top marques
                </h5>
            </div>
            <div class="card-body">
                {% if vehicules_par_marque %}
                <canvas id="marqueChart" height="250"></canvas>
                {% else %}
                <p class="text-muted text-center">Aucune donnée disponible</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Évolution des maintenances -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i> Évolution des maintenances (12 derniers mois)
                </h5>
            </div>
            <div class="card-body">
                {% if maintenances_par_mois %}
                <canvas id="maintenancesChart" height="100"></canvas>
                {% else %}
                <p class="text-muted text-center">Aucune donnée disponible</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Tableaux détaillés -->
<div class="row mb-4">
    <!-- Maintenances par type -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-wrench"></i> Maintenances par type
                </h5>
            </div>
            <div class="card-body">
                {% if maintenances_par_type %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Type</th>
                                <th>Nombre</th>
                                <th>Coût moyen</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in maintenances_par_type %}
                            <tr>
                                <td>{{ item.type_maintenance }}</td>
                                <td><span class="badge bg-primary">{{ item.count }}</span></td>
                                <td>{{ "{:,.0f}"|format(item.cout_moyen or 0) }} MAD</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">Aucune donnée</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Top véhicules maintenances -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-car-crash"></i> Véhicules les plus maintenus
                </h5>
            </div>
            <div class="card-body">
                {% if vehicules_maintenances %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Véhicule</th>
                                <th>Maintenances</th>
                                <th>Coût total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in vehicules_maintenances %}
                            <tr>
                                <td>
                                    <strong>{{ item.immatriculation }}</strong><br>
                                    <small class="text-muted">{{ item.marque }} {{ item.modele }}</small>
                                </td>
                                <td><span class="badge bg-warning">{{ item.nb_maintenances }}</span></td>
                                <td>{{ "{:,.0f}"|format(item.cout_total or 0) }} MAD</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">Aucune donnée</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Conducteurs actifs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-check"></i> Conducteurs avec véhicules affectés
                </h5>
            </div>
            <div class="card-body">
                {% if conducteurs_actifs %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Conducteur</th>
                                <th>Véhicule affecté</th>
                                <th>Immatriculation</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in conducteurs_actifs %}
                            <tr>
                                <td>
                                    <i class="fas fa-user"></i>
                                    {{ item.nom }} {{ item.prenom }}
                                </td>
                                <td>{{ item.marque }} {{ item.modele }}</td>
                                <td><span class="badge bg-info">{{ item.immatriculation }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">Aucun conducteur avec véhicule affecté</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Actions d'export -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Actions</h5>
            </div>
            <div class="card-body">
                <div class="btn-group">
                    <button class="btn btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print"></i> Imprimer
                    </button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i> Export Complet
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('export_complet', format='xlsx') }}">
                                <i class="fas fa-file-excel"></i> Excel Multi-feuilles (XLSX)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('export_complet', format='xls') }}">
                                <i class="fas fa-file-excel"></i> Excel (XLS)
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('export_vehicules', format='csv') }}">
                                <i class="fas fa-car"></i> Véhicules (CSV)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('export_conducteurs', format='csv') }}">
                                <i class="fas fa-users"></i> Conducteurs (CSV)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('export_maintenances', format='csv') }}">
                                <i class="fas fa-tools"></i> Maintenances (CSV)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('export_affectations', format='csv') }}">
                                <i class="fas fa-exchange-alt"></i> Affectations (CSV)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration des couleurs
    const colors = {
        primary: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6c757d', '#17a2b8', '#fd7e14', '#6f42c1', '#e83e8c', '#20c997'],
        gradient: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b']
    };

    // Graphique des statuts
    {% if vehicules_par_statut %}
    const statutCtx = document.getElementById('statutChart').getContext('2d');
    new Chart(statutCtx, {
        type: 'pie',
        data: {
            labels: [{% for item in vehicules_par_statut %}'{{ item.statut|title }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for item in vehicules_par_statut %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: colors.primary,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    {% endif %}

    // Graphique des carburants
    {% if vehicules_par_carburant %}
    const carburantCtx = document.getElementById('carburantChart').getContext('2d');
    new Chart(carburantCtx, {
        type: 'doughnut',
        data: {
            labels: [{% for item in vehicules_par_carburant %}'{{ item.carburant }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for item in vehicules_par_carburant %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: colors.gradient,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    {% endif %}

    // Graphique des marques
    {% if vehicules_par_marque %}
    const marqueCtx = document.getElementById('marqueChart').getContext('2d');
    new Chart(marqueCtx, {
        type: 'bar',
        data: {
            labels: [{% for item in vehicules_par_marque %}'{{ item.marque }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Nombre de véhicules',
                data: [{% for item in vehicules_par_marque %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: '#007bff',
                borderColor: '#0056b3',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
    {% endif %}

    // Graphique évolution maintenances
    {% if maintenances_par_mois %}
    const maintenancesCtx = document.getElementById('maintenancesChart').getContext('2d');
    new Chart(maintenancesCtx, {
        type: 'line',
        data: {
            labels: [{% for item in maintenances_par_mois %}'{{ item.mois }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Nombre de maintenances',
                data: [{% for item in maintenances_par_mois %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }, {
                label: 'Coût total (MAD)',
                data: [{% for item in maintenances_par_mois %}{{ item.cout_total or 0 }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#ffc107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Mois'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Nombre de maintenances'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Coût (MAD)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
    {% endif %}
});
</script>
{% endblock %}
