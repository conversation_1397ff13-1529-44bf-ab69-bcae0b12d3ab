#!/usr/bin/env python3
"""
Test final des rapports corrigés
"""

import requests
import sys

def test_rapports_final():
    """Teste la version finale des rapports"""
    print("🎯 Test Final des Rapports Corrigés")
    print("=" * 50)
    
    try:
        # Test 1: Accès à la page
        print("1. Test d'accès à la page...")
        response = requests.get("http://localhost:5001/rapports", timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Page accessible (200)")
            content = response.text
            
            # Vérifications du contenu
            checks = [
                ("Tableau de Bord Analytics", "Titre principal"),
                ("Véhicules Total", "KPI véhicules"),
                ("Coût Total (MAD)", "KPI coûts"),
                ("chart.js", "Bibliothèque graphiques"),
                ("evolutionChart", "Graphique évolution"),
                ("statutChart", "Graphique statuts"),
                ("dashboard-card", "Classes CSS"),
                ("btn-outline-primary", "Boutons d'action")
            ]
            
            for check, description in checks:
                if check in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} manquant")
                    
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
            return False
        
        # Test 2: Test des API
        print("\n2. Test des API...")
        
        try:
            api_response = requests.get("http://localhost:5001/api/dashboard-data", timeout=5)
            if api_response.status_code == 200:
                print("   ✅ API dashboard-data accessible")
                data = api_response.json()
                if 'stats' in data:
                    print("   ✅ Données stats présentes")
                else:
                    print("   ⚠️ Données stats manquantes")
            else:
                print(f"   ❌ API dashboard-data erreur: {api_response.status_code}")
        except Exception as e:
            print(f"   ⚠️ API dashboard-data non testable: {e}")
        
        # Test 3: Test des exports
        print("\n3. Test des exports...")
        
        try:
            csv_response = requests.get("http://localhost:5001/rapports/export/csv", timeout=10)
            if csv_response.status_code == 200:
                print("   ✅ Export CSV fonctionnel")
            else:
                print(f"   ❌ Export CSV erreur: {csv_response.status_code}")
        except Exception as e:
            print(f"   ⚠️ Export CSV non testable: {e}")
        
        try:
            excel_response = requests.get("http://localhost:5001/rapports/export/excel", timeout=10)
            if excel_response.status_code == 200:
                print("   ✅ Export Excel fonctionnel")
            else:
                print(f"   ❌ Export Excel erreur: {excel_response.status_code}")
        except Exception as e:
            print(f"   ⚠️ Export Excel non testable: {e}")
        
        # Test 4: Vérification des données
        print("\n4. Vérification des données...")
        
        # Vérifier la base de données
        import sqlite3
        conn = sqlite3.connect('parc_automobile.db')
        
        vehicules = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]
        maintenances = conn.execute('SELECT COUNT(*) FROM maintenances').fetchone()[0]
        conducteurs = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]
        
        print(f"   📊 Véhicules: {vehicules}")
        print(f"   🔧 Maintenances: {maintenances}")
        print(f"   👥 Conducteurs: {conducteurs}")
        
        if vehicules > 0:
            print("   ✅ Données véhicules disponibles")
        else:
            print("   ⚠️ Aucun véhicule en base")
            
        conn.close()
        
        print("\n" + "=" * 50)
        print("🎉 Tests terminés avec succès !")
        print("\n📍 Accès au tableau de bord:")
        print("   URL: http://localhost:5001/rapports")
        print("\n🎯 Fonctionnalités disponibles:")
        print("   ✅ KPI et métriques avancées")
        print("   ✅ Graphiques interactifs")
        print("   ✅ Exports CSV/Excel")
        print("   ✅ Interface responsive")
        print("   ✅ Animations et effets visuels")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
        print("💡 Vérifiez que l'application Flask est démarrée")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def test_performance():
    """Teste la performance de la page"""
    print("\n⚡ Test de Performance")
    print("=" * 30)
    
    import time
    
    try:
        start_time = time.time()
        response = requests.get("http://localhost:5001/rapports", timeout=30)
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"Temps de chargement: {duration:.2f} secondes")
        
        if duration < 2:
            print("✅ Performance excellente (< 2s)")
        elif duration < 5:
            print("✅ Performance acceptable (< 5s)")
        else:
            print("⚠️ Performance lente (> 5s)")
            
        return duration < 10
        
    except Exception as e:
        print(f"❌ Erreur de test de performance: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Test Final du Tableau de Bord Analytics")
    print("=" * 60)
    
    success1 = test_rapports_final()
    success2 = test_performance()
    
    if success1 and success2:
        print("\n🎉 Tous les tests sont passés !")
        print("🚀 Le tableau de bord analytics est opérationnel !")
        sys.exit(0)
    else:
        print("\n❌ Certains tests ont échoué")
        sys.exit(1)
