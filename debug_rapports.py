#!/usr/bin/env python3
"""
Debug de la route rapports
"""

import sqlite3

def test_rapports_queries():
    """Teste les requêtes SQL de la route rapports"""
    print("🔍 Debug des Requêtes Rapports")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('parc_automobile.db')
        
        # Test 1: Statistiques générales
        print("1. Test statistiques générales...")
        stats = {}
        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]
        stats['total_conducteurs'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]
        stats['total_maintenances'] = conn.execute('SELECT COUNT(*) FROM maintenances').fetchone()[0]
        stats['total_affectations'] = conn.execute('SELECT COUNT(*) FROM affectations').fetchone()[0]
        print(f"   ✅ Véhicules: {stats['total_vehicules']}")
        print(f"   ✅ Conducteurs: {stats['total_conducteurs']}")
        print(f"   ✅ Maintenances: {stats['total_maintenances']}")
        print(f"   ✅ Affectations: {stats['total_affectations']}")
        
        # Test 2: Statistiques détaillées
        print("\n2. Test statistiques détaillées...")
        try:
            stats['vehicules_disponibles'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'").fetchone()[0]
            print(f"   ✅ Véhicules disponibles: {stats['vehicules_disponibles']}")
        except Exception as e:
            print(f"   ❌ Erreur véhicules disponibles: {e}")
        
        try:
            stats['maintenances_planifiees'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'planifiee'").fetchone()[0]
            print(f"   ✅ Maintenances planifiées: {stats['maintenances_planifiees']}")
        except Exception as e:
            print(f"   ❌ Erreur maintenances planifiées: {e}")
        
        # Test 3: Coûts de maintenance
        print("\n3. Test coûts de maintenance...")
        try:
            cout_total_result = conn.execute('SELECT SUM(cout) FROM maintenances WHERE cout IS NOT NULL').fetchone()
            stats['cout_total_maintenances'] = cout_total_result[0] if cout_total_result[0] else 0
            print(f"   ✅ Coût total: {stats['cout_total_maintenances']}")
        except Exception as e:
            print(f"   ❌ Erreur coût total: {e}")
        
        # Test 4: Répartitions
        print("\n4. Test répartitions...")
        try:
            vehicules_par_statut = conn.execute('''
                SELECT statut, COUNT(*) as count
                FROM vehicules
                GROUP BY statut
                ORDER BY count DESC
            ''').fetchall()
            print(f"   ✅ Véhicules par statut: {len(vehicules_par_statut)} groupes")
            for row in vehicules_par_statut:
                print(f"      {row[0]}: {row[1]}")
        except Exception as e:
            print(f"   ❌ Erreur véhicules par statut: {e}")
        
        try:
            vehicules_par_carburant = conn.execute('''
                SELECT carburant, COUNT(*) as count
                FROM vehicules
                WHERE carburant IS NOT NULL AND carburant != ''
                GROUP BY carburant
                ORDER BY count DESC
            ''').fetchall()
            print(f"   ✅ Véhicules par carburant: {len(vehicules_par_carburant)} groupes")
            for row in vehicules_par_carburant:
                print(f"      {row[0]}: {row[1]}")
        except Exception as e:
            print(f"   ❌ Erreur véhicules par carburant: {e}")
        
        # Test 5: Requêtes complexes
        print("\n5. Test requêtes complexes...")
        try:
            maintenances_par_mois = conn.execute('''
                SELECT strftime('%Y-%m', date_maintenance) as mois, 
                       COUNT(*) as count,
                       SUM(CASE WHEN cout IS NOT NULL THEN cout ELSE 0 END) as cout_total
                FROM maintenances
                WHERE date_maintenance >= date('now', '-12 months')
                GROUP BY strftime('%Y-%m', date_maintenance)
                ORDER BY mois
            ''').fetchall()
            print(f"   ✅ Maintenances par mois: {len(maintenances_par_mois)} mois")
        except Exception as e:
            print(f"   ❌ Erreur maintenances par mois: {e}")
        
        try:
            vehicules_maintenances = conn.execute('''
                SELECT v.immatriculation, v.marque, v.modele, COUNT(m.id) as nb_maintenances,
                       SUM(CASE WHEN m.cout IS NOT NULL THEN m.cout ELSE 0 END) as cout_total
                FROM vehicules v
                LEFT JOIN maintenances m ON v.id = m.vehicule_id
                GROUP BY v.id
                ORDER BY nb_maintenances DESC, cout_total DESC
                LIMIT 5
            ''').fetchall()
            print(f"   ✅ Top véhicules maintenances: {len(vehicules_maintenances)} véhicules")
        except Exception as e:
            print(f"   ❌ Erreur top véhicules: {e}")
        
        conn.close()
        print("\n✅ Toutes les requêtes SQL fonctionnent")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur générale: {e}")
        return False

if __name__ == "__main__":
    test_rapports_queries()
