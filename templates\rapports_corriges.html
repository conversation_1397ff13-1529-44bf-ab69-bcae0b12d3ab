{% extends "base.html" %}

{% block title %}Tableau de Bord Analytics - GesParc Auto{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border: none;
        border-radius: 15px;
        overflow: hidden;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .stat-card {
        background: linear-gradient(135deg, var(--bs-primary), #0056b3);
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .stat-icon {
        font-size: 3rem;
        opacity: 0.8;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }
    
    .metric-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        border-left: 4px solid;
    }
    
    .metric-primary { border-left-color: #007bff; }
    .metric-success { border-left-color: #28a745; }
    .metric-warning { border-left-color: #ffc107; }
    .metric-danger { border-left-color: #dc3545; }
    .metric-info { border-left-color: #17a2b8; }
    
    @media (max-width: 768px) {
        .chart-container {
            height: 250px;
        }
        
        .stat-icon {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h1 class="mb-1">
                        <i class="fas fa-chart-line text-primary"></i> 
                        Tableau de Bord Analytics
                    </h1>
                    <p class="text-muted mb-0">Vue d'ensemble et analyses du parc automobile</p>
                </div>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i> Actualiser
                    </button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i> Exporter
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('export_rapports', format='csv') }}">
                                <i class="fas fa-file-csv"></i> Rapport CSV
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('export_rapports', format='excel') }}">
                                <i class="fas fa-file-excel"></i> Rapport Excel
                            </a></li>
                        </ul>
                    </div>
                    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print"></i> Imprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
            <div class="card dashboard-card stat-card" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                <div class="card-body position-relative">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h3 class="mb-1">{{ stats.total_vehicules or 0 }}</h3>
                            <p class="mb-0">Véhicules Total</p>
                            <small class="badge bg-light text-dark">
                                {{ stats.vehicules_disponibles or 0 }} disponibles
                            </small>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-car stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
            <div class="card dashboard-card stat-card" style="background: linear-gradient(135deg, #28a745, #1e7e34);">
                <div class="card-body position-relative">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h3 class="mb-1">{{ "{:,.0f}"|format(stats.cout_total_maintenances or 0) }}</h3>
                            <p class="mb-0">Coût Total (MAD)</p>
                            <small class="badge bg-light text-dark">
                                {{ "{:,.0f}"|format(stats.cout_mois_maintenances or 0) }} ce mois
                            </small>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-coins stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
            <div class="card dashboard-card stat-card" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                <div class="card-body position-relative">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h3 class="mb-1 text-dark">{{ stats.total_maintenances or 0 }}</h3>
                            <p class="mb-0 text-dark">Maintenances</p>
                            <small class="badge bg-dark text-light">
                                {{ stats.maintenances_planifiees or 0 }} planifiées
                            </small>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-tools stat-icon text-dark"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
            <div class="card dashboard-card stat-card" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                <div class="card-body position-relative">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h3 class="mb-1">{{ stats.total_conducteurs or 0 }}</h3>
                            <p class="mb-0">Conducteurs</p>
                            <small class="badge bg-light text-dark">
                                {{ stats.affectations_actives or 0 }} actifs
                            </small>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-users stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Métriques avancées -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="metric-card metric-primary">
                <div class="metric-value">
                    {% set taux_dispo = (stats.vehicules_disponibles / stats.total_vehicules * 100) if stats.total_vehicules > 0 else 0 %}
                    <h4 class="mb-1">{{ "{:.1f}"|format(taux_dispo) }}%</h4>
                    <small>Taux Disponibilité</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="metric-card metric-success">
                <div class="metric-value">
                    {% set cout_vehicule = (stats.cout_total_maintenances / stats.total_vehicules) if stats.total_vehicules > 0 else 0 %}
                    <h4 class="mb-1">{{ "{:,.0f}"|format(cout_vehicule) }}</h4>
                    <small>Coût/Véhicule (MAD)</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="metric-card metric-warning">
                <div class="metric-value">
                    {% set maint_vehicule = (stats.total_maintenances / stats.total_vehicules) if stats.total_vehicules > 0 else 0 %}
                    <h4 class="mb-1">{{ "{:.1f}"|format(maint_vehicule) }}</h4>
                    <small>Maintenances/Véhicule</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="metric-card metric-danger">
                <div class="metric-value">
                    {% set taux_maint = (stats.vehicules_en_maintenance / stats.total_vehicules * 100) if stats.total_vehicules > 0 else 0 %}
                    <h4 class="mb-1">{{ "{:.1f}"|format(taux_maint) }}%</h4>
                    <small>En Maintenance</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="metric-card metric-info">
                <div class="metric-value">
                    <h4 class="mb-1">{{ "{:,.0f}"|format(stats.kilometrage_moyen or 0) }}</h4>
                    <small>KM Moyen</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="metric-card metric-primary">
                <div class="metric-value">
                    <h4 class="mb-1">{{ "{:.1f}"|format(stats.age_moyen_parc or 0) }}</h4>
                    <small>Âge Moyen (ans)</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques -->
    <div class="row mb-4">
        <!-- Évolution des maintenances -->
        <div class="col-lg-8 mb-4">
            <div class="card dashboard-card h-100">
                <div class="card-header bg-transparent">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-primary"></i> 
                        Évolution des Maintenances
                    </h5>
                </div>
                <div class="card-body">
                    {% if maintenances_par_mois %}
                    <div class="chart-container">
                        <canvas id="evolutionChart"></canvas>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                        <p>Aucune donnée d'évolution disponible</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Répartition par statut -->
        <div class="col-lg-4 mb-4">
            <div class="card dashboard-card h-100">
                <div class="card-header bg-transparent">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie text-success"></i> 
                        Répartition par Statut
                    </h5>
                </div>
                <div class="card-body">
                    {% if vehicules_par_statut %}
                    <div class="chart-container">
                        <canvas id="statutChart"></canvas>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-chart-pie fa-3x mb-3"></i>
                        <p>Aucune donnée de statut disponible</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration des couleurs
    const colors = {
        primary: '#007bff',
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545',
        info: '#17a2b8',
        palette: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1']
    };

    // Configuration globale Chart.js
    Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#495057';

    // 1. Graphique d'évolution des maintenances
    {% if maintenances_par_mois %}
    const evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
    const evolutionChart = new Chart(evolutionCtx, {
        type: 'line',
        data: {
            labels: [{% for item in maintenances_par_mois %}'{{ item.mois }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Nombre de maintenances',
                data: [{% for item in maintenances_par_mois %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: colors.primary,
                backgroundColor: colors.primary + '20',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: colors.primary,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }, {
                label: 'Coût total (MAD)',
                data: [{% for item in maintenances_par_mois %}{{ item.cout_total or 0 }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: colors.success,
                backgroundColor: colors.success + '20',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                pointBackgroundColor: colors.success,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: colors.primary,
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 1) {
                                return context.dataset.label + ': ' + context.parsed.y.toLocaleString() + ' MAD';
                            }
                            return context.dataset.label + ': ' + context.parsed.y;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Période'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Nombre de maintenances'
                    },
                    beginAtZero: true
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Coût (MAD)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}

    // 2. Graphique donut pour les statuts
    {% if vehicules_par_statut %}
    const statutCtx = document.getElementById('statutChart').getContext('2d');
    const statutChart = new Chart(statutCtx, {
        type: 'doughnut',
        data: {
            labels: [{% for item in vehicules_par_statut %}'{{ item.statut|title }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for item in vehicules_par_statut %}{{ item.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: colors.palette.slice(0, {{ vehicules_par_statut|length }}),
                borderColor: '#fff',
                borderWidth: 3,
                hoverBorderWidth: 5,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: colors.primary,
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1000
            }
        }
    });
    {% endif %}

    // Animation des cartes au scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observer toutes les cartes
    document.querySelectorAll('.dashboard-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>
{% endblock %}
