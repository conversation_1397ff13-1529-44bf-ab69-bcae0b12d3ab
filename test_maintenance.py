#!/usr/bin/env python3
"""
Test de la fonctionnalité de planification de maintenance
"""

import requests
import sys

def test_maintenance_page():
    """Teste la page de planification de maintenance"""
    print("🔍 Test de la page Planifier une Maintenance")
    print("=" * 50)
    
    base_url = "http://localhost:5001"
    
    try:
        # Test 1: Accès à la page
        print("1. Test d'accès à la page...")
        response = requests.get(f"{base_url}/maintenances/ajouter", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Page accessible")
            
            # Vérifier le contenu
            content = response.text
            if "Planifier une Maintenance" in content:
                print("   ✅ Titre correct")
            else:
                print("   ❌ Titre manquant")
                
            if "vehicule_id" in content:
                print("   ✅ Formulaire présent")
            else:
                print("   ❌ Formulaire manquant")
                
            if "Aucun véhicule disponible" in content:
                print("   ⚠️ Aucun véhicule disponible")
            else:
                print("   ✅ Véhicules disponibles")
                
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
            print(f"   Contenu: {response.text[:200]}...")
            
        # Test 2: Accès à la liste des maintenances
        print("\n2. Test d'accès à la liste des maintenances...")
        response = requests.get(f"{base_url}/maintenances", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Liste accessible")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            
        # Test 3: Test de soumission (simulation)
        print("\n3. Test de données...")
        test_data = {
            'vehicule_id': '1',
            'type_maintenance': 'Vidange',
            'date_maintenance': '2024-01-15',
            'priorite': 'normale',
            'cout_estime': '300',
            'garage': 'Garage Test',
            'description': 'Test de maintenance',
            'statut': 'planifiee'
        }
        
        print(f"   Données de test préparées: {len(test_data)} champs")
        print("   ✅ Structure de données valide")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
        print("💡 Vérifiez que l'application Flask est démarrée")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🏁 Tests terminés")
    return True

if __name__ == "__main__":
    success = test_maintenance_page()
    sys.exit(0 if success else 1)
