#!/usr/bin/env python3
"""
Script d'initialisation de la base de données pour GesParc Auto
"""

import sqlite3
import os
from datetime import datetime, date

DATABASE = 'parc_automobile.db'

def init_database():
    """Initialise la base de données avec les tables nécessaires"""
    
    # Supprimer la base existante si elle existe
    if os.path.exists(DATABASE):
        os.remove(DATABASE)
        print(f"Base de données existante supprimée: {DATABASE}")
    
    conn = sqlite3.connect(DATABASE)
    print(f"Création de la base de données: {DATABASE}")
    
    # Table des véhicules
    conn.execute('''
        CREATE TABLE vehicules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            immatriculation TEXT UNIQUE NOT NULL,
            marque TEXT NOT NULL,
            modele TEXT NOT NULL,
            annee INTEGER NOT NULL,
            couleur TEXT,
            kilometrage INTEGER DEFAULT 0,
            carburant TEXT NOT NULL,
            statut TEXT DEFAULT 'disponible',
            date_acquisition DATE,
            prix_acquisition REAL,
            date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    print("Table 'vehicules' créée")
    
    # Table des conducteurs
    conn.execute('''
        CREATE TABLE conducteurs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            prenom TEXT NOT NULL,
            numero_permis TEXT UNIQUE NOT NULL,
            date_permis DATE,
            telephone TEXT,
            email TEXT,
            statut TEXT DEFAULT 'actif',
            date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    print("Table 'conducteurs' créée")
    
    # Table des maintenances
    conn.execute('''
        CREATE TABLE maintenances (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            vehicule_id INTEGER NOT NULL,
            type_maintenance TEXT NOT NULL,
            description TEXT,
            date_maintenance DATE NOT NULL,
            cout REAL,
            kilometrage_maintenance INTEGER,
            garage TEXT,
            priorite TEXT DEFAULT 'normale',
            statut TEXT DEFAULT 'planifiee',
            date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            date_realisation DATE,
            notes_technicien TEXT,
            FOREIGN KEY (vehicule_id) REFERENCES vehicules (id)
        )
    ''')
    print("Table 'maintenances' créée")
    
    # Table des affectations
    conn.execute('''
        CREATE TABLE affectations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            vehicule_id INTEGER NOT NULL,
            conducteur_id INTEGER NOT NULL,
            date_debut DATE NOT NULL,
            date_fin DATE,
            statut TEXT DEFAULT 'active',
            commentaire TEXT,
            date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vehicule_id) REFERENCES vehicules (id),
            FOREIGN KEY (conducteur_id) REFERENCES conducteurs (id)
        )
    ''')
    print("Table 'affectations' créée")
    
    # Insérer des données de test
    insert_sample_data(conn)
    
    conn.commit()
    conn.close()
    print("Base de données initialisée avec succès!")

def insert_sample_data(conn):
    """Insère des données de test"""
    print("Insertion des données de test...")
    
    # Véhicules de test
    vehicules_test = [
        ('AB-123-CD', 'Peugeot', '308', 2020, 'Blanc', 25000, 'Essence', 'disponible', '2020-01-15', 18000),
        ('EF-456-GH', 'Renault', 'Clio', 2019, 'Rouge', 35000, 'Diesel', 'affecte', '2019-03-20', 15000),
        ('IJ-789-KL', 'Citroën', 'C3', 2021, 'Bleu', 15000, 'Essence', 'disponible', '2021-06-10', 16500),
        ('MN-012-OP', 'Volkswagen', 'Golf', 2018, 'Gris', 45000, 'Diesel', 'en_maintenance', '2018-09-05', 22000),
    ]
    
    for vehicule in vehicules_test:
        conn.execute('''
            INSERT INTO vehicules (immatriculation, marque, modele, annee, couleur, 
            kilometrage, carburant, statut, date_acquisition, prix_acquisition)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', vehicule)
    
    # Conducteurs de test
    conducteurs_test = [
        ('Dupont', 'Jean', '123456789', '2015-05-20', '0123456789', '<EMAIL>', 'actif'),
        ('Martin', 'Marie', '987654321', '2018-03-15', '0987654321', '<EMAIL>', 'actif'),
        ('Bernard', 'Pierre', '456789123', '2020-07-10', '0456789123', '<EMAIL>', 'actif'),
    ]
    
    for conducteur in conducteurs_test:
        conn.execute('''
            INSERT INTO conducteurs (nom, prenom, numero_permis, date_permis, 
            telephone, email, statut)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', conducteur)
    
    # Maintenances de test
    maintenances_test = [
        (4, 'Révision', 'Révision des 40 000 km', '2025-08-15', 350.0, 45000, 'Garage Central', 'planifiee'),
        (2, 'Vidange', 'Vidange et changement filtres', '2025-07-20', 120.0, 35500, 'Garage Renault', 'planifiee'),
        (1, 'Contrôle technique', 'Contrôle technique obligatoire', '2025-09-01', 78.0, 25200, 'Centre de contrôle', 'planifiee'),
    ]
    
    for maintenance in maintenances_test:
        conn.execute('''
            INSERT INTO maintenances (vehicule_id, type_maintenance, description, 
            date_maintenance, cout, kilometrage_maintenance, garage, statut)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', maintenance)
    
    # Affectations de test
    affectations_test = [
        (2, 1, '2025-01-01', None, 'active', 'Véhicule de service pour Jean Dupont'),
        (1, 2, '2025-06-01', '2025-06-30', 'terminee', 'Mission temporaire'),
    ]
    
    for affectation in affectations_test:
        conn.execute('''
            INSERT INTO affectations (vehicule_id, conducteur_id, date_debut, 
            date_fin, statut, commentaire)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', affectation)
    
    print("Données de test insérées avec succès!")

if __name__ == '__main__':
    init_database()
