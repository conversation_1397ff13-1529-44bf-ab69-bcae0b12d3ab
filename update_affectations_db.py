#!/usr/bin/env python3
"""
Script pour mettre à jour la table affectations avec les nouveaux champs
"""

import sqlite3

def update_affectations_table():
    """Met à jour la table affectations avec les nouveaux champs"""
    conn = sqlite3.connect('parc_automobile.db')
    
    # Ajouter la colonne motif_fin
    try:
        conn.execute('ALTER TABLE affectations ADD COLUMN motif_fin TEXT')
        print('✅ Colonne motif_fin ajoutée')
    except Exception as e:
        print('ℹ️ Colonne motif_fin existe déjà ou erreur:', str(e))
    
    # Ajouter la colonne commentaire_fin
    try:
        conn.execute('ALTER TABLE affectations ADD COLUMN commentaire_fin TEXT')
        print('✅ Colonne commentaire_fin ajoutée')
    except Exception as e:
        print('ℹ️ Colonne commentaire_fin existe déjà ou erreur:', str(e))
    
    # Ajouter la colonne kilometrage_debut
    try:
        conn.execute('ALTER TABLE affectations ADD COLUMN kilometrage_debut INTEGER')
        print('✅ Colonne kilometrage_debut ajoutée')
    except Exception as e:
        print('ℹ️ Colonne kilometrage_debut existe déjà ou erreur:', str(e))
    
    # Ajouter la colonne kilometrage_fin
    try:
        conn.execute('ALTER TABLE affectations ADD COLUMN kilometrage_fin INTEGER')
        print('✅ Colonne kilometrage_fin ajoutée')
    except Exception as e:
        print('ℹ️ Colonne kilometrage_fin existe déjà ou erreur:', str(e))
    
    # Ajouter la colonne budget_carburant
    try:
        conn.execute('ALTER TABLE affectations ADD COLUMN budget_carburant REAL')
        print('✅ Colonne budget_carburant ajoutée')
    except Exception as e:
        print('ℹ️ Colonne budget_carburant existe déjà ou erreur:', str(e))
    
    # Ajouter la colonne carburant_consomme
    try:
        conn.execute('ALTER TABLE affectations ADD COLUMN carburant_consomme REAL')
        print('✅ Colonne carburant_consomme ajoutée')
    except Exception as e:
        print('ℹ️ Colonne carburant_consomme existe déjà ou erreur:', str(e))
    
    # Ajouter la colonne cout_carburant
    try:
        conn.execute('ALTER TABLE affectations ADD COLUMN cout_carburant REAL')
        print('✅ Colonne cout_carburant ajoutée')
    except Exception as e:
        print('ℹ️ Colonne cout_carburant existe déjà ou erreur:', str(e))
    
    # Ajouter la colonne etat_vehicule_fin
    try:
        conn.execute('ALTER TABLE affectations ADD COLUMN etat_vehicule_fin TEXT DEFAULT "bon"')
        print('✅ Colonne etat_vehicule_fin ajoutée')
    except Exception as e:
        print('ℹ️ Colonne etat_vehicule_fin existe déjà ou erreur:', str(e))
    
    conn.commit()
    conn.close()
    print('✅ Mise à jour de la table affectations terminée')

if __name__ == '__main__':
    print("🔄 Mise à jour de la table affectations...")
    update_affectations_table()
    print("✅ Terminé!")
