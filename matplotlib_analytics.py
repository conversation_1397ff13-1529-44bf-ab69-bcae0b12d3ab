#!/usr/bin/env python3
"""
Module de génération de graphiques avec Matplotlib pour GesParc Auto
Créé des analyses visuelles avancées pour le parc automobile
"""

import matplotlib
matplotlib.use('Agg')  # Backend non-interactif pour serveur web

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sqlite3
import io
import base64
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class GesparcAnalytics:
    """Générateur de graphiques analytics pour GesParc Auto"""
    
    def __init__(self, db_path: str = 'parc_automobile.db'):
        """
        Initialise le générateur d'analytics
        
        Args:
            db_path: Chemin vers la base de données SQLite
        """
        self.db_path = db_path
        self.setup_style()
        
    def setup_style(self):
        """Configure le style global des graphiques"""
        # Style Seaborn moderne
        sns.set_style("whitegrid")
        sns.set_palette("husl")
        
        # Configuration matplotlib
        plt.rcParams.update({
            'figure.figsize': (12, 8),
            'figure.dpi': 100,
            'savefig.dpi': 300,
            'savefig.bbox': 'tight',
            'savefig.facecolor': 'white',
            'axes.titlesize': 16,
            'axes.labelsize': 12,
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'legend.fontsize': 11,
            'font.family': 'sans-serif',
            'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
            'axes.grid': True,
            'grid.alpha': 0.3,
            'axes.spines.top': False,
            'axes.spines.right': False,
        })
        
        # Palette de couleurs GesParc
        self.colors = {
            'primary': '#007bff',
            'success': '#28a745', 
            'warning': '#ffc107',
            'danger': '#dc3545',
            'info': '#17a2b8',
            'secondary': '#6c757d',
            'palette': ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1', '#fd7e14']
        }
        
    def get_data(self) -> Dict:
        """Récupère toutes les données nécessaires depuis la base"""
        conn = sqlite3.connect(self.db_path)
        
        data = {}
        
        # Véhicules (adapter aux colonnes existantes)
        data['vehicules'] = pd.read_sql_query('''
            SELECT id, immatriculation, marque, modele, annee, statut, carburant, kilometrage
            FROM vehicules
        ''', conn)
        
        # Maintenances
        data['maintenances'] = pd.read_sql_query('''
            SELECT m.*, v.immatriculation, v.marque, v.modele
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
        ''', conn)
        
        # Conducteurs et affectations
        data['conducteurs'] = pd.read_sql_query('''
            SELECT c.*, a.vehicule_id, a.date_debut, a.date_fin, a.statut as statut_affectation,
                   v.immatriculation
            FROM conducteurs c
            LEFT JOIN affectations a ON c.id = a.conducteur_id
            LEFT JOIN vehicules v ON a.vehicule_id = v.id
        ''', conn)
        
        conn.close()
        
        # Conversion des dates
        if not data['maintenances'].empty:
            data['maintenances']['date_maintenance'] = pd.to_datetime(data['maintenances']['date_maintenance'])
            data['maintenances']['date_creation'] = pd.to_datetime(data['maintenances']['date_creation'])
        
        # Pas de conversion de date_achat car la colonne n'existe pas
            
        return data
        
    def create_maintenance_evolution_chart(self, period_months: int = 12) -> str:
        """
        Crée un graphique d'évolution des maintenances sur une période
        
        Args:
            period_months: Nombre de mois à analyser
            
        Returns:
            String base64 de l'image du graphique
        """
        data = self.get_data()
        
        if data['maintenances'].empty:
            return self._create_no_data_chart("Aucune donnée de maintenance disponible")
        
        # Filtrer par période
        end_date = datetime.now()
        start_date = end_date - timedelta(days=period_months * 30)
        
        df = data['maintenances'][
            data['maintenances']['date_maintenance'] >= start_date
        ].copy()
        
        if df.empty:
            return self._create_no_data_chart(f"Aucune maintenance sur les {period_months} derniers mois")
        
        # Grouper par mois
        df['mois'] = df['date_maintenance'].dt.to_period('M')
        monthly_stats = df.groupby('mois').agg({
            'id': 'count',
            'cout': ['sum', 'mean']
        }).round(2)
        
        monthly_stats.columns = ['nb_maintenances', 'cout_total', 'cout_moyen']
        monthly_stats = monthly_stats.reset_index()
        monthly_stats['mois_str'] = monthly_stats['mois'].astype(str)
        
        # Création du graphique
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
        fig.suptitle('Évolution des Maintenances - Analyse Temporelle', fontsize=18, fontweight='bold')
        
        # Graphique 1: Nombre de maintenances
        ax1.plot(monthly_stats['mois_str'], monthly_stats['nb_maintenances'], 
                marker='o', linewidth=3, markersize=8, color=self.colors['primary'])
        ax1.fill_between(monthly_stats['mois_str'], monthly_stats['nb_maintenances'], 
                        alpha=0.3, color=self.colors['primary'])
        ax1.set_title('Nombre de Maintenances par Mois', fontsize=14, pad=20)
        ax1.set_ylabel('Nombre de maintenances', fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # Rotation des labels
        ax1.tick_params(axis='x', rotation=45)
        
        # Graphique 2: Coûts
        ax2_twin = ax2.twinx()
        
        bars = ax2.bar(monthly_stats['mois_str'], monthly_stats['cout_total'], 
                      alpha=0.7, color=self.colors['success'], label='Coût total')
        line = ax2_twin.plot(monthly_stats['mois_str'], monthly_stats['cout_moyen'], 
                           color=self.colors['danger'], marker='s', linewidth=2, 
                           markersize=6, label='Coût moyen')
        
        ax2.set_title('Évolution des Coûts de Maintenance', fontsize=14, pad=20)
        ax2.set_ylabel('Coût total (MAD)', fontsize=12, color=self.colors['success'])
        ax2_twin.set_ylabel('Coût moyen (MAD)', fontsize=12, color=self.colors['danger'])
        ax2.tick_params(axis='x', rotation=45)
        
        # Légendes
        lines1, labels1 = ax2.get_legend_handles_labels()
        lines2, labels2 = ax2_twin.get_legend_handles_labels()
        ax2.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        plt.tight_layout()
        return self._fig_to_base64(fig)
        
    def create_vehicle_analysis_dashboard(self) -> str:
        """
        Crée un dashboard d'analyse des véhicules
        
        Returns:
            String base64 de l'image du dashboard
        """
        data = self.get_data()
        
        if data['vehicules'].empty:
            return self._create_no_data_chart("Aucune donnée de véhicule disponible")
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Dashboard d\'Analyse des Véhicules', fontsize=20, fontweight='bold')
        
        # 1. Répartition par statut (Pie chart)
        statut_counts = data['vehicules']['statut'].value_counts()
        colors_pie = [self.colors['palette'][i % len(self.colors['palette'])] for i in range(len(statut_counts))]
        
        wedges, texts, autotexts = ax1.pie(statut_counts.values, labels=statut_counts.index, 
                                          autopct='%1.1f%%', colors=colors_pie, startangle=90)
        ax1.set_title('Répartition par Statut', fontsize=14, pad=20)
        
        # Améliorer l'apparence du pie chart
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        # 2. Répartition par carburant (Bar chart)
        if 'carburant' in data['vehicules'].columns:
            carburant_counts = data['vehicules']['carburant'].value_counts()
            bars = ax2.bar(carburant_counts.index, carburant_counts.values, 
                          color=self.colors['palette'][:len(carburant_counts)])
            ax2.set_title('Répartition par Type de Carburant', fontsize=14, pad=20)
            ax2.set_ylabel('Nombre de véhicules')
            ax2.tick_params(axis='x', rotation=45)
            
            # Ajouter les valeurs sur les barres
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{int(height)}', ha='center', va='bottom', fontweight='bold')
        
        # 3. Distribution des âges (Histogram)
        if 'annee' in data['vehicules'].columns:
            ages = 2025 - data['vehicules']['annee']
            ax3.hist(ages, bins=10, alpha=0.7, color=self.colors['info'], edgecolor='black')
            ax3.set_title('Distribution des Âges des Véhicules', fontsize=14, pad=20)
            ax3.set_xlabel('Âge (années)')
            ax3.set_ylabel('Nombre de véhicules')
            ax3.axvline(ages.mean(), color=self.colors['danger'], linestyle='--', 
                       linewidth=2, label=f'Âge moyen: {ages.mean():.1f} ans')
            ax3.legend()
        
        # 4. Top marques (Horizontal bar)
        if 'marque' in data['vehicules'].columns:
            marque_counts = data['vehicules']['marque'].value_counts().head(8)
            y_pos = np.arange(len(marque_counts))
            bars = ax4.barh(y_pos, marque_counts.values, color=self.colors['warning'])
            ax4.set_yticks(y_pos)
            ax4.set_yticklabels(marque_counts.index)
            ax4.set_title('Top Marques de Véhicules', fontsize=14, pad=20)
            ax4.set_xlabel('Nombre de véhicules')
            
            # Ajouter les valeurs
            for i, bar in enumerate(bars):
                width = bar.get_width()
                ax4.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                        f'{int(width)}', ha='left', va='center', fontweight='bold')
        
        plt.tight_layout()
        return self._fig_to_base64(fig)
        
    def create_maintenance_cost_analysis(self) -> str:
        """
        Analyse détaillée des coûts de maintenance
        
        Returns:
            String base64 de l'image de l'analyse
        """
        data = self.get_data()
        
        if data['maintenances'].empty:
            return self._create_no_data_chart("Aucune donnée de maintenance disponible")
        
        # Filtrer les maintenances avec coût
        df = data['maintenances'][data['maintenances']['cout'].notna() & (data['maintenances']['cout'] > 0)].copy()
        
        if df.empty:
            return self._create_no_data_chart("Aucune donnée de coût disponible")
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Analyse des Coûts de Maintenance', fontsize=20, fontweight='bold')
        
        # 1. Distribution des coûts (Histogram avec KDE)
        ax1.hist(df['cout'], bins=20, alpha=0.7, color=self.colors['primary'], density=True)
        
        # Ajouter une courbe de densité
        from scipy import stats
        kde = stats.gaussian_kde(df['cout'])
        x_range = np.linspace(df['cout'].min(), df['cout'].max(), 100)
        ax1.plot(x_range, kde(x_range), color=self.colors['danger'], linewidth=2, label='Densité')
        
        ax1.set_title('Distribution des Coûts de Maintenance', fontsize=14, pad=20)
        ax1.set_xlabel('Coût (MAD)')
        ax1.set_ylabel('Densité')
        ax1.legend()
        
        # 2. Coûts par type de maintenance (Box plot)
        if 'type_maintenance' in df.columns:
            types = df['type_maintenance'].unique()
            data_for_box = [df[df['type_maintenance'] == t]['cout'].values for t in types]
            
            box_plot = ax2.boxplot(data_for_box, labels=types, patch_artist=True)
            
            # Colorer les boîtes
            for patch, color in zip(box_plot['boxes'], self.colors['palette']):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            ax2.set_title('Coûts par Type de Maintenance', fontsize=14, pad=20)
            ax2.set_ylabel('Coût (MAD)')
            ax2.tick_params(axis='x', rotation=45)
        
        # 3. Évolution des coûts dans le temps (Scatter plot avec tendance)
        df_sorted = df.sort_values('date_maintenance')
        ax3.scatter(df_sorted['date_maintenance'], df_sorted['cout'], 
                   alpha=0.6, color=self.colors['info'], s=50)
        
        # Ligne de tendance
        x_numeric = mdates.date2num(df_sorted['date_maintenance'])
        z = np.polyfit(x_numeric, df_sorted['cout'], 1)
        p = np.poly1d(z)
        ax3.plot(df_sorted['date_maintenance'], p(x_numeric), 
                color=self.colors['danger'], linewidth=2, linestyle='--', label='Tendance')
        
        ax3.set_title('Évolution des Coûts dans le Temps', fontsize=14, pad=20)
        ax3.set_xlabel('Date')
        ax3.set_ylabel('Coût (MAD)')
        ax3.legend()
        
        # Format des dates
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax3.tick_params(axis='x', rotation=45)
        
        # 4. Top véhicules par coût total (Bar chart)
        cout_par_vehicule = df.groupby('immatriculation')['cout'].sum().sort_values(ascending=False).head(8)
        
        bars = ax4.bar(range(len(cout_par_vehicule)), cout_par_vehicule.values, 
                      color=self.colors['warning'])
        ax4.set_xticks(range(len(cout_par_vehicule)))
        ax4.set_xticklabels(cout_par_vehicule.index, rotation=45)
        ax4.set_title('Top Véhicules par Coût Total', fontsize=14, pad=20)
        ax4.set_ylabel('Coût total (MAD)')
        
        # Ajouter les valeurs sur les barres
        for bar in bars:
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{int(height)}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        return self._fig_to_base64(fig)
        
    def _create_no_data_chart(self, message: str) -> str:
        """Crée un graphique indiquant l'absence de données"""
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, message, ha='center', va='center', fontsize=16, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor=self.colors['secondary'], alpha=0.3))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        return self._fig_to_base64(fig)
        
    def _fig_to_base64(self, fig) -> str:
        """Convertit une figure matplotlib en string base64"""
        img_buffer = io.BytesIO()
        fig.savefig(img_buffer, format='png', bbox_inches='tight', dpi=300)
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.read()).decode()
        plt.close(fig)  # Libérer la mémoire
        return img_str

    def create_performance_heatmap(self) -> str:
        """
        Crée une heatmap de performance des véhicules

        Returns:
            String base64 de l'image de la heatmap
        """
        data = self.get_data()

        if data['vehicules'].empty or data['maintenances'].empty:
            return self._create_no_data_chart("Données insuffisantes pour la heatmap")

        # Créer une matrice de performance
        vehicules_stats = []

        for _, vehicule in data['vehicules'].iterrows():
            maintenances_vehicule = data['maintenances'][
                data['maintenances']['vehicule_id'] == vehicule['id']
            ]

            stats = {
                'Immatriculation': vehicule['immatriculation'],
                'Âge': 2025 - vehicule['annee'] if pd.notna(vehicule['annee']) else 0,
                'Kilométrage': vehicule['kilometrage'] / 1000 if pd.notna(vehicule['kilometrage']) else 0,  # En milliers
                'Nb_Maintenances': len(maintenances_vehicule),
                'Coût_Total': maintenances_vehicule['cout'].sum() if not maintenances_vehicule.empty else 0,
                'Coût_Moyen': maintenances_vehicule['cout'].mean() if not maintenances_vehicule.empty else 0
            }
            vehicules_stats.append(stats)

        df_stats = pd.DataFrame(vehicules_stats)

        if df_stats.empty:
            return self._create_no_data_chart("Aucune statistique calculable")

        # Préparer les données pour la heatmap
        df_heatmap = df_stats.set_index('Immatriculation')[
            ['Âge', 'Kilométrage', 'Nb_Maintenances', 'Coût_Total', 'Coût_Moyen']
        ].fillna(0)

        # Normaliser les données (0-1)
        df_normalized = (df_heatmap - df_heatmap.min()) / (df_heatmap.max() - df_heatmap.min())
        df_normalized = df_normalized.fillna(0)

        # Créer la heatmap
        fig, ax = plt.subplots(figsize=(12, 8))

        sns.heatmap(df_normalized.T, annot=True, cmap='RdYlBu_r', center=0.5,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')

        ax.set_title('Heatmap de Performance des Véhicules\n(Valeurs normalisées 0-1)',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('Véhicules', fontsize=12)
        ax.set_ylabel('Métriques de Performance', fontsize=12)

        plt.tight_layout()
        return self._fig_to_base64(fig)

    def create_predictive_analysis(self) -> str:
        """
        Analyse prédictive des maintenances

        Returns:
            String base64 de l'image de l'analyse prédictive
        """
        data = self.get_data()

        if data['maintenances'].empty:
            return self._create_no_data_chart("Données insuffisantes pour l'analyse prédictive")

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Analyse Prédictive des Maintenances', fontsize=20, fontweight='bold')

        # 1. Corrélation Âge vs Coût de maintenance
        vehicules_avec_cout = []
        for _, vehicule in data['vehicules'].iterrows():
            maintenances_vehicule = data['maintenances'][
                data['maintenances']['vehicule_id'] == vehicule['id']
            ]
            if not maintenances_vehicule.empty and maintenances_vehicule['cout'].notna().any():
                age = 2025 - vehicule['annee'] if pd.notna(vehicule['annee']) else 0
                cout_total = maintenances_vehicule['cout'].sum()
                vehicules_avec_cout.append({'age': age, 'cout_total': cout_total})

        if vehicules_avec_cout:
            df_corr = pd.DataFrame(vehicules_avec_cout)
            ax1.scatter(df_corr['age'], df_corr['cout_total'], alpha=0.7,
                       color=self.colors['primary'], s=60)

            # Ligne de tendance
            if len(df_corr) > 1:
                z = np.polyfit(df_corr['age'], df_corr['cout_total'], 1)
                p = np.poly1d(z)
                ax1.plot(df_corr['age'], p(df_corr['age']),
                        color=self.colors['danger'], linewidth=2, linestyle='--')

                # Calculer R²
                correlation = np.corrcoef(df_corr['age'], df_corr['cout_total'])[0,1]
                ax1.text(0.05, 0.95, f'Corrélation: {correlation:.3f}',
                        transform=ax1.transAxes, fontsize=12,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

            ax1.set_title('Corrélation Âge vs Coût Total', fontsize=14, pad=20)
            ax1.set_xlabel('Âge du véhicule (années)')
            ax1.set_ylabel('Coût total maintenance (MAD)')

        # 2. Fréquence des maintenances par mois
        if not data['maintenances'].empty:
            df_maint = data['maintenances'].copy()
            df_maint['mois'] = df_maint['date_maintenance'].dt.month
            freq_mois = df_maint['mois'].value_counts().sort_index()

            mois_noms = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
                        'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc']

            bars = ax2.bar(range(1, 13), [freq_mois.get(i, 0) for i in range(1, 13)],
                          color=self.colors['success'], alpha=0.7)
            ax2.set_xticks(range(1, 13))
            ax2.set_xticklabels(mois_noms, rotation=45)
            ax2.set_title('Saisonnalité des Maintenances', fontsize=14, pad=20)
            ax2.set_ylabel('Nombre de maintenances')

            # Ajouter les valeurs
            for i, bar in enumerate(bars):
                height = bar.get_height()
                if height > 0:
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            f'{int(height)}', ha='center', va='bottom', fontweight='bold')

        # 3. Prédiction des prochaines maintenances (basée sur le kilométrage)
        vehicules_prediction = []
        for _, vehicule in data['vehicules'].iterrows():
            if pd.notna(vehicule['kilometrage']) and vehicule['kilometrage'] > 0:
                # Estimation simple: maintenance tous les 10000 km
                km_actuel = vehicule['kilometrage']
                prochaine_maintenance = ((km_actuel // 10000) + 1) * 10000
                km_restants = prochaine_maintenance - km_actuel

                vehicules_prediction.append({
                    'immatriculation': vehicule['immatriculation'],
                    'km_restants': km_restants,
                    'urgence': 'Urgent' if km_restants < 2000 else 'Moyen' if km_restants < 5000 else 'Normal'
                })

        if vehicules_prediction:
            df_pred = pd.DataFrame(vehicules_prediction)
            urgence_counts = df_pred['urgence'].value_counts()

            colors_urgence = {'Urgent': self.colors['danger'],
                            'Moyen': self.colors['warning'],
                            'Normal': self.colors['success']}
            colors_pie = [colors_urgence.get(cat, self.colors['secondary']) for cat in urgence_counts.index]

            wedges, texts, autotexts = ax3.pie(urgence_counts.values, labels=urgence_counts.index,
                                              autopct='%1.1f%%', colors=colors_pie, startangle=90)
            ax3.set_title('Urgence des Prochaines Maintenances', fontsize=14, pad=20)

            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')

        # 4. Tendance des coûts moyens
        if not data['maintenances'].empty:
            df_maint = data['maintenances'][data['maintenances']['cout'].notna()].copy()
            if not df_maint.empty:
                df_maint['trimestre'] = df_maint['date_maintenance'].dt.to_period('Q')
                cout_trimestre = df_maint.groupby('trimestre')['cout'].mean()

                ax4.plot(range(len(cout_trimestre)), cout_trimestre.values,
                        marker='o', linewidth=3, markersize=8, color=self.colors['info'])
                ax4.fill_between(range(len(cout_trimestre)), cout_trimestre.values,
                               alpha=0.3, color=self.colors['info'])

                ax4.set_title('Évolution du Coût Moyen par Trimestre', fontsize=14, pad=20)
                ax4.set_ylabel('Coût moyen (MAD)')
                ax4.set_xlabel('Trimestre')
                ax4.set_xticks(range(len(cout_trimestre)))
                ax4.set_xticklabels([str(q) for q in cout_trimestre.index], rotation=45)

        plt.tight_layout()
        return self._fig_to_base64(fig)

    def create_advanced_fleet_analytics(self) -> str:
        """
        Analyse avancée de la flotte avec métriques de performance

        Returns:
            String base64 de l'image de l'analyse avancée
        """
        data = self.get_data()

        if data['vehicules'].empty:
            return self._create_no_data_chart("Aucune donnée de flotte disponible")

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
        fig.suptitle('Analytics Avancés de la Flotte Automobile', fontsize=22, fontweight='bold')

        # 1. Analyse de l'efficacité par âge et kilométrage
        vehicules_metrics = []
        for _, vehicule in data['vehicules'].iterrows():
            maintenances_vehicule = data['maintenances'][
                data['maintenances']['vehicule_id'] == vehicule['id']
            ]

            age = 2025 - vehicule['annee'] if pd.notna(vehicule['annee']) else 0
            km = vehicule['kilometrage'] if pd.notna(vehicule['kilometrage']) else 0
            nb_maintenances = len(maintenances_vehicule)
            cout_total = maintenances_vehicule['cout'].sum() if not maintenances_vehicule.empty else 0

            # Calcul de l'efficacité (inverse du coût par km)
            efficacite = 1 / (cout_total / km + 1) if km > 0 else 0

            vehicules_metrics.append({
                'age': age,
                'kilometrage': km,
                'efficacite': efficacite,
                'cout_total': cout_total,
                'nb_maintenances': nb_maintenances
            })

        df_metrics = pd.DataFrame(vehicules_metrics)

        # Scatter plot avec taille proportionnelle au coût
        if not df_metrics.empty:
            scatter = ax1.scatter(df_metrics['age'], df_metrics['kilometrage'],
                                c=df_metrics['efficacite'], s=df_metrics['cout_total']/10 + 50,
                                cmap='RdYlGn', alpha=0.7, edgecolors='black', linewidth=0.5)

            ax1.set_title('Efficacité de la Flotte\n(Couleur=Efficacité, Taille=Coût)', fontsize=14, pad=20)
            ax1.set_xlabel('Âge du véhicule (années)')
            ax1.set_ylabel('Kilométrage')

            # Colorbar
            cbar = plt.colorbar(scatter, ax=ax1)
            cbar.set_label('Indice d\'efficacité', rotation=270, labelpad=20)

        # 2. Analyse de la disponibilité et utilisation
        statut_counts = data['vehicules']['statut'].value_counts()
        total_vehicules = len(data['vehicules'])

        # Calcul des métriques de disponibilité
        disponibles = statut_counts.get('disponible', 0)
        affectes = statut_counts.get('affecte', 0)
        en_maintenance = statut_counts.get('en_maintenance', 0)

        taux_disponibilite = (disponibles / total_vehicules) * 100
        taux_utilisation = (affectes / total_vehicules) * 100
        taux_maintenance = (en_maintenance / total_vehicules) * 100

        # Graphique en barres empilées avec métriques
        categories = ['Disponibilité', 'Utilisation', 'Maintenance']
        valeurs = [taux_disponibilite, taux_utilisation, taux_maintenance]
        colors = [self.colors['success'], self.colors['primary'], self.colors['warning']]

        bars = ax2.bar(categories, valeurs, color=colors, alpha=0.8)
        ax2.set_title('Métriques de Performance de la Flotte', fontsize=14, pad=20)
        ax2.set_ylabel('Pourcentage (%)')
        ax2.set_ylim(0, 100)

        # Ajouter les valeurs et objectifs
        objectifs = [70, 60, 15]  # Objectifs cibles
        for i, (bar, valeur, objectif) in enumerate(zip(bars, valeurs, objectifs)):
            # Valeur actuelle
            ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 1,
                    f'{valeur:.1f}%', ha='center', va='bottom', fontweight='bold', fontsize=12)

            # Ligne d'objectif
            ax2.axhline(y=objectif, xmin=i/3 + 0.1, xmax=(i+1)/3 - 0.1,
                       color='red', linestyle='--', linewidth=2, alpha=0.7)
            ax2.text(i, objectif + 2, f'Obj: {objectif}%', ha='center', va='bottom',
                    color='red', fontsize=10, fontweight='bold')

        # 3. Analyse des coûts par véhicule avec prédictions
        if not data['maintenances'].empty:
            cout_par_vehicule = data['maintenances'].groupby('immatriculation').agg({
                'cout': ['sum', 'count', 'mean']
            }).round(2)
            cout_par_vehicule.columns = ['cout_total', 'nb_maintenances', 'cout_moyen']
            cout_par_vehicule = cout_par_vehicule.reset_index()

            # Top 10 véhicules les plus coûteux
            top_vehicules = cout_par_vehicule.nlargest(10, 'cout_total')

            # Graphique avec double axe
            ax3_twin = ax3.twinx()

            bars = ax3.bar(range(len(top_vehicules)), top_vehicules['cout_total'],
                          alpha=0.7, color=self.colors['danger'], label='Coût total')
            line = ax3_twin.plot(range(len(top_vehicules)), top_vehicules['nb_maintenances'],
                               color=self.colors['primary'], marker='o', linewidth=2,
                               markersize=8, label='Nb maintenances')

            ax3.set_title('Top 10 Véhicules - Coûts vs Fréquence', fontsize=14, pad=20)
            ax3.set_ylabel('Coût total (MAD)', color=self.colors['danger'])
            ax3_twin.set_ylabel('Nombre de maintenances', color=self.colors['primary'])
            ax3.set_xticks(range(len(top_vehicules)))
            ax3.set_xticklabels(top_vehicules['immatriculation'], rotation=45)

            # Légendes combinées
            lines1, labels1 = ax3.get_legend_handles_labels()
            lines2, labels2 = ax3_twin.get_legend_handles_labels()
            ax3.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

        # 4. Analyse prédictive avancée avec machine learning simple
        if not data['maintenances'].empty and len(data['vehicules']) > 5:
            # Préparer les données pour la prédiction
            features_vehicules = []
            for _, vehicule in data['vehicules'].iterrows():
                maintenances_vehicule = data['maintenances'][
                    data['maintenances']['vehicule_id'] == vehicule['id']
                ]

                age = 2025 - vehicule['annee'] if pd.notna(vehicule['annee']) else 0
                km = vehicule['kilometrage'] if pd.notna(vehicule['kilometrage']) else 0
                nb_maint = len(maintenances_vehicule)
                cout_total = maintenances_vehicule['cout'].sum() if not maintenances_vehicule.empty else 0

                # Prédiction simple basée sur l'âge et le kilométrage
                risque_panne = min(100, (age * 5) + (km / 10000 * 10) + (nb_maint * 3))

                features_vehicules.append({
                    'immatriculation': vehicule['immatriculation'],
                    'age': age,
                    'kilometrage': km,
                    'risque_panne': risque_panne,
                    'cout_predit': cout_total * (1 + risque_panne/100)
                })

            df_prediction = pd.DataFrame(features_vehicules)

            # Graphique de risque
            colors_risque = []
            for risque in df_prediction['risque_panne']:
                if risque < 30:
                    colors_risque.append(self.colors['success'])
                elif risque < 60:
                    colors_risque.append(self.colors['warning'])
                else:
                    colors_risque.append(self.colors['danger'])

            bars = ax4.barh(range(len(df_prediction)), df_prediction['risque_panne'],
                           color=colors_risque, alpha=0.8)
            ax4.set_yticks(range(len(df_prediction)))
            ax4.set_yticklabels(df_prediction['immatriculation'])
            ax4.set_title('Analyse de Risque Prédictive', fontsize=14, pad=20)
            ax4.set_xlabel('Indice de risque de panne (%)')

            # Zones de risque
            ax4.axvline(x=30, color=self.colors['success'], linestyle='--', alpha=0.7, label='Faible')
            ax4.axvline(x=60, color=self.colors['warning'], linestyle='--', alpha=0.7, label='Moyen')
            ax4.legend(loc='lower right')

            # Ajouter les valeurs
            for i, bar in enumerate(bars):
                width = bar.get_width()
                ax4.text(width + 1, bar.get_y() + bar.get_height()/2.,
                        f'{width:.0f}%', ha='left', va='center', fontweight='bold')

        plt.tight_layout()
        return self._fig_to_base64(fig)

    def create_financial_dashboard(self) -> str:
        """
        Dashboard financier complet avec analyses de coûts avancées

        Returns:
            String base64 de l'image du dashboard financier
        """
        data = self.get_data()

        if data['maintenances'].empty:
            return self._create_no_data_chart("Aucune donnée financière disponible")

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
        fig.suptitle('Dashboard Financier - Analyse des Coûts', fontsize=22, fontweight='bold')

        # Filtrer les maintenances avec coût
        df_maint = data['maintenances'][data['maintenances']['cout'].notna() & (data['maintenances']['cout'] > 0)].copy()

        if df_maint.empty:
            return self._create_no_data_chart("Aucune donnée de coût disponible")

        # 1. Évolution des coûts avec moyennes mobiles
        df_maint_sorted = df_maint.sort_values('date_maintenance')
        df_maint_sorted['cout_cumule'] = df_maint_sorted['cout'].cumsum()
        df_maint_sorted['moyenne_mobile_7'] = df_maint_sorted['cout'].rolling(window=7, min_periods=1).mean()
        df_maint_sorted['moyenne_mobile_30'] = df_maint_sorted['cout'].rolling(window=30, min_periods=1).mean()

        ax1.plot(df_maint_sorted['date_maintenance'], df_maint_sorted['cout'],
                alpha=0.3, color=self.colors['secondary'], label='Coût individuel')
        ax1.plot(df_maint_sorted['date_maintenance'], df_maint_sorted['moyenne_mobile_7'],
                color=self.colors['primary'], linewidth=2, label='Moyenne mobile 7j')
        ax1.plot(df_maint_sorted['date_maintenance'], df_maint_sorted['moyenne_mobile_30'],
                color=self.colors['danger'], linewidth=2, label='Moyenne mobile 30j')

        ax1.set_title('Évolution des Coûts avec Tendances', fontsize=14, pad=20)
        ax1.set_ylabel('Coût (MAD)')
        ax1.legend()
        ax1.tick_params(axis='x', rotation=45)

        # 2. Analyse des coûts par catégorie avec budget
        cout_par_type = df_maint.groupby('type_maintenance').agg({
            'cout': ['sum', 'mean', 'count']
        }).round(2)
        cout_par_type.columns = ['total', 'moyenne', 'frequence']
        cout_par_type = cout_par_type.reset_index()

        # Budget théorique (exemple)
        budgets_theoriques = {
            'Révision': 5000,
            'Vidange': 2000,
            'Réparation': 8000,
            'Contrôle technique': 1000,
            'Pneus': 3000
        }

        x_pos = range(len(cout_par_type))
        bars_reel = ax2.bar([x - 0.2 for x in x_pos], cout_par_type['total'],
                           width=0.4, label='Coût réel', color=self.colors['primary'], alpha=0.8)

        budgets = [budgets_theoriques.get(type_maint, cout_par_type['total'].mean())
                  for type_maint in cout_par_type['type_maintenance']]
        bars_budget = ax2.bar([x + 0.2 for x in x_pos], budgets,
                             width=0.4, label='Budget théorique', color=self.colors['success'], alpha=0.8)

        ax2.set_title('Coûts Réels vs Budget Théorique', fontsize=14, pad=20)
        ax2.set_ylabel('Coût total (MAD)')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(cout_par_type['type_maintenance'], rotation=45)
        ax2.legend()

        # Ajouter les écarts en pourcentage
        for i, (reel, budget) in enumerate(zip(cout_par_type['total'], budgets)):
            ecart = ((reel - budget) / budget) * 100 if budget > 0 else 0
            color = self.colors['danger'] if ecart > 0 else self.colors['success']
            ax2.text(i, max(reel, budget) + max(cout_par_type['total']) * 0.05,
                    f'{ecart:+.1f}%', ha='center', va='bottom',
                    color=color, fontweight='bold', fontsize=10)

        # 3. Analyse de rentabilité par véhicule
        rentabilite_vehicules = []
        for _, vehicule in data['vehicules'].iterrows():
            maintenances_vehicule = df_maint[df_maint['vehicule_id'] == vehicule['id']]

            if not maintenances_vehicule.empty:
                cout_total = maintenances_vehicule['cout'].sum()
                age = 2025 - vehicule['annee'] if pd.notna(vehicule['annee']) else 1
                km = vehicule['kilometrage'] if pd.notna(vehicule['kilometrage']) else 1

                # Calculs de rentabilité
                cout_par_an = cout_total / max(age, 1)
                cout_par_km = cout_total / max(km, 1)

                # Estimation de la valeur résiduelle (simplifiée)
                valeur_initiale_estimee = 150000  # Estimation moyenne
                depreciation_annuelle = 0.15
                valeur_residuelle = valeur_initiale_estimee * ((1 - depreciation_annuelle) ** age)

                rentabilite = valeur_residuelle - cout_total

                rentabilite_vehicules.append({
                    'immatriculation': vehicule['immatriculation'],
                    'cout_total': cout_total,
                    'cout_par_an': cout_par_an,
                    'cout_par_km': cout_par_km * 1000,  # Par 1000 km
                    'valeur_residuelle': valeur_residuelle,
                    'rentabilite': rentabilite
                })

        if rentabilite_vehicules:
            df_rentabilite = pd.DataFrame(rentabilite_vehicules)

            # Graphique de rentabilité
            colors_rentabilite = [self.colors['success'] if r > 0 else self.colors['danger']
                                 for r in df_rentabilite['rentabilite']]

            bars = ax3.bar(range(len(df_rentabilite)), df_rentabilite['rentabilite'],
                          color=colors_rentabilite, alpha=0.8)
            ax3.set_title('Rentabilité par Véhicule\n(Valeur résiduelle - Coûts maintenance)', fontsize=14, pad=20)
            ax3.set_ylabel('Rentabilité (MAD)')
            ax3.set_xticks(range(len(df_rentabilite)))
            ax3.set_xticklabels(df_rentabilite['immatriculation'], rotation=45)
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)

            # Ajouter les valeurs
            for i, bar in enumerate(bars):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2.,
                        height + (abs(height) * 0.02 if height >= 0 else -abs(height) * 0.02),
                        f'{height:,.0f}', ha='center',
                        va='bottom' if height >= 0 else 'top',
                        fontweight='bold', fontsize=9)

        # 4. Prévisions budgétaires
        if len(df_maint) > 12:  # Assez de données pour une prévision
            # Grouper par mois pour la prévision
            df_maint['mois'] = df_maint['date_maintenance'].dt.to_period('M')
            cout_mensuel = df_maint.groupby('mois')['cout'].sum()

            # Prévision simple avec tendance linéaire
            x_vals = np.arange(len(cout_mensuel))
            y_vals = cout_mensuel.values

            # Régression linéaire
            z = np.polyfit(x_vals, y_vals, 1)
            p = np.poly1d(z)

            # Prévision pour les 6 prochains mois
            x_future = np.arange(len(cout_mensuel), len(cout_mensuel) + 6)
            y_future = p(x_future)

            # Graphique historique et prévision
            ax4.plot(x_vals, y_vals, marker='o', linewidth=2, markersize=6,
                    color=self.colors['primary'], label='Historique')
            ax4.plot(x_future, y_future, marker='s', linewidth=2, markersize=6,
                    color=self.colors['warning'], linestyle='--', label='Prévision')

            # Zone de confiance (estimation simple)
            std_error = np.std(y_vals - p(x_vals))
            ax4.fill_between(x_future, y_future - std_error, y_future + std_error,
                           alpha=0.3, color=self.colors['warning'])

            ax4.set_title('Prévisions Budgétaires (6 mois)', fontsize=14, pad=20)
            ax4.set_ylabel('Coût mensuel (MAD)')
            ax4.set_xlabel('Période')
            ax4.legend()

            # Calcul du budget prévisionnel total
            budget_previsionnel = np.sum(y_future)
            ax4.text(0.02, 0.98, f'Budget prévisionnel 6 mois:\n{budget_previsionnel:,.0f} MAD',
                    transform=ax4.transAxes, fontsize=11, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8),
                    verticalalignment='top')

        plt.tight_layout()
        return self._fig_to_base64(fig)

    def create_comprehensive_report(self) -> Dict[str, str]:
        """
        Génère un rapport complet avec tous les graphiques incluant les nouvelles analyses

        Returns:
            Dictionnaire avec les graphiques en base64
        """
        report = {}

        try:
            report['evolution'] = self.create_maintenance_evolution_chart()
        except Exception as e:
            print(f"Erreur évolution: {e}")
            report['evolution'] = self._create_no_data_chart("Erreur génération évolution")

        try:
            report['dashboard'] = self.create_vehicle_analysis_dashboard()
        except Exception as e:
            print(f"Erreur dashboard: {e}")
            report['dashboard'] = self._create_no_data_chart("Erreur génération dashboard")

        try:
            report['costs'] = self.create_maintenance_cost_analysis()
        except Exception as e:
            print(f"Erreur coûts: {e}")
            report['costs'] = self._create_no_data_chart("Erreur génération analyse coûts")

        try:
            report['heatmap'] = self.create_performance_heatmap()
        except Exception as e:
            print(f"Erreur heatmap: {e}")
            report['heatmap'] = self._create_no_data_chart("Erreur génération heatmap")

        try:
            report['predictive'] = self.create_predictive_analysis()
        except Exception as e:
            print(f"Erreur prédictive: {e}")
            report['predictive'] = self._create_no_data_chart("Erreur génération analyse prédictive")

        # Nouvelles analyses avancées
        try:
            report['advanced_fleet'] = self.create_advanced_fleet_analytics()
        except Exception as e:
            print(f"Erreur analytics avancés: {e}")
            report['advanced_fleet'] = self._create_no_data_chart("Erreur génération analytics avancés")

        try:
            report['financial'] = self.create_financial_dashboard()
        except Exception as e:
            print(f"Erreur dashboard financier: {e}")
            report['financial'] = self._create_no_data_chart("Erreur génération dashboard financier")

        try:
            report['correlation'] = self.create_correlation_analysis()
        except Exception as e:
            print(f"Erreur analyse corrélation: {e}")
            report['correlation'] = self._create_no_data_chart("Erreur génération analyse corrélation")

        try:
            report['operational'] = self.create_operational_efficiency_dashboard()
        except Exception as e:
            print(f"Erreur dashboard efficacité: {e}")
            report['operational'] = self._create_no_data_chart("Erreur génération dashboard efficacité")

        return report

    def create_correlation_analysis(self) -> str:
        """
        Analyse de corrélation entre différentes métriques

        Returns:
            String base64 de l'image de l'analyse de corrélation
        """
        data = self.get_data()

        if data['vehicules'].empty or data['maintenances'].empty:
            return self._create_no_data_chart("Données insuffisantes pour l'analyse de corrélation")

        # Préparer les données pour l'analyse de corrélation
        vehicules_data = []
        for _, vehicule in data['vehicules'].iterrows():
            maintenances_vehicule = data['maintenances'][
                data['maintenances']['vehicule_id'] == vehicule['id']
            ]

            age = 2025 - vehicule['annee'] if pd.notna(vehicule['annee']) else 0
            km = vehicule['kilometrage'] if pd.notna(vehicule['kilometrage']) else 0
            nb_maintenances = len(maintenances_vehicule)
            cout_total = maintenances_vehicule['cout'].sum() if not maintenances_vehicule.empty else 0
            cout_moyen = maintenances_vehicule['cout'].mean() if not maintenances_vehicule.empty else 0

            # Calcul de métriques dérivées
            cout_par_km = (cout_total / km) * 1000 if km > 0 else 0  # Coût par 1000 km
            cout_par_an = cout_total / age if age > 0 else 0
            frequence_maintenance = nb_maintenances / age if age > 0 else 0

            vehicules_data.append({
                'age': age,
                'kilometrage': km,
                'nb_maintenances': nb_maintenances,
                'cout_total': cout_total,
                'cout_moyen': cout_moyen,
                'cout_par_km': cout_par_km,
                'cout_par_an': cout_par_an,
                'frequence_maintenance': frequence_maintenance
            })

        df_corr = pd.DataFrame(vehicules_data)

        if df_corr.empty or len(df_corr) < 3:
            return self._create_no_data_chart("Données insuffisantes pour l'analyse de corrélation")

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
        fig.suptitle('Analyse de Corrélation - Métriques de Performance', fontsize=22, fontweight='bold')

        # 1. Matrice de corrélation
        correlation_matrix = df_corr.corr()

        im = ax1.imshow(correlation_matrix, cmap='RdBu_r', aspect='auto', vmin=-1, vmax=1)
        ax1.set_xticks(range(len(correlation_matrix.columns)))
        ax1.set_yticks(range(len(correlation_matrix.columns)))
        ax1.set_xticklabels(correlation_matrix.columns, rotation=45, ha='right')
        ax1.set_yticklabels(correlation_matrix.columns)
        ax1.set_title('Matrice de Corrélation', fontsize=14, pad=20)

        # Ajouter les valeurs de corrélation
        for i in range(len(correlation_matrix.columns)):
            for j in range(len(correlation_matrix.columns)):
                text = ax1.text(j, i, f'{correlation_matrix.iloc[i, j]:.2f}',
                               ha="center", va="center", color="black", fontweight='bold')

        # Colorbar
        cbar = plt.colorbar(im, ax=ax1)
        cbar.set_label('Coefficient de corrélation', rotation=270, labelpad=20)

        # 2. Scatter plot: Âge vs Coût total avec régression
        if len(df_corr) > 2:
            ax2.scatter(df_corr['age'], df_corr['cout_total'], alpha=0.7,
                       s=100, color=self.colors['primary'], edgecolors='black')

            # Ligne de régression
            if df_corr['age'].std() > 0 and df_corr['cout_total'].std() > 0:
                z = np.polyfit(df_corr['age'], df_corr['cout_total'], 1)
                p = np.poly1d(z)
                ax2.plot(df_corr['age'], p(df_corr['age']),
                        color=self.colors['danger'], linewidth=2, linestyle='--')

                # R² et équation
                correlation = np.corrcoef(df_corr['age'], df_corr['cout_total'])[0,1]
                r_squared = correlation ** 2
                ax2.text(0.05, 0.95, f'R² = {r_squared:.3f}\ny = {z[0]:.1f}x + {z[1]:.1f}',
                        transform=ax2.transAxes, fontsize=12,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

            ax2.set_title('Corrélation Âge vs Coût Total', fontsize=14, pad=20)
            ax2.set_xlabel('Âge du véhicule (années)')
            ax2.set_ylabel('Coût total maintenance (MAD)')

        # 3. Scatter plot: Kilométrage vs Fréquence de maintenance
        if len(df_corr) > 2:
            scatter = ax3.scatter(df_corr['kilometrage'], df_corr['frequence_maintenance'],
                                c=df_corr['cout_total'], s=100, cmap='viridis',
                                alpha=0.7, edgecolors='black')

            ax3.set_title('Kilométrage vs Fréquence Maintenance\n(Couleur = Coût total)', fontsize=14, pad=20)
            ax3.set_xlabel('Kilométrage')
            ax3.set_ylabel('Fréquence maintenance (par an)')

            # Colorbar
            cbar3 = plt.colorbar(scatter, ax=ax3)
            cbar3.set_label('Coût total (MAD)', rotation=270, labelpad=20)

        # 4. Analyse des outliers (box plots multiples)
        metrics_to_analyze = ['cout_par_km', 'cout_par_an', 'frequence_maintenance']
        data_for_box = [df_corr[metric].dropna() for metric in metrics_to_analyze]

        box_plot = ax4.boxplot(data_for_box, labels=['Coût/1000km', 'Coût/an', 'Fréq/an'],
                              patch_artist=True, showfliers=True)

        # Colorer les boîtes
        colors_box = [self.colors['primary'], self.colors['success'], self.colors['warning']]
        for patch, color in zip(box_plot['boxes'], colors_box):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        ax4.set_title('Détection d\'Outliers - Métriques Clés', fontsize=14, pad=20)
        ax4.set_ylabel('Valeur normalisée')

        # Identifier et marquer les outliers
        for i, metric in enumerate(metrics_to_analyze):
            Q1 = df_corr[metric].quantile(0.25)
            Q3 = df_corr[metric].quantile(0.75)
            IQR = Q3 - Q1
            outliers = df_corr[(df_corr[metric] < Q1 - 1.5*IQR) | (df_corr[metric] > Q3 + 1.5*IQR)]

            if not outliers.empty:
                ax4.text(i+1, ax4.get_ylim()[1] * 0.9, f'{len(outliers)} outliers',
                        ha='center', va='center', fontsize=10, fontweight='bold',
                        bbox=dict(boxstyle="round,pad=0.2", facecolor='red', alpha=0.3))

        plt.tight_layout()
        return self._fig_to_base64(fig)

    def create_operational_efficiency_dashboard(self) -> str:
        """
        Dashboard d'efficacité opérationnelle avec KPIs avancés

        Returns:
            String base64 de l'image du dashboard d'efficacité
        """
        data = self.get_data()

        if data['vehicules'].empty:
            return self._create_no_data_chart("Aucune donnée opérationnelle disponible")

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
        fig.suptitle('Dashboard d\'Efficacité Opérationnelle', fontsize=22, fontweight='bold')

        # 1. KPIs de performance avec jauges
        total_vehicules = len(data['vehicules'])
        vehicules_disponibles = len(data['vehicules'][data['vehicules']['statut'] == 'disponible'])
        vehicules_affectes = len(data['vehicules'][data['vehicules']['statut'] == 'affecte'])
        vehicules_maintenance = len(data['vehicules'][data['vehicules']['statut'] == 'en_maintenance'])

        # Calcul des KPIs
        taux_disponibilite = (vehicules_disponibles / total_vehicules) * 100
        taux_utilisation = (vehicules_affectes / total_vehicules) * 100
        taux_maintenance = (vehicules_maintenance / total_vehicules) * 100

        # Graphique en jauge (simulation avec des arcs)
        kpis = [
            ('Disponibilité', taux_disponibilite, 70, self.colors['success']),
            ('Utilisation', taux_utilisation, 60, self.colors['primary']),
            ('Maintenance', taux_maintenance, 15, self.colors['warning'])
        ]

        angles = np.linspace(0, np.pi, 100)

        for i, (nom, valeur, objectif, couleur) in enumerate(kpis):
            # Arc de fond
            ax1.plot(angles, np.ones_like(angles) * (i + 1),
                    color='lightgray', linewidth=20, alpha=0.3)

            # Arc de valeur
            valeur_norm = min(valeur / 100, 1.0)
            angles_valeur = angles[:int(valeur_norm * len(angles))]
            ax1.plot(angles_valeur, np.ones_like(angles_valeur) * (i + 1),
                    color=couleur, linewidth=20, alpha=0.8)

            # Texte
            ax1.text(np.pi/2, i + 1, f'{nom}\n{valeur:.1f}%\n(Obj: {objectif}%)',
                    ha='center', va='center', fontsize=11, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

        ax1.set_xlim(0, np.pi)
        ax1.set_ylim(0.5, 3.5)
        ax1.set_title('KPIs de Performance', fontsize=14, pad=20)
        ax1.axis('off')

        # 2. Analyse de la productivité par marque
        if not data['maintenances'].empty:
            # Calculer les statistiques de maintenance par véhicule
            maintenance_stats = data['maintenances'].groupby('vehicule_id').agg({
                'cout': ['sum', 'count'],
                'date_maintenance': 'count'
            }).round(2)

            # Aplatir les colonnes multi-niveaux
            maintenance_stats.columns = ['cout_total', 'nb_maintenances', 'freq_maintenance']
            maintenance_stats = maintenance_stats.reset_index()

            # Joindre avec les véhicules
            vehicules_maintenances = data['vehicules'].merge(
                maintenance_stats, left_on='id', right_on='vehicule_id', how='left'
            )
            vehicules_maintenances = vehicules_maintenances.fillna(0)

            # Productivité par marque
            productivite_marque = vehicules_maintenances.groupby('marque').agg({
                'cout_total': 'mean',
                'nb_maintenances': 'mean',
                'kilometrage': 'mean'
            }).round(2)

            # Calcul d'un score de productivité (inverse du coût par km)
            productivite_marque['score_productivite'] = (
                productivite_marque['kilometrage'] /
                (productivite_marque['cout_total'] + 1)
            ) * 100

            productivite_marque = productivite_marque.sort_values('score_productivite', ascending=False)

            # Graphique en barres avec double axe
            ax2_twin = ax2.twinx()

            bars = ax2.bar(range(len(productivite_marque)), productivite_marque['score_productivite'],
                          alpha=0.7, color=self.colors['success'], label='Score productivité')
            line = ax2_twin.plot(range(len(productivite_marque)), productivite_marque['cout_total'],
                               color=self.colors['danger'], marker='o', linewidth=2,
                               markersize=8, label='Coût moyen')

            ax2.set_title('Productivité par Marque', fontsize=14, pad=20)
            ax2.set_ylabel('Score de productivité', color=self.colors['success'])
            ax2_twin.set_ylabel('Coût moyen (MAD)', color=self.colors['danger'])
            ax2.set_xticks(range(len(productivite_marque)))
            ax2.set_xticklabels(productivite_marque.index, rotation=45)

            # Légendes
            lines1, labels1 = ax2.get_legend_handles_labels()
            lines2, labels2 = ax2_twin.get_legend_handles_labels()
            ax2.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

        # 3. Analyse temporelle de l'efficacité
        if not data['maintenances'].empty:
            df_maint = data['maintenances'].copy()
            df_maint['mois'] = df_maint['date_maintenance'].dt.to_period('M')

            # Métriques mensuelles
            efficacite_mensuelle = df_maint.groupby('mois').agg({
                'cout': ['sum', 'mean', 'count'],
                'vehicule_id': 'nunique'
            }).round(2)

            efficacite_mensuelle.columns = ['cout_total', 'cout_moyen', 'nb_maintenances', 'nb_vehicules']
            efficacite_mensuelle = efficacite_mensuelle.reset_index()

            # Calcul de l'efficacité (moins de coût = plus d'efficacité)
            efficacite_mensuelle['efficacite'] = (
                efficacite_mensuelle['nb_vehicules'] /
                (efficacite_mensuelle['cout_moyen'] / 1000 + 1)
            )

            # Graphique temporel
            ax3.plot(range(len(efficacite_mensuelle)), efficacite_mensuelle['efficacite'],
                    marker='o', linewidth=3, markersize=8, color=self.colors['primary'])
            ax3.fill_between(range(len(efficacite_mensuelle)), efficacite_mensuelle['efficacite'],
                           alpha=0.3, color=self.colors['primary'])

            # Ligne de tendance
            if len(efficacite_mensuelle) > 2:
                x_vals = np.arange(len(efficacite_mensuelle))
                z = np.polyfit(x_vals, efficacite_mensuelle['efficacite'], 1)
                p = np.poly1d(z)
                ax3.plot(x_vals, p(x_vals), color=self.colors['danger'],
                        linewidth=2, linestyle='--', label='Tendance')

                # Afficher la pente
                pente = z[0]
                tendance = "↗ Amélioration" if pente > 0 else "↘ Dégradation" if pente < 0 else "→ Stable"
                ax3.text(0.02, 0.98, f'Tendance: {tendance}',
                        transform=ax3.transAxes, fontsize=12, fontweight='bold',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8),
                        verticalalignment='top')

            ax3.set_title('Évolution de l\'Efficacité Opérationnelle', fontsize=14, pad=20)
            ax3.set_ylabel('Indice d\'efficacité')
            ax3.set_xlabel('Période')
            ax3.set_xticks(range(len(efficacite_mensuelle)))
            ax3.set_xticklabels([str(m) for m in efficacite_mensuelle['mois']], rotation=45)
            ax3.legend()

        # 4. Radar chart des performances par catégorie
        if not data['maintenances'].empty:
            # Calculer les métriques par type de maintenance
            performance_types = data['maintenances'].groupby('type_maintenance').agg({
                'cout': ['mean', 'count'],
                'vehicule_id': 'nunique'
            }).round(2)

            performance_types.columns = ['cout_moyen', 'frequence', 'nb_vehicules']

            # Normaliser les métriques (0-1)
            for col in performance_types.columns:
                max_val = performance_types[col].max()
                if max_val > 0:
                    performance_types[f'{col}_norm'] = performance_types[col] / max_val

            # Créer le radar chart
            categories = list(performance_types.index)
            N = len(categories)

            if N > 2:
                angles = [n / float(N) * 2 * np.pi for n in range(N)]
                angles += angles[:1]  # Fermer le cercle

                # Données pour le radar
                cout_norm = list(performance_types['cout_moyen_norm']) + [performance_types['cout_moyen_norm'].iloc[0]]
                freq_norm = list(performance_types['frequence_norm']) + [performance_types['frequence_norm'].iloc[0]]

                # Créer le subplot polaire
                ax4 = plt.subplot(2, 2, 4, projection='polar')

                # Tracer les lignes
                ax4.plot(angles, cout_norm, 'o-', linewidth=2, label='Coût moyen', color=self.colors['danger'])
                ax4.fill(angles, cout_norm, alpha=0.25, color=self.colors['danger'])

                ax4.plot(angles, freq_norm, 'o-', linewidth=2, label='Fréquence', color=self.colors['primary'])
                ax4.fill(angles, freq_norm, alpha=0.25, color=self.colors['primary'])

                # Ajouter les labels
                ax4.set_xticks(angles[:-1])
                ax4.set_xticklabels(categories)
                ax4.set_ylim(0, 1)
                ax4.set_title('Performance par Type de Maintenance', fontsize=14, pad=20)
                ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

        plt.tight_layout()
        return self._fig_to_base64(fig)
