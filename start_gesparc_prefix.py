#!/usr/bin/env python3
"""
Script de démarrage de GesParc Auto avec préfixe /gesparc
Permet de tester l'application avec le préfixe sans Apache
"""

import os
import sys
import subprocess
from pathlib import Path

def start_with_prefix():
    """Démarre GesParc Auto avec le préfixe /gesparc"""
    
    print("🚀 Démarrage de GesParc Auto avec préfixe /gesparc")
    print("=" * 60)
    
    # Définir la variable d'environnement pour activer le préfixe
    os.environ['GESPARC_USE_PREFIX'] = 'true'
    os.environ['SCRIPT_NAME'] = '/gesparc'
    
    # Vérifier que le fichier principal existe
    app_file = Path('gesparc_app.py')
    if not app_file.exists():
        print("❌ Erreur: gesparc_app.py non trouvé")
        print("💡 Assurez-vous d'être dans le bon répertoire")
        return False
    
    print("📍 Configuration:")
    print(f"   Répertoire: {os.getcwd()}")
    print(f"   Préfixe: /gesparc")
    print(f"   Port: 5001")
    print(f"   URL: http://localhost:5001/gesparc/")
    print()
    
    print("🔧 Variables d'environnement:")
    print(f"   GESPARC_USE_PREFIX = {os.environ.get('GESPARC_USE_PREFIX')}")
    print(f"   SCRIPT_NAME = {os.environ.get('SCRIPT_NAME')}")
    print()
    
    print("⚠️  IMPORTANT:")
    print("   Avec le préfixe activé, l'application sera accessible à:")
    print("   🌐 http://localhost:5001/gesparc/")
    print("   ❌ http://localhost:5001/ ne fonctionnera PAS")
    print()
    
    try:
        print("🚀 Démarrage de l'application...")
        print("   Appuyez sur Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Démarrer l'application avec les arguments de préfixe
        subprocess.run([sys.executable, 'gesparc_app.py', '--prefix'], 
                      env=os.environ.copy())
        
    except KeyboardInterrupt:
        print("\n🛑 Arrêt de l'application demandé")
        return True
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        return False

def start_normal():
    """Démarre GesParc Auto sans préfixe (mode normal)"""
    
    print("🚀 Démarrage de GesParc Auto (mode normal)")
    print("=" * 50)
    
    # S'assurer que les variables de préfixe ne sont pas définies
    os.environ.pop('GESPARC_USE_PREFIX', None)
    os.environ.pop('SCRIPT_NAME', None)
    
    print("📍 Configuration:")
    print(f"   Répertoire: {os.getcwd()}")
    print(f"   Mode: Normal (sans préfixe)")
    print(f"   Port: 5001")
    print(f"   URL: http://localhost:5001/")
    print()
    
    try:
        print("🚀 Démarrage de l'application...")
        print("   Appuyez sur Ctrl+C pour arrêter")
        print("=" * 50)
        
        subprocess.run([sys.executable, 'gesparc_app.py'], 
                      env=os.environ.copy())
        
    except KeyboardInterrupt:
        print("\n🛑 Arrêt de l'application demandé")
        return True
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        return False

def show_help():
    """Affiche l'aide"""
    print("🔧 Script de Démarrage GesParc Auto")
    print("=" * 40)
    print()
    print("Usage:")
    print("  python start_gesparc_prefix.py [option]")
    print()
    print("Options:")
    print("  --prefix    Démarrer avec préfixe /gesparc")
    print("  --normal    Démarrer en mode normal")
    print("  --help      Afficher cette aide")
    print()
    print("Exemples:")
    print("  python start_gesparc_prefix.py --prefix")
    print("  python start_gesparc_prefix.py --normal")
    print()
    print("URLs d'accès:")
    print("  Mode préfixe: http://localhost:5001/gesparc/")
    print("  Mode normal:  http://localhost:5001/")

def test_configuration():
    """Teste la configuration avec et sans préfixe"""
    print("🧪 Test de Configuration GesParc Auto")
    print("=" * 45)
    
    import requests
    import time
    
    # Test 1: Mode normal
    print("\n1. Test du mode normal...")
    os.environ.pop('GESPARC_USE_PREFIX', None)
    os.environ.pop('SCRIPT_NAME', None)
    
    # Démarrer en arrière-plan pour test
    print("   Démarrage temporaire pour test...")
    
    # Test 2: Mode préfixe
    print("\n2. Test du mode préfixe...")
    os.environ['GESPARC_USE_PREFIX'] = 'true'
    os.environ['SCRIPT_NAME'] = '/gesparc'
    
    print("   Configuration préfixe activée")
    print("   URL attendue: http://localhost:5001/gesparc/")

def main():
    """Fonction principale"""
    
    if len(sys.argv) == 1:
        # Aucun argument, demander à l'utilisateur
        print("🔧 GesParc Auto - Sélection du Mode de Démarrage")
        print("=" * 55)
        print()
        print("Choisissez le mode de démarrage:")
        print("  1. Mode normal (http://localhost:5001/)")
        print("  2. Mode préfixe (http://localhost:5001/gesparc/)")
        print("  3. Aide")
        print("  4. Test de configuration")
        print()
        
        choice = input("Votre choix (1-4): ").strip()
        
        if choice == '1':
            start_normal()
        elif choice == '2':
            start_with_prefix()
        elif choice == '3':
            show_help()
        elif choice == '4':
            test_configuration()
        else:
            print("❌ Choix invalide")
            show_help()
    
    elif '--prefix' in sys.argv:
        start_with_prefix()
    elif '--normal' in sys.argv:
        start_normal()
    elif '--help' in sys.argv or '-h' in sys.argv:
        show_help()
    elif '--test' in sys.argv:
        test_configuration()
    else:
        print("❌ Option non reconnue")
        show_help()

if __name__ == "__main__":
    main()
