#!/usr/bin/env python3
"""
Test des nouvelles fonctionnalités d'analytics avancées
"""

import sys
import os
import traceback
from matplotlib_analytics import GesparcAnalytics

def test_advanced_analytics():
    """Test complet des nouvelles analyses"""
    print("🧪 Test des Analytics Avancées avec Matplotlib")
    print("=" * 60)
    
    try:
        # Initialiser le générateur d'analytics
        analytics = GesparcAnalytics()
        print("✅ Générateur d'analytics initialisé")
        
        # Test des données
        print("\n📊 Test de récupération des données...")
        data = analytics.get_data()
        
        print(f"  - Véhicules: {len(data['vehicules'])} entrées")
        print(f"  - Maintenances: {len(data['maintenances'])} entrées")
        print(f"  - Conducteurs: {len(data['conducteurs'])} entrées")
        
        # Test des nouvelles analyses
        analyses_to_test = [
            ('advanced_fleet_analytics', 'Analytics Avancés de la Flotte'),
            ('financial_dashboard', 'Dashboard Financier'),
            ('correlation_analysis', 'Analyse de Corrélation'),
            ('operational_efficiency_dashboard', 'Dashboard d\'Efficacité Opérationnelle')
        ]
        
        print(f"\n🔬 Test des {len(analyses_to_test)} nouvelles analyses...")
        
        results = {}
        for method_name, description in analyses_to_test:
            print(f"\n  🔄 Test: {description}")
            try:
                method = getattr(analytics, f'create_{method_name}')
                result = method()
                
                if result and len(result) > 100:  # Vérifier qu'on a une image base64
                    print(f"    ✅ {description} - Généré avec succès")
                    print(f"    📏 Taille: {len(result)} caractères")
                    results[method_name] = True
                else:
                    print(f"    ⚠️  {description} - Résultat vide ou invalide")
                    results[method_name] = False
                    
            except Exception as e:
                print(f"    ❌ {description} - Erreur: {str(e)}")
                results[method_name] = False
                traceback.print_exc()
        
        # Test du rapport complet
        print(f"\n📋 Test du rapport complet...")
        try:
            comprehensive_report = analytics.create_comprehensive_report()
            
            print(f"  📊 Graphiques générés: {len(comprehensive_report)}")
            for chart_name, chart_data in comprehensive_report.items():
                status = "✅" if chart_data and len(chart_data) > 100 else "❌"
                print(f"    {status} {chart_name}")
            
            print("  ✅ Rapport complet généré avec succès")
            
        except Exception as e:
            print(f"  ❌ Erreur rapport complet: {str(e)}")
            traceback.print_exc()
        
        # Résumé des résultats
        print(f"\n📈 Résumé des Tests:")
        print(f"  🎯 Total analyses testées: {len(analyses_to_test)}")
        print(f"  ✅ Succès: {sum(results.values())}")
        print(f"  ❌ Échecs: {len(results) - sum(results.values())}")
        
        success_rate = (sum(results.values()) / len(results)) * 100
        print(f"  📊 Taux de réussite: {success_rate:.1f}%")
        
        if success_rate >= 75:
            print(f"\n🎉 Tests réussis! Les analytics avancées sont opérationnelles.")
        else:
            print(f"\n⚠️  Certaines analyses ont échoué. Vérifiez les données.")
        
        # Informations sur les dépendances
        print(f"\n🔧 Informations techniques:")
        try:
            import matplotlib
            import seaborn
            import pandas
            import numpy
            print(f"  📦 Matplotlib: {matplotlib.__version__}")
            print(f"  📦 Seaborn: {seaborn.__version__}")
            print(f"  📦 Pandas: {pandas.__version__}")
            print(f"  📦 NumPy: {numpy.__version__}")
        except ImportError as e:
            print(f"  ❌ Dépendance manquante: {e}")
        
        return success_rate >= 75
        
    except Exception as e:
        print(f"❌ Erreur critique lors du test: {e}")
        traceback.print_exc()
        return False

def test_specific_features():
    """Test de fonctionnalités spécifiques"""
    print(f"\n🔍 Tests de fonctionnalités spécifiques...")
    
    try:
        analytics = GesparcAnalytics()
        
        # Test de la configuration des styles
        print("  🎨 Test configuration des styles...")
        analytics.setup_style()
        print("    ✅ Styles configurés")
        
        # Test de récupération des données avec gestion d'erreurs
        print("  📊 Test récupération données robuste...")
        data = analytics.get_data()
        
        # Vérifications de cohérence des données
        if not data['vehicules'].empty:
            print(f"    ✅ Véhicules: colonnes {list(data['vehicules'].columns)}")
        
        if not data['maintenances'].empty:
            print(f"    ✅ Maintenances: {len(data['maintenances'])} avec dates converties")
        
        # Test de génération d'image vide
        print("  🖼️  Test génération graphique vide...")
        no_data_chart = analytics._create_no_data_chart("Test message")
        if no_data_chart and len(no_data_chart) > 100:
            print("    ✅ Graphique 'no data' généré")
        
        print("  ✅ Tests spécifiques réussis")
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur tests spécifiques: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Démarrage des tests d'analytics avancées...")
    
    # Test principal
    main_success = test_advanced_analytics()
    
    # Tests spécifiques
    specific_success = test_specific_features()
    
    # Résultat final
    print(f"\n" + "="*60)
    if main_success and specific_success:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("✅ Les analytics avancées sont prêtes à l'utilisation.")
        sys.exit(0)
    else:
        print("⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez la configuration et les données.")
        sys.exit(1)
