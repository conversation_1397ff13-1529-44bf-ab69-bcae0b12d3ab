#!/usr/bin/env python3
"""
Utilitaires d'export pour GesParc Auto
Support des formats CSV et Excel (XLS/XLSX)
"""

import csv
import io
from datetime import datetime
from flask import Response
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import xlwt

def export_to_csv(data, headers, filename):
    """
    Exporte des données au format CSV
    
    Args:
        data: Liste de dictionnaires ou tuples contenant les données
        headers: Liste des en-têtes de colonnes
        filename: Nom du fichier (sans extension)
    
    Returns:
        Response Flask avec le fichier CSV
    """
    output = io.StringIO()
    writer = csv.writer(output, delimiter=';', quoting=csv.QUOTE_ALL)
    
    # Écrire les en-têtes
    writer.writerow(headers)
    
    # Écrire les données
    for row in data:
        if isinstance(row, dict):
            # Si c'est un dictionnaire, extraire les valeurs dans l'ordre des headers
            csv_row = []
            for header in headers:
                # Convertir les clés d'en-tête en clés de base de données
                key = header_to_db_key(header)
                value = row.get(key, '')
                csv_row.append(format_value_for_export(value))
            writer.writerow(csv_row)
        else:
            # Si c'est un tuple ou une liste
            formatted_row = [format_value_for_export(value) for value in row]
            writer.writerow(formatted_row)
    
    # Créer la réponse
    output.seek(0)
    response = Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={
            'Content-Disposition': f'attachment; filename={filename}.csv',
            'Content-Type': 'text/csv; charset=utf-8'
        }
    )
    
    return response

def export_to_excel_xlsx(data, headers, filename, sheet_name="Données"):
    """
    Exporte des données au format Excel XLSX (moderne)
    
    Args:
        data: Liste de dictionnaires ou tuples contenant les données
        headers: Liste des en-têtes de colonnes
        filename: Nom du fichier (sans extension)
        sheet_name: Nom de la feuille Excel
    
    Returns:
        Response Flask avec le fichier Excel
    """
    # Créer un nouveau classeur
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = sheet_name
    
    # Styles pour les en-têtes
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Écrire les en-têtes
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Écrire les données
    for row_idx, row in enumerate(data, 2):
        if isinstance(row, dict):
            for col_idx, header in enumerate(headers, 1):
                key = header_to_db_key(header)
                value = row.get(key, '')
                ws.cell(row=row_idx, column=col_idx, value=format_value_for_export(value))
        else:
            for col_idx, value in enumerate(row, 1):
                ws.cell(row=row_idx, column=col_idx, value=format_value_for_export(value))
    
    # Ajuster la largeur des colonnes
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Sauvegarder dans un buffer
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)
    
    # Créer la réponse
    response = Response(
        output.getvalue(),
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={
            'Content-Disposition': f'attachment; filename={filename}.xlsx',
        }
    )
    
    return response

def export_to_excel_xls(data, headers, filename, sheet_name="Données"):
    """
    Exporte des données au format Excel XLS (compatible anciennes versions)
    
    Args:
        data: Liste de dictionnaires ou tuples contenant les données
        headers: Liste des en-têtes de colonnes
        filename: Nom du fichier (sans extension)
        sheet_name: Nom de la feuille Excel
    
    Returns:
        Response Flask avec le fichier Excel
    """
    # Créer un nouveau classeur
    wb = xlwt.Workbook(encoding='utf-8')
    ws = wb.add_sheet(sheet_name)
    
    # Styles pour les en-têtes
    header_style = xlwt.XFStyle()
    header_font = xlwt.Font()
    header_font.bold = True
    header_font.colour_index = xlwt.Style.colour_map['white']
    header_style.font = header_font
    
    header_pattern = xlwt.Pattern()
    header_pattern.pattern = xlwt.Pattern.SOLID_PATTERN
    header_pattern.pattern_fore_colour = xlwt.Style.colour_map['dark_blue']
    header_style.pattern = header_pattern
    
    # Écrire les en-têtes
    for col, header in enumerate(headers):
        ws.write(0, col, header, header_style)
    
    # Écrire les données
    for row_idx, row in enumerate(data, 1):
        if isinstance(row, dict):
            for col_idx, header in enumerate(headers):
                key = header_to_db_key(header)
                value = row.get(key, '')
                ws.write(row_idx, col_idx, format_value_for_export(value))
        else:
            for col_idx, value in enumerate(row):
                ws.write(row_idx, col_idx, format_value_for_export(value))
    
    # Sauvegarder dans un buffer
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)
    
    # Créer la réponse
    response = Response(
        output.getvalue(),
        mimetype='application/vnd.ms-excel',
        headers={
            'Content-Disposition': f'attachment; filename={filename}.xls',
        }
    )
    
    return response

def header_to_db_key(header):
    """
    Convertit un en-tête d'affichage en clé de base de données
    """
    mapping = {
        'Immatriculation': 'immatriculation',
        'Marque': 'marque',
        'Modèle': 'modele',
        'Année': 'annee',
        'Couleur': 'couleur',
        'Kilométrage': 'kilometrage',
        'Carburant': 'carburant',
        'Statut': 'statut',
        'Date acquisition': 'date_acquisition',
        'Nom': 'nom',
        'Prénom': 'prenom',
        'Numéro permis': 'numero_permis',
        'Date permis': 'date_permis',
        'Téléphone': 'telephone',
        'Email': 'email',
        'Type maintenance': 'type_maintenance',
        'Description': 'description',
        'Date maintenance': 'date_maintenance',
        'Coût (MAD)': 'cout',
        'Garage': 'garage',
        'Véhicule': 'immatriculation',
        'Conducteur': 'nom',
        'Date début': 'date_debut',
        'Date fin': 'date_fin',
        'Commentaire': 'commentaire'
    }
    return mapping.get(header, header.lower().replace(' ', '_'))

def format_value_for_export(value):
    """
    Formate une valeur pour l'export
    """
    if value is None:
        return ''
    elif isinstance(value, (int, float)):
        return value
    elif isinstance(value, str):
        return value.strip()
    else:
        return str(value)

def get_export_filename(base_name, export_type='csv'):
    """
    Génère un nom de fichier avec timestamp
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return f"{base_name}_{timestamp}"
