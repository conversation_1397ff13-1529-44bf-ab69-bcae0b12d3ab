@echo off
echo ================================================
echo    Configuration Apache pour GesParc Auto
echo ================================================
echo.

REM Chemin vers Apache
set APACHE_DIR=c:\Apache24
set GESPARC_DIR=c:\Apache24\htdocs\gesparc

REM Vérifier si Apache existe
if not exist "%APACHE_DIR%" (
    echo ERREUR: Apache non trouvé dans %APACHE_DIR%
    pause
    exit /b 1
)

echo 1. Sauvegarde de la configuration actuelle...
copy "%APACHE_DIR%\conf\httpd.conf" "%APACHE_DIR%\conf\httpd.conf.backup" >nul 2>&1

echo 2. Vérification des modules nécessaires...
findstr /C:"LoadModule rewrite_module" "%APACHE_DIR%\conf\httpd.conf" >nul
if errorlevel 1 (
    echo    - Activation de mod_rewrite
    echo LoadModule rewrite_module modules/mod_rewrite.so >> "%APACHE_DIR%\conf\httpd.conf"
)

findstr /C:"LoadModule proxy_module" "%APACHE_DIR%\conf\httpd.conf" >nul
if errorlevel 1 (
    echo    - Activation de mod_proxy
    echo LoadModule proxy_module modules/mod_proxy.so >> "%APACHE_DIR%\conf\httpd.conf"
)

findstr /C:"LoadModule proxy_http_module" "%APACHE_DIR%\conf\httpd.conf" >nul
if errorlevel 1 (
    echo    - Activation de mod_proxy_http
    echo LoadModule proxy_http_module modules/mod_proxy_http.so >> "%APACHE_DIR%\conf\httpd.conf"
)

echo 3. Ajout de la configuration GesParc...
findstr /C:"# GesParc Auto Configuration" "%APACHE_DIR%\conf\httpd.conf" >nul
if errorlevel 1 (
    echo. >> "%APACHE_DIR%\conf\httpd.conf"
    echo # GesParc Auto Configuration >> "%APACHE_DIR%\conf\httpd.conf"
    echo Include "%GESPARC_DIR%\gesparc-apache.conf" >> "%APACHE_DIR%\conf\httpd.conf"
    echo    - Configuration ajoutée
) else (
    echo    - Configuration déjà présente
)

echo 4. Vérification de la configuration...
"%APACHE_DIR%\bin\httpd.exe" -t
if errorlevel 1 (
    echo ERREUR: Configuration Apache invalide
    echo Restauration de la sauvegarde...
    copy "%APACHE_DIR%\conf\httpd.conf.backup" "%APACHE_DIR%\conf\httpd.conf" >nul
    pause
    exit /b 1
)

echo 5. Redémarrage d'Apache...
net stop Apache2.4 >nul 2>&1
net start Apache2.4 >nul 2>&1
if errorlevel 1 (
    echo ATTENTION: Impossible de redémarrer Apache automatiquement
    echo Veuillez redémarrer Apache manuellement
) else (
    echo    - Apache redémarré avec succès
)

echo.
echo ================================================
echo Configuration terminée avec succès !
echo.
echo URLs d'accès :
echo   - Via Apache: http://localhost/gesparc
echo   - Direct Flask: http://localhost:5001
echo.
echo Pour démarrer l'application Flask :
echo   cd %GESPARC_DIR%
echo   python gesparc_app.py
echo ================================================
pause
