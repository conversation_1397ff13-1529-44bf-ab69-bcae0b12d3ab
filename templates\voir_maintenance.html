{% extends "base.html" %}

{% block title %}Détails Maintenance - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-tools"></i> Détails de la Maintenance</h1>
            <div>
                <a href="{{ url_for('modifier_maintenance', id=maintenance.id) }}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Modifier
                </a>
                <a href="{{ url_for('maintenances') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Informations principales -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Informations de la maintenance
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Type :</strong></td>
                                <td>{{ maintenance.type_maintenance }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date prévue :</strong></td>
                                <td>{{ maintenance.date_maintenance }}</td>
                            </tr>
                            <tr>
                                <td><strong>Priorité :</strong></td>
                                <td>
                                    {% if maintenance.priorite == 'urgente' %}
                                        <span class="badge bg-danger">🔴 Urgente</span>
                                    {% elif maintenance.priorite == 'elevee' %}
                                        <span class="badge bg-warning">🟠 Élevée</span>
                                    {% elif maintenance.priorite == 'normale' %}
                                        <span class="badge bg-info">🟡 Normale</span>
                                    {% elif maintenance.priorite == 'faible' %}
                                        <span class="badge bg-success">🟢 Faible</span>
                                    {% else %}
                                        <span class="badge bg-secondary">🟡 Normale</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Statut :</strong></td>
                                <td>
                                    {% if maintenance.statut == 'planifiee' %}
                                        <span class="badge bg-warning text-dark">📅 Planifiée</span>
                                    {% elif maintenance.statut == 'en_cours' %}
                                        <span class="badge bg-info">⚙️ En cours</span>
                                    {% elif maintenance.statut == 'terminee' %}
                                        <span class="badge bg-success">✅ Terminée</span>
                                    {% elif maintenance.statut == 'reportee' %}
                                        <span class="badge bg-secondary">⏸️ Reportée</span>
                                    {% elif maintenance.statut == 'annulee' %}
                                        <span class="badge bg-danger">❌ Annulée</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Coût estimé :</strong></td>
                                <td>
                                    {% if maintenance.cout %}
                                        {{ "{:,.2f}".format(maintenance.cout).replace(',', ' ') }} MAD
                                    {% else %}
                                        <span class="text-muted">Non estimé</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Garage :</strong></td>
                                <td>{{ maintenance.garage or '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date création :</strong></td>
                                <td>{{ maintenance.date_creation }}</td>
                            </tr>
                            {% if maintenance.date_realisation %}
                            <tr>
                                <td><strong>Date réalisation :</strong></td>
                                <td>{{ maintenance.date_realisation }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                {% if maintenance.description %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6><i class="fas fa-comment"></i> Description</h6>
                        <div class="alert alert-light">
                            {{ maintenance.description }}
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if maintenance.notes_technicien %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6><i class="fas fa-sticky-note"></i> Notes du technicien</h6>
                        <div class="alert alert-info">
                            {{ maintenance.notes_technicien }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Informations véhicule -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-car"></i> Véhicule concerné
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>Immatriculation :</strong></td>
                        <td>{{ maintenance.immatriculation }}</td>
                    </tr>
                    <tr>
                        <td><strong>Marque :</strong></td>
                        <td>{{ maintenance.marque }}</td>
                    </tr>
                    <tr>
                        <td><strong>Modèle :</strong></td>
                        <td>{{ maintenance.modele }}</td>
                    </tr>
                    <tr>
                        <td><strong>Année :</strong></td>
                        <td>{{ maintenance.annee }}</td>
                    </tr>
                    <tr>
                        <td><strong>Kilométrage :</strong></td>
                        <td>{{ "{:,}".format(maintenance.kilometrage).replace(',', ' ') }} km</td>
                    </tr>
                </table>
                
                <div class="mt-3">
                    <a href="{{ url_for('voir_vehicule', id=maintenance.vehicule_id) }}" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> Voir le véhicule
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Actions rapides -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt"></i> Actions rapides
                </h5>
            </div>
            <div class="card-body">
                {% if maintenance.statut == 'planifiee' %}
                <form method="POST" action="{{ url_for('demarrer_maintenance', id=maintenance.id) }}" class="mb-2">
                    <button type="submit" class="btn btn-success w-100" 
                            onclick="return confirm('Démarrer cette maintenance ?')">
                        <i class="fas fa-play"></i> Démarrer la maintenance
                    </button>
                </form>
                {% elif maintenance.statut == 'en_cours' %}
                <form method="POST" action="{{ url_for('terminer_maintenance', id=maintenance.id) }}" class="mb-2">
                    <button type="submit" class="btn btn-success w-100" 
                            onclick="return confirm('Marquer cette maintenance comme terminée ?')">
                        <i class="fas fa-check"></i> Terminer la maintenance
                    </button>
                </form>
                {% elif maintenance.statut == 'terminee' %}
                <div class="alert alert-success text-center">
                    <i class="fas fa-check-circle"></i><br>
                    Maintenance terminée
                </div>
                {% endif %}
                
                <a href="{{ url_for('modifier_maintenance', id=maintenance.id) }}" 
                   class="btn btn-outline-primary w-100 mb-2">
                    <i class="fas fa-edit"></i> Modifier
                </a>
                
                <a href="{{ url_for('ajouter_maintenance') }}" 
                   class="btn btn-outline-secondary w-100">
                    <i class="fas fa-plus"></i> Nouvelle maintenance
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
